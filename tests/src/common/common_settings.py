"""
The environment settings for pytest
"""

from enum import Enum, auto

# #####>>>>>----- REQUIRED/IMPORTANT SETTINGS -----<<<<<#####

# Global timeout
SHORT_ASSERT_TIMEOUT = 1
SHORT_ASSERT_TIMEOUT_2 = 2
SHORT_ASSERT_TIMEOUT_3 = 3
ASSERT_TIMEOUT = 20
CLICK_TIME_OUT = 20
WAIT_CLICK_TIME_OUT = 30
WAIT_RENDER_TIME_OUT = 60
WAIT_PAGE_LOAD_TIME_OUT = 90
WAIT_CREATE_WS_TIME_OUT_200 = 200
WAIT_CREATE_WS_TIME_OUT = 300
WAIT_RESUME_WS_TIME_OUT = 600


# Backup and Restore
DEFAULT_BACKUP_NAME = "e2e-backup-test"
DEFAULT_BACKUP_TIMEOUT = 18000
DEFAULT_BACKUP_MINI_SIZE = "1KB"
DEFAULT_RESTORE_TIMEOUT = 18000
BACKUP_CHECK_DELAY = 5
RESTORE_CHECK_DELAY = 5

# Clear and Rebuild
DEFAULT_CLEAR_TIMEOUT = 1800
DEFAULT_REBUILD_TIMEOUT = 3600

# Workspace settings
DEFAULT_WORKSPACE_NAME = "QE_E2E_WS"



