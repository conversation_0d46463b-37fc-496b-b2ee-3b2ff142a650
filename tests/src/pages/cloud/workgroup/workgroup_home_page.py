import os
import logging
import time
import json
import random
import pytest
#
#
from os import listdir

from selenium.webdriver import Keys
from utils.data_util.data_resolver import read_test_data
from base.cloud_basepage import CloudBasePage
from locators.cloud.cloud_locators import CloudHomeLocators
from locators.cloud.workgroup_locators import WorkgroupLocators
from common import common_settings
from seleniumbase.common import decorators
from selenium.webdriver.common.action_chains import ActionChains

from locators.cloud.workgroup_locators import GAPHomeLocators

LOGGER = logging.getLogger(__name__)

from utils.data_util.login import LoginUtils

class WorkgroupHomePage(CloudBasePage):
  """Workgroup Home page objects"""
  read_data = read_test_data(file="tgcloud_test_data.json")
  login_test_data = read_data.get("login")


  def __init__(self, sb):
    self.sb = sb
    self.actions = ActionChains(self.sb.driver)
    test_data = read_test_data(file="tgcloud_test_data.json")
    self.default_WG_name = test_data.get("default_workgroup_name")
    LOGGER.info("read default_WG_name:" + self.default_WG_name)
    # self.default_WS_name = test_data.get("default_workspace_name")

  def cloud_v4_login(self):
    loginUtil = LoginUtils(self.sb)
    loginUtil.login_cloud()

  def cloud_v4_login_with_workspace_role(self):
    loginUtil = LoginUtils(self.sb)
    read_data = read_test_data(file="tgcloud_test_data.json")
    login_test_data = read_data.get("login")
    workspace_login_test_data = login_test_data.get("workspace_role_login")
    LOGGER.info("workspace_login_test_data: " + str(workspace_login_test_data))
    org = workspace_login_test_data[0]["org"]
    user = workspace_login_test_data[0]["user"]
    password = workspace_login_test_data[0]["password"]
    LOGGER.info("cloud_v4_login_with_workspace_role: " + org + ", " + user + ", " + password)
    loginUtil.login_cloud(org=org, username=user, password=password)

  def create_RW_workgroup(self):
    LOGGER.info("before create workgroup, check if WG existed ")
    LOGGER.info("read default_WG_name:" + self.default_WG_name)
    self.check_workgroup_and_delete_if_existed(work_group_name="QE_E2E_WG", workspace_name="QE_E2E_WS")
    self.create_new_workgroup("QE_E2E_WG")
    self.create_new_workspace("QE_E2E_WS")

  def get_tgcloud_test_data_value(self, key=""):
    test_data = read_test_data(file="tgcloud_test_data.json")
    if key == "":
        raise Exception("input key is null")
    value = test_data.get(key)
    LOGGER.info("input key:" + key + ", get value:" + str(value))
    return value

  def create_RO_workspace(self, rw_workspace_name="QE_E2E_WS"):
    LOGGER.info("before create RO WS, check if WG existed ")
    # turn to the workgroup
    self.turn_to_workgroup(need_wait_create_button=True)
    if rw_workspace_name != "":
        self.sb.assert_text(rw_workspace_name, timeout=common_settings.WAIT_CLICK_TIME_OUT)
    if self.sb.is_element_present(WorkgroupLocators.common_span_button.format(rw_workspace_name+"_RO")):
        LOGGER.info("RO is created, assert if active")
        #assert connect button is clickable
        if self.sb.is_element_present(WorkgroupLocators.horizon_progressbar):
            LOGGER.info("RO workspace is not ready")
            self.wait_horizon_progressbar(not_visible_timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
            if self.sb.is_element_present(WorkgroupLocators.horizon_progressbar):
                self.sb.attach_allure_screenshot("RO workspace not ready")
                raise Exception("RO workspace is not ready")
            else:
                LOGGER.info("RO workspace is ready, click connect button")
                self.sb.find_elements(WorkgroupLocators.common_span_button.format("Connect"))[1].click()
                self.sb.assert_text("Connect from API", timeout=common_settings.WAIT_CLICK_TIME_OUT)
                self.sb.attach_allure_screenshot("RO connect button clickable done")
                return True
        else:
            LOGGER.info("RO workspace is ready, click connect button")
            # if have congratulation pop windows, click
            if self.sb.is_element_present(WorkgroupLocators.common_button.format("Skip")):
                LOGGER.info("close congratulation pop_windows")
                self.wait_and_click(WorkgroupLocators.common_button.format("Skip"))
            self.sb.find_elements(WorkgroupLocators.common_span_button.format("Connect"))[1].click()
            self.sb.assert_text("Connect from API", timeout=common_settings.WAIT_CLICK_TIME_OUT)
            self.sb.attach_allure_screenshot("RO connect button clickable done")
            return True
    self.wait_and_click(WorkgroupLocators.create_WS_button)
    self.create_new_workspace(workspace_name=rw_workspace_name, is_RW_WS=False)

  def pause_RW_workspace(self, rw_workspace_name="QE_E2E_WS"):
    LOGGER.info("pause RW workspace")
    # turn to the workgroup
    self.turn_to_workgroup(need_wait_create_button=True)
    self.double_check_disable_Network_Access()
    self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
    # just for rerun job check
    if self.sb.is_element_present(WorkgroupLocators.common_div_button.format("Resume")):
        LOGGER.info("pause_RW_workspace already succeed, return")
        self.sb.attach_allure_screenshot("pause_RW_workspace already succeed, return")
        return True
    # compare the pause button with different scenarios by enable_auto_start
    if self.sb.is_element_present(WorkgroupLocators.common_div_button.format("Pause")):
        LOGGER.info("prepare to click pause button")
        self.wait_and_click(WorkgroupLocators.common_div_button.format("Pause"))
    elif self.sb.is_element_present(WorkgroupLocators.common_div_button.format("Stop")):
        LOGGER.info("prepare to click Stop button")
        self.wait_and_click(WorkgroupLocators.common_div_button.format("Stop"))
    else:
        LOGGER.info("didn't see pause/stop button")
    self.sb.attach_allure_screenshot("after click pause/stop button")
    if self.sb.is_element_present(WorkgroupLocators.common_button.format("Confirm")):
        LOGGER.info("prepare to click confirm button")
        self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    else:
        LOGGER.info("didn't see confirm button")
    self.sb.attach_allure_screenshot("after click confirm")
    self.sb.sleep(2)
    LOGGER.info("finish operate, wait WS")
    self.sb.attach_allure_screenshot("wait WS")
    self.check_operator_button(css=WorkgroupLocators.common_div_button.format("Resume"), timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)
    # self.sb.hover(WorkgroupLocators.WS_status_icon)
    # self.sb.assert_text("Idle")


  def resume_RW_workspace(self, rw_workspace_name="QE_E2E_WS"):
    LOGGER.info("resume RW workspace")
    # turn to the workgroup
    self.turn_to_workgroup(need_wait_create_button=True)
    self.wait_WS_operate_circle_progressbar(visible_timeout=common_settings.CLICK_TIME_OUT, not_visible_timeout=common_settings.WAIT_CREATE_WS_TIME_OUT_200)
    self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
    # just for rerun job check
    if self.sb.is_element_present(WorkgroupLocators.common_div_button.format("Stop")):
        LOGGER.info("already start WS")
        self.sb.attach_allure_screenshot("already start WS")
        self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
        try:
            # double check and assert the job
            self.sb.wait_for_element_clickable(WorkgroupLocators.common_span_button.format("Connect"), timeout=common_settings.WAIT_CLICK_TIME_OUT)
            self.sb.find_elements(WorkgroupLocators.common_span_button.format("Connect"))[0].click()
            self.sb.assert_text("Connect from API", timeout=common_settings.ASSERT_TIMEOUT)
            self.sb.attach_allure_screenshot("second time to begin to restart_RW_workspace")
            LOGGER.info("second time resume_RW_workspace succeed")
            return True
        except Exception as e:
            LOGGER.info("second time resume_RW_workspace failed: " + str(e))
    else:
        self.sb.attach_allure_screenshot("first time to begin to restart_RW_workspace")
        self.wait_and_click(WorkgroupLocators.common_div_button.format("Resume"))
        self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
        # wait WS active
        LOGGER.info("first time to wait WS active")
        # the stop button will show quickly
        # self.check_operator_button(css=WorkgroupLocators.common_div_button.format("Stop"), timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)
    self.sb.attach_allure_screenshot("after click confirm")
    self.sb.sleep(2)
    LOGGER.info("finish operate, wait WS")
    self.sb.attach_allure_screenshot("wait WS")
    self.wait_WS_operate_circle_progressbar(not_visible_timeout=common_settings.WAIT_CREATE_WS_TIME_OUT_200)
    # double check and assert the job
    self.sb.wait_for_element_clickable(WorkgroupLocators.common_span_button.format("Connect"), timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.sb.find_elements(WorkgroupLocators.common_span_button.format("Connect"))[0].click()
    self.sb.assert_text("Connect from API", timeout=common_settings.ASSERT_TIMEOUT)
    LOGGER.info("resume_RW_workspace succeed")
    self.sb.attach_allure_screenshot("RW restart succeed, connect button clickable done")

  def stop_RW_workspace(self):
    LOGGER.info("stop workspace")
    # turn to the workgroup
    self.turn_to_workgroup(need_wait_create_button=True)
    self.sb.sleep(common_settings.SHORT_ASSERT_TIMEOUT_2)
    self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
    # just for rerun job check
    if self.sb.is_element_present(WorkgroupLocators.common_div_button.format("Resume")):
        LOGGER.info("stop_RW_workspace already succeed, return")
        self.sb.attach_allure_screenshot("stop_RW_workspace already succeed, return")
        return True
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Stop"))
    self.sb.sleep(common_settings.SHORT_ASSERT_TIMEOUT_2)
    self.sb.attach_allure_screenshot("clicked stop WS")
    if self.sb.is_element_present(WorkgroupLocators.common_button.format("Confirm")):
        LOGGER.info("prepare to click confirm button")
        self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    else:
        LOGGER.info("didn't see confirm button")
    # wait and assert status is Stopped
    self.sb.attach_allure_screenshot("after click confirm")
    LOGGER.info("wait to stop")
    self.sb.sleep(1)
    LOGGER.info("finish operate, wait WS")
    self.sb.attach_allure_screenshot("wait WS")
    self.check_operator_button(css=WorkgroupLocators.common_div_button.format("Resume"), timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)


  def terminate_RW_workspace(self):
    LOGGER.info("terminate RW workspace")
    # turn to the workgroup
    self.turn_to_workgroup(need_wait_create_button=True)
    self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Terminate"))
    checkbox = self.sb.find_elements(WorkgroupLocators.checkbox_button)
    LOGGER.info("find delete checkbox: " + str(len(checkbox)))
    for j in range(len(checkbox)):
        LOGGER.info("click " + str(j) + " checkbox")
        self.sb.find_elements(WorkgroupLocators.checkbox_button)[j].click()

    self.wait_and_click(WorkgroupLocators.comfirm_delete_WS_button)
    self.wait_WS_operate_circle_progressbar(not_visible_timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
    LOGGER.info("terminate_RW_workspace succeed")
    self.sb.attach_allure_screenshot("terminate_RW_workspace done")


  def terminate_RO_workspace(self):
    LOGGER.info("terminate RO workspace")
    # turn to the workgroup
    self.turn_to_workgroup(need_wait_create_button=True)
    self.sb.find_elements(WorkgroupLocators.more_button)[2].click()
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Terminate"))
    checkbox = self.sb.find_elements(WorkgroupLocators.checkbox_button)
    LOGGER.info("find delete checkbox: " + str(len(checkbox)))
    for j in range(len(checkbox)):
        LOGGER.info("click " + str(j) + " checkbox")
        self.sb.find_elements(WorkgroupLocators.checkbox_button)[j].click()

    self.wait_and_click(WorkgroupLocators.comfirm_delete_WS_button)
    self.wait_WS_operate_circle_progressbar(not_visible_timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
    self.sb.attach_allure_screenshot("terminate_RO_workspace done")

  def terminate_workspace_DB(self):
    LOGGER.info("terminate workspace DB")
    # turn to the workgroup
    self.turn_to_workgroup(need_wait_create_button=True)
    if self.sb.is_element_visible(WorkgroupLocators.delete_WG):
        self.sb.attach_allure_screenshot("already deleted DB")
        return True
    self.sb.find_elements(WorkgroupLocators.more_button)[0].click()
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Terminate"))
    self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    self.sb.attach_allure_screenshot("terminate_workspace DB done")
    self.sb.assert_text("Delete Workgroup", timeout=common_settings.WAIT_CLICK_TIME_OUT)

  def terminate_workgroup(self, rw_workgroup_name="QE_E2E_WG"):
    LOGGER.info("terminate workgroup")
    # turn to the workgroup
    self.turn_to_workgroup()
    if self.default_WG_name != "":
        rw_workgroup_name = self.default_WG_name
    # delete WG
    LOGGER.info("terminate workgroup:" + rw_workgroup_name)
    self.wait_and_click(WorkgroupLocators.delete_WG)
    self.wait_and_click(WorkgroupLocators.delete_WG_OK)
    self.sb.sleep(3)
    self.sb.attach_allure_screenshot("delete WG done")
    # switch other menu to refresh WG
    self.wait_and_click(WorkgroupLocators.load_data)
    self.wait_and_click(WorkgroupLocators.workgroup_line)
    # Double assert
    if self.sb.is_element_present(WorkgroupLocators.workgroup_name.format(rw_workgroup_name)):
        LOGGER.info("workgroup:" + rw_workgroup_name + " still existed, retry")
        self.sb.attach_allure_screenshot("terminate_workgroup failed, WG still existed")
        raise Exception("terminate_workgroup failed, WG still existed")


  @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
  def turn_to_workgroup(self, work_group_name="QE_E2E_WG", need_wait_create_button=False):
    if self.default_WG_name != "":
        work_group_name = self.default_WG_name
    LOGGER.info("begin to check workgroup:" + WorkgroupLocators.workgroup_name.format(work_group_name))
    self.wait_and_click(WorkgroupLocators.workgroup_line)
    self.wait_top_blue_circle_progressbar(visible_timeout=common_settings.WAIT_RENDER_TIME_OUT)
    if self.sb.is_element_present(WorkgroupLocators.workgroup_name.format(work_group_name)):
        LOGGER.info("begin to redirect to workgroup:" + work_group_name)
        self.wait_and_click(WorkgroupLocators.workgroup_name.format(work_group_name))
        if need_wait_create_button:
            self.sb.wait_for_element_clickable(WorkgroupLocators.create_WS_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    else:
        self.sb.attach_allure_screenshot("didn't find workgroup")
        self.sb.refresh()
        raise Exception("didn't find workgroup:" + work_group_name)

  @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
  def into_Monitor_logs(self):
    try:
        self.sb.wait_for_element_clickable(GAPHomeLocators.monitor, timeout=common_settings.WAIT_CLICK_TIME_OUT)
        if not self.sb.is_element_visible(GAPHomeLocators.logs):
            self.wait_and_click(GAPHomeLocators.monitor)
        self.wait_and_click(GAPHomeLocators.logs)
        self.sb.wait_for_ready_state_complete()
    except Exception as e:
        # don't refresh, will cause logout GAP
        self.sb.attach_allure_screenshot("open logs menu failed screenshot")
        raise Exception(e)


  @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=10)
  def check_workgroup_and_delete_if_existed(self, work_group_name="", workspace_name=""):
    if self.default_WG_name != "":
        work_group_name = self.default_WG_name
    LOGGER.info("begin to check workgroup:" + WorkgroupLocators.workgroup_name.format(work_group_name))
    self.wait_and_click(WorkgroupLocators.workgroup_line)
    self.wait_top_blue_circle_progressbar()
    if self.sb.is_element_present(WorkgroupLocators.workgroup_name.format(work_group_name)):
        LOGGER.info("begin to delete workgroup:" + work_group_name)
        self.wait_and_click(WorkgroupLocators.workgroup_name.format(work_group_name))
        try:
          # delete all WS
          if workspace_name != "":
            self.sb.assert_text(workspace_name, timeout=common_settings.WAIT_CLICK_TIME_OUT)
          elements = self.sb.find_elements(WorkgroupLocators.more_button)
          for i in reversed(range(len(elements))):
                LOGGER.info("operate on " + str(i) + " more icon")
                self.sb.find_elements(WorkgroupLocators.more_button)[i].click()
                self.wait_and_click(WorkgroupLocators.terminate_button)
                self.sb.wait_for_element_clickable(WorkgroupLocators.common_button.format("Cancel"))
                checkbox = self.sb.find_elements(WorkgroupLocators.checkbox_button)
                LOGGER.info("find checkbox: " + str(checkbox))
                for j in range(len(checkbox)):
                    LOGGER.info("click " + str(j) + " checkbox")
                    self.sb.find_elements(WorkgroupLocators.checkbox_button)[j].click()

                self.wait_and_click(WorkgroupLocators.comfirm_delete_WS_button)
                self.sb.sleep(7)
        except Exception as e:
          LOGGER.info("delete all WS exception:" + str(e))
        # delete WG
        self.wait_and_click(WorkgroupLocators.delete_WG)
        self.wait_and_click(WorkgroupLocators.delete_WG_OK)
        self.sb.sleep(3)
        self.sb.attach_allure_screenshot("delete WG done")
        # switch other menu to refresh WG
        self.wait_and_click(WorkgroupLocators.load_data)
        self.wait_and_click(WorkgroupLocators.workgroup_line)
    else:
        LOGGER.info("workgroup:" + work_group_name + " not existed")
        self.sb.attach_allure_screenshot("WG not existed")

    if self.sb.is_element_present(WorkgroupLocators.workgroup_name.format(work_group_name)):
        LOGGER.info("workgroup:" + work_group_name + " still existed, retry")
        self.sb.refresh()
        raise Exception("workgroup:" + work_group_name + " still existed, retry")

  def create_new_workgroup(self, workgroup_name=""):
    if self.default_WG_name != "":
        workgroup_name = self.default_WG_name
    LOGGER.info("begin to create new workgroup:" + workgroup_name)
    self.wait_and_click(WorkgroupLocators.create_workgroup)
    self.wait_top_blue_circle_progressbar(visible_timeout=common_settings.SHORT_ASSERT_TIMEOUT)
    self.sb.type(WorkgroupLocators.input_WG_name, workgroup_name)
    self.wait_and_click(WorkgroupLocators.default_cp_aws)
    self.sb.attach_allure_screenshot("create WG page")
    self.wait_and_click(WorkgroupLocators.next_button)

  def create_new_workspace(self, workspace_name="", is_RW_WS=True):
    LOGGER.info("begin to create new workspace:" + workspace_name)
    # select WS size
    if is_RW_WS:
        self.sb.type(WorkgroupLocators.input_WS_name, workspace_name)
        # type TG version
        tg_version = self.get_tgcloud_test_data_value("tg_version")
        if tg_version != "":
            self.sb.type(WorkgroupLocators.input_TG_version, tg_version)
        else:
            LOGGER.info("use default TG version")
        # select WS details
        self.wait_and_click(WorkgroupLocators.edit_WS_detail)
        self.sb.find_element(WorkgroupLocators.select_WS_size).click()
        self.wait_and_click(WorkgroupLocators.select_TG_size)
        self.sb.attach_allure_screenshot("edit WS size")
        self.wait_and_click(WorkgroupLocators.save_button)
        LOGGER.info("create RW workspace:" + workspace_name)
        self.sb.type(WorkgroupLocators.input_RW_DB_name, workspace_name)
    else:
        self.sb.type(WorkgroupLocators.input_WS_name, workspace_name+"_RO")
        LOGGER.info("create RO workspace:" + workspace_name)
        # select WS details
        self.wait_and_click(WorkgroupLocators.edit_WS_detail)
        self.sb.find_element(WorkgroupLocators.select_WS_size).click()
        self.wait_and_click(WorkgroupLocators.select_TG_size)
        self.sb.attach_allure_screenshot("edit WS size")
        self.wait_and_click(WorkgroupLocators.save_button)
        # select RW db
        self.sb.find_elements(WorkgroupLocators.select_DB_radio)[1].click()
        self.wait_and_click(WorkgroupLocators.select_DB_click)
        self.wait_and_click(WorkgroupLocators.select_DB_name.format(workspace_name))

    self.wait_and_click(WorkgroupLocators.create_button)
    self.sb.wait_for_element_not_visible(WorkgroupLocators.create_button, timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.sb.attach_allure_screenshot("begin to create WS")
    # # wait_horizon_progressbar not work well
    LOGGER.info("wait_horizon_progressbar: not_visible_timeout=" + str(common_settings.WAIT_CREATE_WS_TIME_OUT))
    self.wait_horizon_progressbar(not_visible_timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
    LOGGER.info("prepare to check_congratuations_pop_windows ")
    self.check_congratuations_pop_windows(timeout=common_settings.WAIT_CREATE_WS_TIME_OUT, is_RW_WS=is_RW_WS)
    # not stability to pop windows
    # self.sb.assert_text("Congratulations!", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.sb.attach_allure_screenshot("create WS done")

  def check_congratuations_pop_windows(self, timeout=300, is_RW_WS=True):
    LOGGER.info("check_congratuations_pop_windows timeout: " + str(timeout))
    create_success = False
    play_fireworks = False
    for i in range(timeout):
        # double check and assert the job
        LOGGER.info("wait_pop_windows and assert WS is active")
        if self.sb.is_element_present(WorkgroupLocators.common_button.format("Skip")):
            LOGGER.info("close congratulation pop_windows")
            self.sb.attach_allure_screenshot("play fireworks screenshot")
            self.wait_and_click(WorkgroupLocators.common_button.format("Skip"))
            play_fireworks = True
        if is_RW_WS:
            try:
                if not create_success:
                    self.sb.find_elements(WorkgroupLocators.common_span_button.format("Connect"))[0].click()
                    self.sb.assert_text("Connect from API", timeout=common_settings.SHORT_ASSERT_TIMEOUT_2)
                    self.sb.attach_allure_screenshot("RW connect button clickable done")
                    LOGGER.info("RW connect button clickable done, break")
                    create_success = True
            except Exception as e:
                LOGGER.info("wait_RW_pop_windows exception:" + str(e))
        else:
            try:
                if not create_success:
                    self.sb.find_elements(WorkgroupLocators.common_span_button.format("Connect"))[1].click()
                    self.sb.assert_text("Connect from API", timeout=common_settings.SHORT_ASSERT_TIMEOUT_2)
                    self.sb.attach_allure_screenshot("RO connect button clickable done")
                    LOGGER.info("RO connect button clickable done")
                    create_success = True
            except Exception as e:
                LOGGER.info("wait_RO_pop_windows exception:" + str(e))
        if create_success and play_fireworks:
            LOGGER.info("final create WS succeed")
            break
        else:
            LOGGER.info("create_success:" + str(create_success) + ", play_fireworks:" + str(play_fireworks))
    if create_success and play_fireworks:
        LOGGER.info("finally play fireworks and assert WS succeed")
    else:
        LOGGER.info("finally play fireworks and assert WS is not active")
        self.sb.attach_allure_screenshot("finally create WS failed")
        raise Exception("finally wait_pop_windows and assert WS is not active")

  def check_operator_button(self, css="", timeout=300, is_RW_WS=True):
    LOGGER.info("check WS operate button: "+css+" , timeout: " + str(timeout))
    operate_success = False
    for i in range(timeout):
        # double check and assert the job
        LOGGER.info("check_operator_button")
        self.sb.sleep(common_settings.SHORT_ASSERT_TIMEOUT)
        if is_RW_WS:
            self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
            if self.sb.is_element_present(css):
                LOGGER.info("pause/stop/resume_RW_workspace succeed, break")
                self.sb.attach_allure_screenshot("pause/stop/resume_RW_workspace succeed")
                operate_success = True
                break
            else:
                LOGGER.info("pause/stop/resume_RW_workspace not ready")
                self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
        else:
            self.sb.find_elements(WorkgroupLocators.more_button)[2].click()
            if self.sb.is_element_present(css):
                LOGGER.info("pause/stop/resume_RO_workspace succeed, break")
                self.sb.attach_allure_screenshot("pause/stop/resume_RO_workspace succeed")
                operate_success = True
                break
            else:
                LOGGER.info("pause/stop/resume_RO_workspace not ready")
                self.sb.find_elements(WorkgroupLocators.more_button)[2].click()
    # finally result
    if operate_success:
        LOGGER.info("finally operate WS succeed")
    else:
        LOGGER.info("finally operate WS failed")
        self.sb.attach_allure_screenshot("finally operate WS failed")
        raise Exception("finally operate WS failed")


  def wait_and_click(self, css):
    LOGGER.info("wait and click: " + css)
    self.sb.wait_for_element_clickable(css, timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.sb.click(css, timeout=common_settings.WAIT_CLICK_TIME_OUT)

  def wait_horizon_progressbar(self, visible_timeout=common_settings.WAIT_CLICK_TIME_OUT, not_visible_timeout=common_settings.WAIT_RENDER_TIME_OUT):
    try:
        self.sb.wait_for_element_visible(WorkgroupLocators.horizon_progressbar, timeout=visible_timeout)
        LOGGER.info("horizon_progressbar visible")
        self.sb.sleep(3)
        self.sb.wait_for_element_not_visible(WorkgroupLocators.horizon_progressbar, timeout=not_visible_timeout)
        LOGGER.info("horizon_progressbar NOT visible")
    except Exception as e:
        LOGGER.info("wait_horizon_progressbar exception:" + str(e))
        self.sb.attach_allure_screenshot("wait_horizon_progressbar exception")

  def wait_WS_operate_circle_progressbar(self, visible_timeout=common_settings.WAIT_CLICK_TIME_OUT, not_visible_timeout=common_settings.WAIT_RENDER_TIME_OUT):
    try:
        self.sb.wait_for_element_visible(WorkgroupLocators.WS_operate_circle_progressbar, timeout=visible_timeout)
        LOGGER.info("WS_operate_circle_progressbar visible")
        self.sb.sleep(3)
        self.sb.wait_for_element_not_visible(WorkgroupLocators.WS_operate_circle_progressbar, timeout=not_visible_timeout)
        LOGGER.info("WS_operate_circle_progressbar NOT visible")
    except Exception as e:
        LOGGER.info("wait_WS_operate_circle_progressbar exception:" + str(e))
        self.sb.attach_allure_screenshot("wait_WS_operate_circle_progressbar exception")


  def wait_top_blue_circle_progressbar(self, visible_timeout=common_settings.WAIT_CLICK_TIME_OUT, not_visible_timeout=common_settings.WAIT_RENDER_TIME_OUT):
    try:
        self.sb.wait_for_element_visible(WorkgroupLocators.top_blue_circle_progressbar, timeout=visible_timeout)
        LOGGER.info("top_blue_circle_progressbar visible")
        self.sb.sleep(1)
        self.sb.wait_for_element_not_visible(WorkgroupLocators.top_blue_circle_progressbar, timeout=not_visible_timeout)
        LOGGER.info("top_blue_circle_progressbar NOT visible")
    except Exception as e:
        LOGGER.info("wait top_blue_circle_progressbar exception:" + str(e))
        self.sb.attach_allure_screenshot("wait top_blue_circle_progressbar exception")

  def wait_backup_list_render(self, visible_timeout=common_settings.WAIT_CLICK_TIME_OUT, not_visible_timeout=common_settings.WAIT_RENDER_TIME_OUT):
    try:
        self.sb.wait_for_element_visible(WorkgroupLocators.backup_list, timeout=visible_timeout)
        LOGGER.info("wait_backup_list_render visible")
        self.sb.wait_for_element_not_visible(WorkgroupLocators.backup_list, timeout=not_visible_timeout)
        LOGGER.info("wait_backup_list_render NOT visible")
    except Exception as e:
        LOGGER.info("wait wait_backup_list_render exception:" + str(e))
        self.sb.attach_allure_screenshot("wait wait_backup_list_render exception")

  def wait_upload_file_horizon_progressbar(self, visible_timeout=common_settings.WAIT_CLICK_TIME_OUT, not_visible_timeout=common_settings.WAIT_RENDER_TIME_OUT):
    try:
        self.sb.wait_for_element_visible(WorkgroupLocators.upload_file_progressbar, timeout=visible_timeout)
        LOGGER.info("upload_file_progressbar visible")
        self.sb.sleep(1)
        self.sb.wait_for_element_not_visible(WorkgroupLocators.upload_file_progressbar, timeout=not_visible_timeout)
        LOGGER.info("upload_file_progressbar NOT visible")
    except Exception as e:
        LOGGER.info("wait top_blue_circle_progressbar exception:" + str(e))
        self.sb.attach_allure_screenshot("wait top_blue_circle_progressbar exception")

  def connect_tools(self, tools_name="", need_wait_progressbar=True, is_suites=False):
    # connect RW design schema
    self.sb.wait_for_element_clickable(WorkgroupLocators.create_WS_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.wait_and_click(WorkgroupLocators.common_span_button.format("Connect"))
    if is_suites:
        self.wait_and_click(WorkgroupLocators.common_a_button.format(tools_name))
    else:
        self.wait_and_click(WorkgroupLocators.common_div_button.format(tools_name))
    if need_wait_progressbar:
        self.wait_top_blue_circle_progressbar(visible_timeout=common_settings.CLICK_TIME_OUT)


  def connect_suites(self, tools_name=""):
    # connect RW design schema
    self.sb.wait_for_element_clickable(WorkgroupLocators.create_WS_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.wait_and_click(WorkgroupLocators.addons_GAP_button)
    self.sb.attach_allure_screenshot("wait to click add-ons suites")
    self.wait_and_click(WorkgroupLocators.common_a_button.format(tools_name))


  def close_tips_panel(self):
      try:
          LOGGER.info("Start close tips panel if visible")
          if self.sb.is_element_present(CloudHomeLocators.tips_button):
              LOGGER.info("closed tips panel successfully")
              self.sb.click(CloudHomeLocators.tips_button)
          if self.sb.is_element_present(CloudHomeLocators.guide_button):
              LOGGER.info("closed guide panel successfully")
              self.sb.click(CloudHomeLocators.guide_button)
      except Exception as e:
          LOGGER.info("close tips panel failed: " + str(e))
          self.sb.attach_allure_screenshot("close tips panel failed")


  def connect_design_schema(self, graph_name=""):
    self.turn_to_workgroup()
    # connect RW design schema
    self.connect_tools("Design Schema")
    self.sb.assert_text("Create Vertex", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.close_tips_panel()
    # create a vertex and save
    self.wait_and_click(WorkgroupLocators.create_new_vertex_button)
    self.close_tips_panel()
    self.wait_and_click(WorkgroupLocators.select_id_as_attribute)
    self.wait_and_click(WorkgroupLocators.add_attribute_button)
    self.wait_and_click(WorkgroupLocators.add_attribute_button)
    self.wait_and_click(WorkgroupLocators.add_attribute_button)
    # check delete the last attribute, there is a bug here
    elements = self.sb.find_elements(WorkgroupLocators.attribute_list)
    for i in reversed(range(len(elements))):
        LOGGER.info("delete the " + str(i) + " attribute")
        self.sb.find_elements(WorkgroupLocators.attribute_list)[i].click()
        self.wait_and_click(WorkgroupLocators.delete_attribute_button)
        self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
        self.sb.attach_allure_screenshot("delete the last attribute screenshot")
        break
    elements = self.sb.find_elements(WorkgroupLocators.attribute_list)
    # delete successfully
    if len(elements) != 4:
        self.sb.attach_allure_screenshot("delete the last attribute failed screenshot")
        raise Exception("can't delete the last attribute")

    self.sb.sleep(common_settings.SHORT_ASSERT_TIMEOUT_3)
    self.wait_and_click(WorkgroupLocators.graph_select_button)
    self.click_center_position()
    self.sb.sleep(common_settings.SHORT_ASSERT_TIMEOUT_3)
    self.wait_and_click(WorkgroupLocators.save_schema_button)
    self.sb.attach_allure_screenshot("click save button successfully screenshot")
    # disable assert tmply
    # self.sb.wait_for_text_not_visible(WorkgroupLocators.assert_save_schema_text, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.sb.attach_allure_screenshot("save schema successfully screenshot")

  def click_center_position(self):
    try:
        width = self.sb.driver.get_window_size()['width']
        height = self.sb.driver.get_window_size()['height']

        center_x = width // 3
        center_y = height // 2

        self.actions.move_by_offset(center_x, center_y).click().perform()
        self.sb.attach_allure_screenshot("click center position successfully screenshot")
    except Exception as e:
        LOGGER.info("click_center_position exception:" + str(e))

  def delete_graph(self, graph_name="test_"):
    self.wait_and_click(WorkgroupLocators.graph_select)



  def connect_load_data(self, graph_name="test_"):
    self.turn_to_workgroup()
    self.connect_tools("Load Data")
    # create a graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)
    # upload local files
    filePath = "./data/localFiles/"
    LOGGER.info(os.listdir(filePath))
    for f in listdir(filePath):
        # clear the input value
        self.sb.execute_script("document.querySelector('input[accept=\".csv,.tsv,.json,.jsonl\"]').value = ''")
        path = os.path.join(filePath, f)
        # skip directories or files that have already been uploaded
        if os.path.isdir(path) or self.sb.is_element_present(f'//div[contains(text(),"{f}")]'):
            continue
        LOGGER.info("file path:" + path)
        self.sb.choose_file('input[accept=".csv,.tsv,.json,.jsonl"]', path)
        # improve the success rate
        self.wait_top_blue_circle_progressbar(visible_timeout=5)
        self.sb.attach_allure_screenshot("upload successfully screenshot")
    self.sb.attach_allure_screenshot("uploaded successfully screenshot")
    # config files
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    self.wait_top_blue_circle_progressbar(visible_timeout=5)
    self.sb.attach_allure_screenshot("config file successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # config map
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.attach_allure_screenshot("config map successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # confirm
    self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    self.check_save_schema_job(timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
    if self.sb.is_element_visible(WorkgroupLocators.common_button.format("Schema Designer")):
        LOGGER.info("still in confirm page, raise exception")
        raise Exception("still in confirm page")
    self.wait_top_blue_circle_progressbar()
    self.sb.assert_text("FINISHED", timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.sb.attach_allure_screenshot("load successfully screenshot")

  def check_save_schema_job(self, timeout=60):
      LOGGER.info("prepare to check confirm status, timeout=" + str(timeout))
      for i in range(timeout):
          self.sb.sleep(1)
          try:
              elements = self.sb.find_elements(WorkgroupLocators.toast_window)
              LOGGER.info("find elements:" + str(len(elements)))
              if len(elements) > 0:
                  for i in range(len(elements)):
                      LOGGER.info(i)
                      LOGGER.info("toast:" + elements[i].text)
          except Exception as e:
              LOGGER.info("toast exception: " + str(e))
          if self.sb.is_element_visible(WorkgroupLocators.common_button.format("Schema Designer")):
              LOGGER.info("still in confirm page, continue to wait")
          else:
              LOGGER.info("not in confirm page, break")
              self.sb.attach_allure_screenshot("confirm successfully screenshot")
              break



  def connect_gsql_editor_load_data(self, graph_name=""):
    self.turn_to_workgroup()
    self.connect_tools("Query Editor")
    self.close_tips_panel()
    self.sb.assert_text("Run", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    # create a file
    self.wait_and_click(WorkgroupLocators.create_new_file)
    self.wait_and_click(WorkgroupLocators.common_button.format("GSQL Query"))
    self.wait_and_click(WorkgroupLocators.input_cmd)
    self.sb.attach_allure_screenshot("create a file successfully screenshot")
    # creat a schema
    cmd = 'CREATE GRAPH AntiFraud () +\
        CREATE SCHEMA_CHANGE JOB change_schema_of_AntiFraud FOR GRAPH AntiFraud { +\
          Add VERTEX Individual(PRIMARY_ID id UINT, name STRING, isBlocked BOOL, createTime DATETIME, gender STRING, dob DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Company(PRIMARY_ID id UINT, name STRING, isBlocked BOOL, createTime DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX BeneficialOwner(PRIMARY_ID cif STRING, nationality STRING, gender STRING, createTime DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Merchant(PRIMARY_ID mid STRING, type_of_merchant STRING, createTime DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Account(PRIMARY_ID id UINT, createTime DATETIME, isBlocked BOOL, accountType STRING, phoneNumber STRING, email STRING, accountLevel STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Bank(PRIMARY_ID bank_id STRING, bank_name STRING, swift_code STRING, createTime DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Channel(PRIMARY_ID channel STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Name(PRIMARY_ID name STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX DoB(PRIMARY_ID dob DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Phone(PRIMARY_ID phone_number STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Email(PRIMARY_ID email STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX SSN(PRIMARY_ID ssn STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Community(PRIMARY_ID cid UINT) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Address(PRIMARY_ID address STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX IP(PRIMARY_ID id STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Device(PRIMARY_ID id STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX SAR(PRIMARY_ID id STRING, report_type STRING, report_time DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX CreditCard(PRIMARY_ID card_number INT, cm_curr_balance DOUBLE, createTime DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX DebitCard(PRIMARY_ID card_number INT, cm_curr_balance DOUBLE, createTime DATETIME) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Transaction(PRIMARY_ID id STRING, createTime DATETIME, amount DOUBLE, goodsType STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX City(PRIMARY_ID city STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add VERTEX Country(PRIMARY_ID country STRING) WITH STATS="OUTDEGREE_BY_EDGETYPE", PRIMARY_ID_AS_ATTRIBUTE="true"; +\
          Add UNDIRECTED EDGE ACCT_HAS_BANK(FROM Account, TO Bank); +\
          Add UNDIRECTED EDGE CUST_HAS_ACCT(FROM Company, TO Account|FROM Individual, TO Account); +\
          Add UNDIRECTED EDGE MERCHATN_HAS_ACCT(FROM Merchant, TO Account); +\
          Add UNDIRECTED EDGE BO_HAS_ACCT(FROM BeneficialOwner, TO Account); +\
          Add UNDIRECTED EDGE HAS_NAME(FROM Account, TO Name|FROM Company, TO Name|FROM Individual, TO Name|FROM BeneficialOwner, TO Name); +\
          Add UNDIRECTED EDGE HAS_DOB(FROM Account, TO DoB|FROM Company, TO DoB|FROM Individual, TO DoB|FROM BeneficialOwner, TO DoB); +\
          Add UNDIRECTED EDGE HAS_PHONE(FROM Account, TO Phone|FROM Company, TO Phone|FROM Individual, TO Phone|FROM BeneficialOwner, TO Phone); +\
          Add UNDIRECTED EDGE HAS_EMAIL(FROM Account, TO Email|FROM Company, TO Email|FROM Individual, TO Email|FROM BeneficialOwner, TO Email); +\
          Add UNDIRECTED EDGE HAS_SSN(FROM Account, TO SSN|FROM Company, TO SSN|FROM Individual, TO SSN|FROM BeneficialOwner, TO SSN); +\
          Add UNDIRECTED EDGE HAS_ADDRESS(FROM Company, TO Address|FROM Individual, TO Address|FROM BeneficialOwner, TO Address); +\
          Add UNDIRECTED EDGE ACCT_USES_IP(FROM Account, TO IP); +\
          Add UNDIRECTED EDGE ACCT_USES_DEV(FROM Account, TO Device); +\
          Add UNDIRECTED EDGE ACCT_HAS_CARD(FROM Account, TO DebitCard|FROM Account, TO CreditCard); +\
          Add UNDIRECTED EDGE TX_HAS_CHANNEL(FROM Transaction, TO Channel); +\
          Add UNDIRECTED EDGE SEND(FROM Account, TO Transaction); +\
          Add UNDIRECTED EDGE RECEIVE(FROM Account, TO Transaction); +\
          Add UNDIRECTED EDGE TX_HAS_MERCHANT(FROM Transaction, TO Merchant); +\
          Add UNDIRECTED EDGE TX_USE_CARD(FROM Transaction, TO DebitCard|FROM Transaction, TO CreditCard); +\
          Add UNDIRECTED EDGE CARD_RECEIVE(FROM DebitCard, TO Transaction|FROM CreditCard, TO Transaction); +\
          Add UNDIRECTED EDGE Account_HAS_INCIDENT(FROM SAR, TO Account); +\
          Add UNDIRECTED EDGE ACCT_HAS_Address(FROM Account, TO Address); +\
          Add UNDIRECTED EDGE IS_LOCATED_IN(FROM Address, TO City|FROM City, TO Country); +\
          Add UNDIRECTED EDGE CUST_HAS_COMMUNITY(FROM Company, TO Community|FROM Individual, TO Community); +\
        } +\
        RUN SCHEMA_CHANGE JOB change_schema_of_AntiFraud +\
        DROP JOB change_schema_of_AntiFraud +\
        '
    cmd = 'CREATE GRAPH AntiFraud () +\
        CREATE SCHEMA_CHANGE JOB change_schema_of_AntiFraud FOR GRAPH AntiFraud { +\
          Add UNDIRECTED EDGE CUST_HAS_COMMUNITY(FROM Company, TO Community|FROM Individual, TO Community); +\
        } +\
        RUN SCHEMA_CHANGE JOB change_schema_of_AntiFraud +\
        DROP JOB change_schema_of_AntiFraud +\
        '
    cmd_lines = cmd.split('+')
    LOGGER.info(str(len(cmd_lines)))
    element = self.sb.find_element(WorkgroupLocators.input_cmd)
    for i in range(len(cmd_lines)):
        LOGGER.info("input text:" + cmd_lines[i])
        self.sb.send_keys(WorkgroupLocators.input_cmd, cmd_lines[i])
        self.actions.move_to_element(element).click().key_down(Keys.ENTER).perform()

    self.wait_and_click(WorkgroupLocators.common_button.format("Run"))
    self.sb.assert_text("Successfully dropped jobs on the graph 'AntiFraud'", timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.sb.attach_allure_screenshot("create schema successfully screenshot")


  def connect_gsql_editor(self, graph_name=""):
    self.turn_to_workgroup()
    self.connect_tools("Query Editor")
    self.close_tips_panel()
    self.sb.assert_text("Run", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    # create a file
    self.wait_and_click(WorkgroupLocators.create_new_file)
    self.wait_and_click(WorkgroupLocators.common_button.format("GSQL File"))
    self.wait_and_click(WorkgroupLocators.input_cmd)
    self.sb.attach_allure_screenshot("create a file successfully screenshot")
    # run command
    self.sb.type(WorkgroupLocators.input_cmd, "ls")
    self.wait_and_click(WorkgroupLocators.run_all_button)
    self.sb.assert_text("Graphs:", timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.sb.attach_allure_screenshot("run command successfully screenshot")


  def switch_graph(self, graph_name="", select_last_graph = 1):
    self.wait_top_blue_circle_progressbar(visible_timeout=3)
    if select_last_graph == 1:
        elements = self.sb.find_elements(WorkgroupLocators.graph_option)
        start_index = len(elements) - 1
        LOGGER.info("graph options start_index:" + str(start_index))
        for i in range(start_index, -1, -1):
            elements[i].click()
            break
    else:
        self.wait_and_click(WorkgroupLocators.select_graph.format(graph_name))
    self.sb.attach_allure_screenshot("switch graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=3)

  def connect_explore_graph(self, graph_name=""):
    self.turn_to_workgroup()
    self.connect_tools("Explore Graph")
    self.sb.assert_text("exploring your graph", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    # select a graph
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.switch_graph("test_")
    self.wait_and_click(WorkgroupLocators.input_explore_query)
    self.sb.find_elements(WorkgroupLocators.select_explore_vertex)[1].click()
    self.sb.find_elements(WorkgroupLocators.operate_button)[1].click()
    self.sb.sleep(3)
    self.sb.attach_allure_screenshot("explore graph successfully screenshot")

  def connect_get_data_profile(self, graph_name=""):
    self.turn_to_workgroup()
    self.connect_tools("Get Data Profile")
    self.sb.assert_text("Generate Data Profile for", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    # select a graph
    self.wait_and_click(WorkgroupLocators.select_graph_trangle)
    self.switch_graph("test_")
    self.wait_and_click(WorkgroupLocators.common_button.format("Generate"))
    self.sb.attach_allure_screenshot("generate data profile successfully screenshot")
    self.sb.assert_text("Ready", timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.wait_and_click(WorkgroupLocators.preview_button)
    self.wait_top_blue_circle_progressbar()
    self.sb.assert_text("Data Profile For", timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.sb.assert_text("Average Degree", timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.sb.attach_allure_screenshot("preview data profile successfully screenshot")
    # delete the data prefiles record
    self.wait_and_click(WorkgroupLocators.common_span_button.format("Back"))
    self.wait_and_click(WorkgroupLocators.delete_button)
    self.wait_and_click(WorkgroupLocators.common_button.format("OK"))
    self.sb.attach_allure_screenshot("delete data profile successfully screenshot")

  def delete_all_existed_solution(self):
    LOGGER.info("begin to delete existed solution")
    self.sb.wait_for_element_clickable(WorkgroupLocators.common_button.format("Solution"), timeout=common_settings.WAIT_CLICK_TIME_OUT)
    elements = self.sb.find_elements(WorkgroupLocators.delete_button)
    LOGGER.info("find solution: " + str(len(elements)))
    # self.sb.sleep(300)
    for i in range(len(elements)):
        self.sb.find_elements(WorkgroupLocators.delete_button)[i].click()
        self.wait_and_click(WorkgroupLocators.common_button.format("OK"))
    self.sb.attach_allure_screenshot("delete all solution successfully screenshot")

  def connect_solution(self, solution_name="test"):
    self.turn_to_workgroup()
    self.connect_tools("Solution")
    self.delete_all_existed_solution()
    self.sb.assert_text("install your first solution", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    # add a solution
    LOGGER.info("begin to add solution")
    self.wait_and_click(WorkgroupLocators.common_span_button.format("Add Solution"))
    self.wait_top_blue_circle_progressbar(visible_timeout=common_settings.CLICK_TIME_OUT)
    # self.wait_and_click(WorkgroupLocators.common_button.format("Add"))
    #  select Product Recommendation solution
    self.sb.find_elements(WorkgroupLocators.common_button.format("Add"))[4].click()
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    self.sb.type(WorkgroupLocators.input_solution_name, solution_name)
    self.sb.attach_allure_screenshot("input solution name successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # data source
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    self.sb.attach_allure_screenshot("add solution successfully screenshot")


  def connect_from_API(self):
    self.turn_to_workgroup()
    self.connect_tools("Connect from API", need_wait_progressbar=False)
    self.sb.assert_text("Connect From API", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    # add a solution
    LOGGER.info("switch to build-in query")
    self.wait_and_click(WorkgroupLocators.common_button.format("Built-In Query"))
    self.sb.assert_text("showprocesslistall", timeout=common_settings.WAIT_CLICK_TIME_OUT)
    # todo click GET method and test
    # self.wait_and_click(WorkgroupLocators.common_div_button.format("GET"))
    self.sb.attach_allure_screenshot("built-in test successfully screenshot")


  def check_enable_tools(self):
    LOGGER.info("check enable GST and Insights ")
    self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Configuration"))
    self.wait_top_blue_circle_progressbar(visible_timeout=common_settings.SHORT_ASSERT_TIMEOUT_3)
    self.wait_and_click(WorkgroupLocators.common_button.format("Add-ons"))

    # judge the add-ons is added
    if not self.sb.is_element_present(WorkgroupLocators.common_h4_addons.format("TigerGraph Insights")) or not self.sb.is_element_present(WorkgroupLocators.common_h4_addons.format("TigerGraph GraphStudio")):
        LOGGER.info("go to market to add addons ")
        self.wait_and_click(WorkgroupLocators.redirect_to_addons_button)
        self.wait_top_blue_circle_progressbar()
        # switch to market
        self.sb.sleep(3)
        all_handles = self.sb.driver.window_handles
        LOGGER.info(all_handles)
        self.sb.switch_to_window(all_handles[1], timeout=common_settings.WAIT_CLICK_TIME_OUT)
        LOGGER.info("switch to addons")
        # disable all
        self.sb.wait_for_element_clickable(WorkgroupLocators.common_button.format("Request"), timeout=common_settings.WAIT_RENDER_TIME_OUT)
        elements = self.sb.find_elements(WorkgroupLocators.common_button.format("Edit"))
        LOGGER.info("find Edit addons:" + str(len(elements)))
        for i in range(len(elements)):
            try:
                LOGGER.info("Now operate Edit button " + str(i))
                elements[i].click()
                self.wait_and_click(WorkgroupLocators.common_button.format("Disable"))
                self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
                self.sb.wait_for_element_not_visible(WorkgroupLocators.common_button.format("Cancel"))
                self.sb.attach_allure_screenshot("disable copilot successfully screenshot")
            except Exception as e:
                LOGGER.info("Now operate disable addons Exception" + str(e))
        # enable copilot, insights, GST
        self.sb.wait_for_element_clickable(WorkgroupLocators.common_button.format("Add"), timeout=common_settings.WAIT_RENDER_TIME_OUT)
        elements = self.sb.find_elements(WorkgroupLocators.common_button.format("Add"))
        LOGGER.info("find Add addons:" + str(len(elements)))
        for i in range(len(elements)):
            try:
                LOGGER.info("Now operate add button " + str(i))
                if i == 0:
                    elements[i].click()
                    self.wait_and_click(WorkgroupLocators.common_button.format("Enable"))
                    self.wait_and_click(WorkgroupLocators.close_button)
                    self.sb.attach_allure_screenshot("add copilot successfully screenshot")
                if i == 1 or i == 2:
                    elements[i].click()
                    self.wait_and_click(WorkgroupLocators.common_button.format("Enable"))
                    self.sb.wait_for_element_not_visible(WorkgroupLocators.common_button.format("Back"))
                    self.sb.attach_allure_screenshot("add addons successfully screenshot")
            except Exception as e:
                LOGGER.info("Now operate add button Exception" + str(e))
        # close current tab
        self.sb.driver.close()
        self.sb.switch_to_default_window()
        self.wait_and_click(WorkgroupLocators.common_button.format("Cancel"))
    else:
        self.wait_and_click(WorkgroupLocators.common_button.format("Cancel"))
        LOGGER.info("already enable GST and Insights, close panel check ")

    # reopen edit panel to enable GST and insights
    self.sb.find_elements(WorkgroupLocators.more_button)[1].click()
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Configuration"))
    #  should have copilot, enable insights button(4)
    self.wait_top_blue_circle_progressbar(visible_timeout=common_settings.SHORT_ASSERT_TIMEOUT_3)
    self.wait_and_click(WorkgroupLocators.common_button.format("Add-ons"))
    elements = self.sb.find_elements(WorkgroupLocators.checkbox_status_workspace)
    need_save = False
    self.sb.attach_allure_screenshot("before enable all tools suites screenshot")
    for i in range(len(elements)):
        if len(elements) > 3:
          if i == 0:
              LOGGER.info("skip enable the copilot for LLM error")
              continue
          checkbox_status = self.sb.find_elements(WorkgroupLocators.checkbox_status_workspace)[i].get_attribute("aria-checked").lower()
          LOGGER.info(str(i + 1) + " checkbox_status: " + checkbox_status)
          if checkbox_status == "false":
              self.sb.find_elements(WorkgroupLocators.checkbox_button_workspace)[i].click()
              need_save = True
        else:
            # enable all
            LOGGER.info("no need to skip enable the copilot")
            checkbox_status = self.sb.find_elements(WorkgroupLocators.checkbox_status_workspace)[i].get_attribute("aria-checked").lower()
            LOGGER.info(str(i+1) + " checkbox_status: " + checkbox_status)
            if checkbox_status == "false":
                self.sb.find_elements(WorkgroupLocators.checkbox_button_workspace)[i].click()
                need_save = True

    self.sb.attach_allure_screenshot("enable all tools suites successfully screenshot")
    if need_save:
    # save always
        self.wait_and_click(WorkgroupLocators.save_addon_button)
        self.wait_top_blue_circle_progressbar()
    else:
        self.wait_and_click(WorkgroupLocators.common_button.format("Cancel"))

  def connect_from_addons(self, tools_name="Admin Portal", search_log=False):
    self.turn_to_workgroup()
    self.sb.wait_for_element_clickable(WorkgroupLocators.create_WS_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    if tools_name != "Admin Portal":
        self.check_enable_tools()

    # click addons
    # self.connect_suites(tools_name=tools_name)
    self.connect_tools(tools_name=tools_name, is_suites=True)
    # switch to tools tab
    self.sb.sleep(3)
    all_handles = self.sb.driver.window_handles
    LOGGER.info(all_handles)
    self.sb.switch_to_window(all_handles[1], timeout=common_settings.WAIT_CLICK_TIME_OUT)
    LOGGER.info("switch to " + tools_name)
    if tools_name == "Admin Portal":
        self.sb.attach_allure_screenshot("switch to admin portal screenshot")
        self.sb.assert_text("Dashboard", timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info("assert " + tools_name + " succeed")
        self.sb.attach_allure_screenshot("succeed switch to GAP successfully screenshot")
    elif tools_name == "TigerGraph GraphStudio":
        self.sb.attach_allure_screenshot("switch to Graph Studio screenshot")
        self.sb.assert_text("Home", timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info("assert " + tools_name + " succeed")
        self.sb.attach_allure_screenshot("succeed switch to GST successfully screenshot")
    elif tools_name == "TigerGraph Insights":
        self.sb.attach_allure_screenshot("switch to Insights screenshot")
        self.sb.assert_text("My Applications", timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info("assert " + tools_name + " succeed")
        self.sb.attach_allure_screenshot("succeed switch to Insights successfully screenshot")
    if search_log:
        # begin to search log in GAP
        self.sb.attach_allure_screenshot("begin to search log screenshot")
        self.search_log_in_GAP()
    self.sb.driver.close()
    self.sb.switch_to_default_window()
    LOGGER.info("switch_to_default_window succeed")
    self.sb.attach_allure_screenshot("switch to default window successfully screenshot")

  def init_workspace_role(self):
    try:
        if self.sb.is_element_present(WorkgroupLocators.common_div_button.format("Member")):
            list = self.sb.find_elements(WorkgroupLocators.common_div_button.format("Member"))
            LOGGER.info("found " + str(len(list)) + " roles")
            for i in range(len(list)):
                LOGGER.info("begin to delete " + str(i) + " role")
                self.sb.hover_and_click(hover_selector=WorkgroupLocators.common_div_button.format("Member"), click_selector=WorkgroupLocators.delete_role_button, timeout=common_settings.WAIT_CLICK_TIME_OUT)
                self.wait_top_blue_circle_progressbar()
        self.sb.attach_allure_screenshot("finish delete role screenshot")
    except Exception as e:
        LOGGER.info("delete role exception:" + str(e))
        self.sb.attach_allure_screenshot("delete role exception screenshot")

  def grant_workspace_role(self, role="Workspace Admin"):
    self.turn_to_workgroup()
    self.sb.wait_for_element_clickable(WorkgroupLocators.create_WS_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.wait_and_click(WorkgroupLocators.common_button.format("Access Management"))
    self.sb.attach_allure_screenshot("turn to Access Management screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("QE_E2E_WS"))
    self.wait_top_blue_circle_progressbar()
    # init the role
    self.init_workspace_role()
    LOGGER.info("begin to grant " + role)
    self.sb.type(WorkgroupLocators.input_role, "<EMAIL>")
    self.wait_and_click(WorkgroupLocators.select_account)
    self.wait_top_blue_circle_progressbar()
    self.sb.attach_allure_screenshot("grant role with default role screenshot")

    if role == "Workspace Admin":
        self.wait_and_click(WorkgroupLocators.common_div_button.format("Admin"))
        self.wait_top_blue_circle_progressbar()
        LOGGER.info("grant " + role + " succeed")
        self.sb.attach_allure_screenshot("succeed grant Workspace Admin successfully screenshot")
    elif role == "Workspace Member":
        self.wait_and_click(WorkgroupLocators.common_div_button.format("Member"))
        self.wait_top_blue_circle_progressbar(visible_timeout=common_settings.SHORT_ASSERT_TIMEOUT_2)
        LOGGER.info("grant " + role + " succeed")
        self.sb.attach_allure_screenshot("succeed grant Workspace Member successfully screenshot")
    LOGGER.info("grant_workspace_role succeed")
    self.sb.attach_allure_screenshot("grant_workspace_role successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("Cancel"))
    loginUtil = LoginUtils(self.sb)
    loginUtil.logout_cloud()

  def assert_save_button_with_special_characters(self):
    if self.sb.is_element_clickable(WorkgroupLocators.common_button.format("Save")):
        LOGGER.info("Save button should not be clickable , assert False")
        self.sb.attach_allure_screenshot("Save button should not be clickable, assert False screenshot")
        assert False
    else:
        LOGGER.info("Save button is not clickable, pass")
    self.sb.attach_allure_screenshot("Save button should not be clickable screenshot")

  def assert_network_access_with_special_characters(self):
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.assert_text("Error:Field validation", timeout=common_settings.WAIT_RENDER_TIME_OUT )
    self.sb.attach_allure_screenshot("assert_network_access_with_special_characters screenshot")

  def input_sepcial_characters_case(self, tab="Backup And Restore"):
    self.turn_to_workgroup()
    self.sb.wait_for_element_clickable(WorkgroupLocators.create_WS_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.wait_and_click(WorkgroupLocators.common_button.format(tab))
    self.sb.attach_allure_screenshot("turn to " + tab + " screenshot")
    special_characters = "~!@#$%^"
    long_characters = "12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890"
    chinese_characters = "无效的输入"

    if tab == "Backup And Restore":
        self.wait_backup_list_render()
        self.sb.wait_for_element_clickable(WorkgroupLocators.input_hour, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.type(WorkgroupLocators.input_hour, special_characters)
        self.assert_save_button_with_special_characters()
        self.sb.attach_allure_screenshot("input special characters screenshot")
        self.sb.type(WorkgroupLocators.input_hour, long_characters)
        self.sb.attach_allure_screenshot("input special characters screenshot")
        self.assert_save_button_with_special_characters()
        self.sb.type(WorkgroupLocators.input_hour, chinese_characters)
        self.sb.attach_allure_screenshot("input special characters screenshot")
        self.assert_save_button_with_special_characters()
        self.sb.attach_allure_screenshot("succeed checking input special characters successfully screenshot")
    elif tab == "Network Access":
        self.sb.wait_for_element_visible(WorkgroupLocators.label_checkbox, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        checkbox_status = self.sb.find_elements(WorkgroupLocators.ip_allow_list)[0].get_attribute("aria-checked").lower()
        LOGGER.info("ip_allow_list checkbox_status: " + checkbox_status)
        if checkbox_status == "true":
            LOGGER.info("no need to click checkbox_status")
        else:
            self.wait_and_click(WorkgroupLocators.label_checkbox)
        self.sb.attach_allure_screenshot("open ip_allow_list checkbox_status successfully screenshot")

        # click add ip button
        self.wait_and_click(WorkgroupLocators.common_button.format("Add IP"))
        self.sb.type(WorkgroupLocators.input_ip, special_characters)
        self.sb.type(WorkgroupLocators.input_note, special_characters)
        self.assert_network_access_with_special_characters()
        self.sb.type(WorkgroupLocators.input_ip, long_characters)
        self.sb.type(WorkgroupLocators.input_note, long_characters)
        self.assert_network_access_with_special_characters()
        self.sb.type(WorkgroupLocators.input_ip, chinese_characters)
        self.sb.type(WorkgroupLocators.input_note, chinese_characters)
        self.assert_network_access_with_special_characters()
        self.sb.attach_allure_screenshot("succeed checking input special characters successfully screenshot")

        # restore the env
        self.wait_and_click(WorkgroupLocators.common_button.format("Cancel"))
        self.sb.wait_for_element_clickable(WorkgroupLocators.label_checkbox, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        checkbox_status = self.sb.find_elements(WorkgroupLocators.ip_allow_list)[0].get_attribute("aria-checked").lower()
        LOGGER.info("ip_allow_list checkbox_status: " + checkbox_status)
        if checkbox_status == "true":
            LOGGER.info("click to disable checkbox_status")
            self.wait_and_click(WorkgroupLocators.label_checkbox)
        else:
            LOGGER.info("no need to disable checkbox_status")
        self.sb.attach_allure_screenshot("restore ip_allow_list checkbox_status successfully screenshot")
        self.double_check_disable_Network_Access()


  def double_check_disable_Network_Access(self, tab="Network Access"):
    # double check restore the ip_allow
    self.wait_and_click(WorkgroupLocators.common_button.format("General"))
    self.wait_and_click(WorkgroupLocators.common_button.format(tab))
    self.sb.wait_for_element_visible(WorkgroupLocators.label_checkbox, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    checkbox_status = self.sb.find_elements(WorkgroupLocators.ip_allow_list)[0].get_attribute("aria-checked").lower()
    LOGGER.info("double check ip_allow_list checkbox_status: " + checkbox_status)
    if checkbox_status == "true":
      LOGGER.info("double check click to disable checkbox_status")
      self.wait_and_click(WorkgroupLocators.label_checkbox)
    else:
      LOGGER.info("double check no need to disable checkbox_status")
    self.sb.attach_allure_screenshot("double check restore ip_allow_list checkbox_status successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("General"))



  def check_workspace_role_privillege(self, role="Workspace Admin"):
    self.cloud_v4_login_with_workspace_role()
    self.turn_to_workgroup()
    self.sb.wait_for_element_clickable(WorkgroupLocators.more_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
    list_menu = ["General", "Workspace", "Database", "Monitor", "Solution", "Data Profile", "Access Management", "Backup And Restore", "Network Access"]
    ws_config_menu = ["General", "Advanced Settings", "Add-ons", "Alerts", "Graph Admin"]
    if role == "Workspace Admin":
        # check tools tip privilege
        for item in list_menu:
            if self.sb.is_element_visible(WorkgroupLocators.common_button.format(item)):
                if item == "Network Access":
                    LOGGER.info("should not see " + item + " memu, assert False")
                    self.sb.attach_allure_screenshot("should not see " + item + " memu for WS admin, assert False screenshot")
                    assert False
                else:
                    LOGGER.info("Can see " + item + " memu, pass")
            else:
                if item == "Network Access":
                    LOGGER.info("should not see " + item + " memu, pass")
                else:
                    LOGGER.info("can't see " + item + " memu, assert False")
                    self.sb.attach_allure_screenshot("can't see " + item + " memu for WS admin, assert False screenshot")
                    assert False

        # check edit workspace configuration privilege
        # click the more button
        self.sb.find_elements(WorkgroupLocators.more_button)[0].click()
        self.wait_and_click(WorkgroupLocators.common_div_button.format("Configuration"))
        self.wait_top_blue_circle_progressbar()
        for item in ws_config_menu:
            if self.sb.is_element_visible(WorkgroupLocators.common_button.format(item)):
                LOGGER.info("Can see " + item + " tab, pass")
            else:
                LOGGER.info("can't see " + item + " memu, assert False")
                self.sb.attach_allure_screenshot("can't see " + item + " memu, assert False screenshot")
                assert False
        LOGGER.info("check " + role + " privilege succeed")
        self.sb.attach_allure_screenshot("succeed check Workspace Admin privilege successfully screenshot")
    elif role == "Workspace Member":
        # check tools tip privilege
        for item in list_menu:
            if self.sb.is_element_visible(WorkgroupLocators.common_button.format(item)):
                if item == "Access Management" or item == "Backup And Restore" or item == "Network Access":
                    LOGGER.info("should not see " + item + " memu, assert False")
                    self.sb.attach_allure_screenshot("should not see " + item + " memu for WS member, assert False screenshot")
                    assert False
                else:
                    LOGGER.info("Can see " + item + " tab, pass")
            else:
                if item == "Access Management" or item == "Backup And Restore" or item == "Network Access":
                    LOGGER.info("should not see " + item + " memu, pass")
                else:
                    LOGGER.info("can't see " + item + " memu, assert False")
                    self.sb.attach_allure_screenshot("can't see " + item + " memu for WS member, assert False screenshot")
                    assert False

        # check edit workspace configuration privilege
        # click the more button
        self.sb.find_elements(WorkgroupLocators.more_button)[0].click()
        if self.sb.is_element_visible(WorkgroupLocators.common_div_button.format("Configuration")):
            LOGGER.info("should not see edit button, assert False")
            self.sb.attach_allure_screenshot("should not see edit button, assert False screenshot")
            assert False
        else:
            LOGGER.info("should not see edit button, pass")
        LOGGER.info("check " + role + " privilege succeed")
        self.sb.attach_allure_screenshot("succeed check Workspace Member privilege successfully screenshot")

    LOGGER.info("check_workspace_role_privillege succeed")
    self.sb.attach_allure_screenshot("check_workspace_role_privillege successfully screenshot")


  def search_log_in_GAP(self):
    self.into_Monitor_logs()
    self.sb.wait_for_element_clickable(GAPHomeLocators.log_pattern, timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.sb.type(GAPHomeLocators.log_pattern, "error")
    self.wait_and_click(GAPHomeLocators.search)
    self.sb.attach_allure_screenshot("click search button successfully screenshot")
    self.sb.wait_for_element_clickable(GAPHomeLocators.all_component, timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.sb.attach_allure_screenshot("search log successfully screenshot")
    self.sb.assert_text(GAPHomeLocators.assert_search_log_text, timeout=common_settings.WAIT_CLICK_TIME_OUT)



  def monitor_ws(self):
    self.turn_to_workgroup()
    self.wait_and_click(WorkgroupLocators.common_button.format("Monitor"))
    self.sb.attach_allure_screenshot("pending in monitor screenshot")

    iframes = self.sb.find_elements('iframe')

    LOGGER.info(f"Found {len(iframes)} iframes")
    for iframe in iframes:
        LOGGER.info(f"iframe title: {iframe.get_attribute('title')}")
        LOGGER.info(f"iframe src: {iframe.get_attribute('src')}")

    try:
        self.sb.switch_to_frame(frame='iframe[title="monitor"]', timeout=None)
        LOGGER.info("Successfully switched to monitor iframe")
    except Exception as e:
        LOGGER.error(f"Failed to switch iframe: {str(e)}")
        self.sb.attach_allure_screenshot("iframe_switch_failed")
        raise e

    # Wait for page to load
    self.sb.sleep(5)

    # Check TOTAL CPU CORES metric
    try:
        LOGGER.info("Start finding monitoring metrics")
        # Wait for page to fully load
        self.sb.sleep(10)

        # Try to find different monitoring metrics
        metrics_xpaths = [
            "//h6[contains(@title, 'TOTAL CPU CORES')]",
            "//h6[contains(@title, 'TOTAL MEMORY')]",
            "//h6[contains(@title, 'CPU USAGE PER NODE')]",
            "//h6[contains(@title, 'RAM USAGE PER NODE')]",
            "//h6[contains(@title, 'LOG DISK PER NODE')]",
            "//h6[contains(@title, 'TOTAL RAM USAGE')]",
            "//h6[contains(@title, 'KAFKA DISK PER NODE')]",
            "//h6[contains(@title, 'NETWORK TRAFFIC RATE PER NODE')]",
            "//h6[contains(@title, 'TOTAL DISK USAGE PER NODE')]",
            "//h6[contains(@title, 'INCOMING NETWORK TRAFFIC')]",
            "//h6[contains(@title, 'OUTGOING NETWORK TRAFFIC')]",
            "//h6[contains(@title, 'NETWORK CONNECTIONS')]",
            "//h6[contains(@title, 'TOTAL MEMORY USAGE PER SERVICE')]",
            "//h6[contains(@title, 'SERVICE HEALTHINESS')]",
            "//h6[contains(@title, 'QPS')]",
            "//h6[contains(@title, 'TOP 10 SLOW QUERIES')]",
            "//h6[contains(@title, 'QUERY COUNT BY WEEK')]",
            "//h6[contains(@title, 'QUERY COUNT BY DAY')]",
            "//h6[contains(@title, 'QUERY COUNT BY HOUR')]"
        ]

        metric_found = False
        for xpath in metrics_xpaths:
            try:
                self.sb.wait_for_element_present(xpath, timeout=5)
                metric_value = self.sb.get_text(xpath)
                LOGGER.info(f"Found monitoring metric: {metric_value}")
                metric_found = True
                break
            except Exception:
                continue

        if not metric_found:
            raise Exception("No monitoring metrics found")

        self.sb.attach_allure_screenshot("monitor_metrics_found")

    except Exception as e:
        LOGGER.error(f"Failed to find monitoring metrics, error: {str(e)}")
        self.sb.attach_allure_screenshot("monitor_metrics_error")


  def toolsbar_for_WS(self, tab_name="", assert_texts=""):
    self.turn_to_workgroup()
    LOGGER.info("click: " + tab_name + ", assert: " + assert_texts)
    self.wait_and_click(WorkgroupLocators.common_button.format(tab_name))
    self.sb.assert_text(assert_texts, timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.sb.attach_allure_screenshot("check "+tab_name+" successfully screenshot")


  def connect_load_data_from_s3(self, graph_name="test_S3"):
    """Load data from S3 bucket to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")
    # create a new graph
    # Click add load data button to start loading data
    self.wait_and_click(WorkgroupLocators.add_load_data)
    # Select graph dropdown and create new graph
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)
    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)
    # Input graph name and complete graph creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)
    # Select Amazon S3 as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Amazon S3"))
    # Configure S3 connection settings
    self.wait_and_click(WorkgroupLocators.add_data_source_button)
    s3_config = read_test_data(file="tgcloud_test_data.json").get("s3_config")
    timestamp = int(time.time())
    alias = f"s3_alias_{timestamp}"
    # Input S3 connection details
    self.sb.type(WorkgroupLocators.input_field_after_label.format("Connection alias"), alias)
    self.sb.type(WorkgroupLocators.input_field_after_label.format("AWS access key ID"), s3_config.get("access_key"))
    self.sb.type(WorkgroupLocators.input_field_after_label.format("AWS secret access key"), s3_config["secret_key"])
    self.sb.type(WorkgroupLocators.s3_uri_input, s3_config["bucket"])
    # Save S3 configuration
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.attach_allure_screenshot("save s3 config successfully screenshot")
    self.wait_and_click(WorkgroupLocators.next_button)
    # Configure data mapping
    self.wait_top_blue_circle_progressbar(visible_timeout=5)
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # Save configuration
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.attach_allure_screenshot("config map successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # confirm
    self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    self.check_save_schema_job(timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)

    self.sb.type('input[placeholder="Search Job or File Name"]', "Account.csv" + Keys.ENTER)
    self.wait_top_blue_circle_progressbar(visible_timeout=30)
    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()
    self.sb.wait_for_text_visible("FINISHED", timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)

    job_status = self.sb.get_text("span[title='FINISHED']")
    assert job_status == "FINISHED", f"Job status is {job_status}, expected FINISHED"
    LOGGER.info("Load data job completed successfully with status: FINISHED")
    self.sb.attach_allure_screenshot("load from S3 successfully screenshot")


  def connect_load_data_from_gcs(self, graph_name="test_"):
    """Load data from Google Cloud Storage bucket to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")
    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)
    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)
    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)
    # Select GCS as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Google Cloud Storage"))
    # Configure GCS connection settings
    self.wait_and_click(WorkgroupLocators.add_data_source_button)
    # Generate unique connection alias
    timestamp = int(time.time())
    alias = f"gcs_alias_{timestamp}"
    # Input GCS connection information
    self.sb.type(WorkgroupLocators.input_field_after_label.format("Connection alias"), alias)
    # Upload service account key file
    gcs_config = read_test_data(file="tgcloud_test_data.json").get("gcs_config")
    service_account_key = gcs_config.get("service_key")
    # Generate temporary file path
    temp_key_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), f"gcs_key_{int(time.time())}.json")
    # Write json file
    with open(temp_key_path, 'w') as f:
        json.dump(service_account_key, f, indent=2)
    # Upload file
    self.sb.choose_file(WorkgroupLocators.input_field_after_label.format("GCS service account key"), temp_key_path)
    # Delete temporary file
    os.remove(temp_key_path)
    # Input GCS URI
    self.sb.type(WorkgroupLocators.gcs_uri_input, gcs_config.get("bucket"))
    # Save GCS configuration
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.attach_allure_screenshot("save gcs config successfully screenshot")
    self.wait_and_click(WorkgroupLocators.next_button)
    # Configure data mapping
    self.wait_top_blue_circle_progressbar(visible_timeout=5)
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # Save configuration
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.attach_allure_screenshot("config map successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # Confirm and start loading
    self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    self.check_save_schema_job(timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()
    self.sb.type('input[placeholder="Search Job or File Name"]', "Case.csv" + Keys.ENTER)
    self.wait_top_blue_circle_progressbar(visible_timeout=30)
    self.sb.wait_for_text_visible("FINISHED", timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)
    job_status = self.sb.get_text("span[title='FINISHED']")
    assert job_status == "FINISHED", f"Job status is {job_status}, expected FINISHED"
    LOGGER.info("Load data job completed successfully with status: FINISHED")
    self.sb.attach_allure_screenshot("load from GCS successfully screenshot")

  def connect_load_data_from_abs(self, graph_name="test_"):
    """Load data from Azure Blob Storage to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")
    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)
    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)
    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)
    # Select Azure Blob Storage as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Azure Blob Storage"))
    # Configure ABS connection settings
    self.wait_and_click(WorkgroupLocators.add_data_source_button)
    abs_config = read_test_data(file="tgcloud_test_data.json").get("abs_config")
    # Generate unique connection alias
    timestamp = int(time.time())
    alias = f"abs_alias_{timestamp}"
    # Input ABS connection information
    self.sb.type(WorkgroupLocators.input_field_after_label.format("Connection alias"), alias)

    self.sb.type(WorkgroupLocators.input_field_after_label.format("ABS connection string"), abs_config["connection_string"])

    self.sb.type(WorkgroupLocators.abs_uri_input, abs_config["uri"])
    # Save ABS configuration
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.attach_allure_screenshot("save abs config successfully screenshot")
    self.wait_and_click(WorkgroupLocators.next_button)
    # Configure data mapping
    self.wait_top_blue_circle_progressbar(visible_timeout=5)
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # Save configuration
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.attach_allure_screenshot("config map successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
    # Confirm and start loading
    self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    self.check_save_schema_job(timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)
    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()
    self.sb.wait_for_text_visible("FINISHED", timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)
    self.sb.type('input[placeholder="Search Job or File Name"]', "part-00000-7f084763-0c5b-4142-be53-9f6161424357-c000.csv" + Keys.ENTER)
    self.wait_top_blue_circle_progressbar(visible_timeout=30)
    job_status = self.sb.get_text("span[title='FINISHED']")
    assert job_status == "FINISHED", f"Job status is {job_status}, expected FINISHED"
    LOGGER.info("Load data job completed successfully with status: FINISHED")
    self.sb.attach_allure_screenshot("load from ABS successfully screenshot")

  def connect_gsql_editor_query_count(self):
    """Query vertex and edge count in Query Editor"""
    self.turn_to_workgroup()
    # Enter Query Editor
    self.connect_tools("Query Editor")

    try:
        self.wait_and_click(WorkgroupLocators.common_button.format("Got it!"))
    except:
        LOGGER.info("Got it button not found, continuing...")
    self.wait_and_click(WorkgroupLocators.graph_select)
    graph_elements = self.sb.find_elements(WorkgroupLocators.graph_option)
    graph_name = ""
    for element in graph_elements:
        name = element.text
        if "test_S3" in name:
            graph_name = name
            break

    # Create new file
    self.wait_and_click(WorkgroupLocators.create_new_file)
    self.wait_and_click(WorkgroupLocators.common_button.format("GSQL File"))

    query = f"""USE GRAPH {graph_name}

    CREATE OR REPLACE DISTRIBUTED QUERY vertexCount() FOR GRAPH {graph_name} {{
    ListAccum<EDGE> @@edgeList;
    seed = {{phone.*}};
    S1 = SELECT s
            FROM seed:s -(:e)-:t
            ACCUM @@edgeList += e;

    PRINT @@edgeList;
   """


    self.sb.type(WorkgroupLocators.input_cmd, query)
    # Run query
    self.wait_and_click(WorkgroupLocators.run_all_button)
    query = f"""install query vertexCount
    RUN QUERY vertexCount()
    """
    self.wait_and_click(WorkgroupLocators.create_new_file)
    self.wait_and_click(WorkgroupLocators.common_button.format("GSQL File"))
    self.sb.type(WorkgroupLocators.input_cmd, query)
    # Run query
    self.wait_and_click(WorkgroupLocators.run_all_button)

    # Wait for results
    self.sb.sleep(common_settings.WAIT_PAGE_LOAD_TIME_OUT)
    # Get message logs content
    message_logs = self.sb.find_element('div[data-baseweb="tab-panel"][role="tabpanel"] pre').text
    # Check if contains '[]'
    if 'The query response size is ' not in message_logs:
        raise Exception("'[]' not found in message logs, query result is abnormal")
    self.sb.attach_allure_screenshot("query vertex and edge count result")

  def test_connect_explore_graph_phone_vertex(self):
    """Test exploring graph with phone vertex"""
    self.turn_to_workgroup()
    # Enter Explore Graph
    self.connect_tools("Explore Graph")

    # Wait for page to load
    self.sb.assert_text("exploring your graph", timeout=common_settings.WAIT_CREATE_WS_TIME_OUT_200)

    # self.wait_and_click('div[data-baseweb="select"]')
    # # Select graph
    # self.switch_graph("test_S3")

    # Click input box to open vertex selection dropdown
    LOGGER.info("Attempting to click query input box")
    try:
        # Use more precise locator
        self.sb.wait_for_element_visible('input[aria-autocomplete="list"][role="combobox"]', timeout=60)
        self.sb.click('input[aria-autocomplete="list"][role="combobox"]')
        LOGGER.info("Successfully clicked query input box")
    except Exception as e:
        LOGGER.error(f"Failed to click query input box: {str(e)}")
        # Log current page state
        LOGGER.info("Current page elements:")
        elements = self.sb.find_elements("input")
        for elem in elements:
            try:
                LOGGER.info(f"Element attributes: aria-autocomplete={elem.get_attribute('aria-autocomplete')}, role={elem.get_attribute('role')}")
            except:
                pass
        self.sb.attach_allure_screenshot("query_input_click_failed")
        raise e

    # Select phone vertex from dropdown list
    phone_option = "//li[@role='option']//div[contains(text(),'phone')]"
    LOGGER.info(f"Attempting to locate phone option: {phone_option}")
    try:
        self.wait_and_click(phone_option)
        LOGGER.info("Successfully clicked phone option")
    except Exception as e:
        LOGGER.error(f"Failed to click phone option: {str(e)}")
        elements = self.sb.find_elements("//li[@role='option']//div")
        LOGGER.info(f"Currently visible options: {[e.text for e in elements]}")
        self.sb.attach_allure_screenshot("phone_option_not_found")
        raise e

    # Click run button
    run_button = "//div[@data-baseweb='select']/following-sibling::div/div/button[2]"
    self.wait_and_click(run_button)
    # Click Proceed button
    proceed_button = "//button[normalize-space(text())='Proceed']"
    self.sb.wait_for_element_visible(proceed_button)
    self.sb.click(proceed_button)
    self.sb.sleep(15)
    self.sb.attach_allure_screenshot("explore graph phone vertex result")
    # Check if data is displayed
    graph_container = "canvas[data-id='layer2-node']"
    self.sb.wait_for_element_present(graph_container)
    # Check if canvas is rendered properly
    canvas_width = self.sb.get_attribute(graph_container, "width")
    canvas_height = self.sb.get_attribute(graph_container, "height")
    if int(canvas_width) == 0 or int(canvas_height) == 0:
        raise Exception("Canvas is not rendered properly")


  def verify_monitor_metrics(self):
    self.turn_to_workgroup()
    self.wait_and_click(WorkgroupLocators.common_button.format("Monitor"))
    self.sb.attach_allure_screenshot("pending in monitor screenshot")

    iframes = self.sb.find_elements('iframe')

    LOGGER.info(f"Found {len(iframes)} iframes")
    for iframe in iframes:
        LOGGER.info(f"iframe title: {iframe.get_attribute('title')}")
        LOGGER.info(f"iframe src: {iframe.get_attribute('src')}")

    try:
        self.sb.switch_to_frame(frame='iframe[title="monitor"]', timeout=None)
        LOGGER.info("Successfully switched to monitor iframe")
    except Exception as e:
        LOGGER.error(f"Failed to switch iframe: {str(e)}")
        self.sb.attach_allure_screenshot("iframe_switch_failed")
        raise e

    # Check TOTAL CPU CORES metric
    try:
        LOGGER.info("Start finding monitoring metrics")
        # Wait for page to fully load

        # Try to find different monitoring metrics
        metrics_xpaths = [
            "//h6[contains(@title, 'TOTAL CPU CORES')]",
            "//h6[contains(@title, 'TOTAL MEMORY')]",
            "//h6[contains(@title, 'CPU USAGE PER NODE')]",
            "//h6[contains(@title, 'RAM USAGE PER NODE')]",
            "//h6[contains(@title, 'LOG DISK PER NODE')]",
            "//h6[contains(@title, 'TOTAL RAM USAGE')]",
            "//h6[contains(@title, 'KAFKA DISK PER NODE')]",
            "//h6[contains(@title, 'NETWORK TRAFFIC RATE PER NODE')]",
            "//h6[contains(@title, 'TOTAL DISK USAGE PER NODE')]",
            "//h6[contains(@title, 'INCOMING NETWORK TRAFFIC')]",
            "//h6[contains(@title, 'OUTGOING NETWORK TRAFFIC')]",
            "//h6[contains(@title, 'NETWORK CONNECTIONS')]",
            "//h6[contains(@title, 'TOTAL MEMORY USAGE PER SERVICE')]",
            "//h6[contains(@title, 'SERVICE HEALTHINESS')]",
            "//h6[contains(@title, 'QPS')]",
            "//h6[contains(@title, 'TOP 10 SLOW QUERIES')]",
            "//h6[contains(@title, 'QUERY COUNT BY WEEK')]",
            "//h6[contains(@title, 'QUERY COUNT BY DAY')]",
            "//h6[contains(@title, 'QUERY COUNT BY HOUR')]"
        ]

        metric_found = False
        for xpath in metrics_xpaths:
            try:
                self.sb.wait_for_element_present(xpath, timeout=300)
                metric_value = self.sb.get_text(xpath)
                LOGGER.info(f"Found monitoring metric: {metric_value}")
                metric_found = True
                break
            except Exception:
                continue

        if not metric_found:
            raise Exception("No monitoring metrics found")

        self.sb.attach_allure_screenshot("monitor_metrics_found")

    except Exception as e:
        LOGGER.error(f"Failed to find monitoring metrics, error: {str(e)}")
        self.sb.attach_allure_screenshot("monitor_metrics_error")


  def connect_load_data_from_local(self, graph_name="test_"):
    """Load data from local file to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")

    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)

    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)

    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)

    # Select Local File as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Local File"))

    # Create temporary CSV file from test data
    local_config = read_test_data(file="tgcloud_test_data.json").get("local_file_config")
    temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    temp_file_path = os.path.join(temp_dir, local_config["file_name"])

    # Write data to temporary CSV file
    with open(temp_file_path, 'w') as f:
        # Write headers
        f.write(','.join(local_config["headers"]) + '\n')
        # Write data rows
        f.write('\n'.join(local_config["data"]))

    # Upload the temporary file
    self.sb.choose_file(WorkgroupLocators.upload_file_input, temp_file_path)

    # Wait for file upload to complete
    self.wait_top_blue_circle_progressbar()

    # Wait for file processing to complete
    try:
        # Wait for filename to appear on interface
        self.sb.wait_for_text_visible(local_config["file_name"], timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info("File upload successful")

        # Wait for upload progress bar to disappear
        self.sb.wait_for_element_not_visible(WorkgroupLocators.upload_file_progressbar, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info("File processing complete")

        # Take screenshot of current status
        self.sb.attach_allure_screenshot("upload local file successfully screenshot")

        # Wait for Next button to be visible and clickable
        self.sb.wait_for_element_visible(WorkgroupLocators.next_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.wait_for_element_clickable(WorkgroupLocators.next_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)

        # Try to scroll to Next button
        next_button = self.sb.find_element(WorkgroupLocators.next_button)
        self.sb.execute_script("arguments[0].scrollIntoView(true);", next_button)

        # Wait for Next button to be clickable after scrolling
        self.sb.wait_for_element_clickable(WorkgroupLocators.next_button, timeout=common_settings.WAIT_RENDER_TIME_OUT)

        # Click Next button
        LOGGER.info("Clicking Next button")
        self.wait_and_click(WorkgroupLocators.next_button)

    except Exception as e:
        LOGGER.error(f"File upload or processing failed: {str(e)}")
        self.sb.attach_allure_screenshot("file_upload_error")
        raise e

    # Configure data mapping
    self.wait_top_blue_circle_progressbar(visible_timeout=common_settings.WAIT_RENDER_TIME_OUT)
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))

    # Save configuration
    self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
    self.sb.attach_allure_screenshot("config map successfully screenshot")
    self.wait_and_click(WorkgroupLocators.common_button.format("Next"))

    # Confirm and start loading
    self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
    self.check_save_schema_job(timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)

    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()
    self.sb.wait_for_text_visible("FINISHED", timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)
    self.sb.type('input[placeholder="Search Job or File Name"]', "phone_data.csv" + Keys.ENTER)
    self.wait_top_blue_circle_progressbar(visible_timeout=30)
    job_status = self.sb.get_text("span[title='FINISHED']")
    assert job_status == "FINISHED", f"Job status is {job_status}, expected FINISHED"

    LOGGER.info("Load data job completed successfully with status: FINISHED")
    self.sb.attach_allure_screenshot("load from local file successfully screenshot")

    # Clean up temporary files
    try:
        os.remove(temp_file_path)
        os.rmdir(temp_dir)
        LOGGER.info("Temporary files and directory cleaned up")
    except Exception as e:
        LOGGER.warning(f"Failed to clean up temporary files: {str(e)}")


  def connect_load_data_from_delta_lake(self, graph_name="test_"):
    """Load data template from Delta Lake to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")

    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)

    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)

    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)

    # Select Delta Lake as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Delta Lake"))

    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()

    # Check if Delta Lake keyword exists
    self.sb.wait_for_element_visible('button[aria-label="Delta Lake"]', timeout=common_settings.WAIT_RENDER_TIME_OUT)
    LOGGER.info("Found Delta Lake template")

    # Click "OPEN IN GSQL EDITOR" button
    self.wait_and_click(WorkgroupLocators.common_button.format("Open In Query Editor"))

    # Wait for redirect to Query Editor page
    self.wait_top_blue_circle_progressbar()

    # Check if successfully redirected to Query Editor
    try:
        # Check if editor content contains Delta Lake related text
        editor_content = self.sb.get_text('div[class="cm-content"]')
        if "DeltaLake" not in editor_content:
            raise Exception("Delta Lake template content not found in Query Editor")
        LOGGER.info("Successfully loaded Delta Lake template to Query Editor")
    except Exception as e:
        LOGGER.error(f"Failed to redirect to Query Editor: {str(e)}")
        self.sb.attach_allure_screenshot("gsql_editor_load_failed")
        raise e

    self.sb.attach_allure_screenshot("load Delta Lake template successfully screenshot")
    LOGGER.info("Delta Lake template loading completed")

  def connect_load_data_from_iceberg(self, graph_name="test_"):
    """Load data template from Iceberg to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")

    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)

    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)

    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)

    # Select Iceberg as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Iceberg"))

    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()

    # Check if Iceberg keyword exists
    self.sb.wait_for_element_visible('button[aria-label="Iceberg"]', timeout=common_settings.WAIT_RENDER_TIME_OUT)
    LOGGER.info("Found Iceberg template")

    # Click "OPEN IN GSQL EDITOR" button
    self.wait_and_click(WorkgroupLocators.common_button.format("Open In Query Editor"))

    # Wait for redirect to Query Editor page
    self.wait_top_blue_circle_progressbar()

    # Check if successfully redirected to Query Editor
    try:
        # Check if editor content contains Iceberg related text
        editor_content = self.sb.get_text('div[class="cm-content"]')
        if "Iceberg" not in editor_content:
            raise Exception("Iceberg template content not found in Query Editor")
        LOGGER.info("Successfully loaded Iceberg template to Query Editor")
    except Exception as e:
        LOGGER.error(f"Failed to redirect to Query Editor: {str(e)}")
        self.sb.attach_allure_screenshot("gsql_editor_load_failed")
        raise e

    self.sb.attach_allure_screenshot("load Iceberg template successfully screenshot")
    LOGGER.info("Iceberg template loading completed")

  def get_current_workspace_version(self):
    """Get current workspace TigerGraph version"""
    try:
        self.wait_and_click(WorkgroupLocators.workspace_box_button)
        self.sb.attach_allure_screenshot("after_click_workspace_button")

        self.sb.wait_for_element_visible(WorkgroupLocators.workspace_version_label,
                                       timeout=common_settings.WAIT_CREATE_WS_TIME_OUT_200)
        self.sb.attach_allure_screenshot("workspace_version_label_visible")

        version_text = self.sb.get_text(WorkgroupLocators.workspace_version)
        self.sb.attach_allure_screenshot("after_get_version_text")
        LOGGER.info(f"Current workspace version: {version_text}")

        version = version_text.split('-')[0].replace('.', '')
        retry_count = 0
        while not version and retry_count < 10:
            LOGGER.info(f"Waiting for version, attempt {retry_count + 1}/10")
            self.sb.wait_for_element_visible(WorkgroupLocators.workspace_version, timeout=30)
            self.sb.attach_allure_screenshot(f"retry_attempt_{retry_count + 1}")
            retry_count += 1
            version_text = self.sb.get_text(WorkgroupLocators.workspace_version)
            version = version_text.split('-')[0].replace('.', '')

        if version:
            self.wait_and_click(WorkgroupLocators.workspace_button)
            self.sb.attach_allure_screenshot("successful_version_retrieval")
        else:
            LOGGER.error("Failed to get version after 10 retries")
            self.wait_and_click(WorkgroupLocators.workspace_button)
            self.sb.attach_allure_screenshot("get_workspace_version_failed")
        return version
    except Exception as e:
        LOGGER.error(f"Failed to get workspace version: {str(e)}")
        self.sb.attach_allure_screenshot("exception_occurred")
        return ""

  def connect_load_data_from_snowflake(self, graph_name="test_"):
    """Load data template from Snowflake to workspace"""
    self.turn_to_workgroup()
    tg_version = self.get_current_workspace_version()
    self.sb.attach_allure_screenshot("get_workspace_version")
    LOGGER.info("tg_version: " + str(tg_version))
    self.connect_tools("Load Data")

    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)

    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)

    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)

    # Select Snowflake as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Snowflake"))

    if tg_version and tg_version < "420":
          # Wait for loading to complete
        self.wait_top_blue_circle_progressbar()
         # Check if Snowflake keyword exists
        self.sb.wait_for_element_visible('button[aria-label="Snowflake"]', timeout=common_settings.WAIT_RENDER_TIME_OUT)
        LOGGER.info("Found Snowflake template")
        # Click "OPEN IN GSQL EDITOR" button
        self.wait_and_click(WorkgroupLocators.common_button.format("Open In Query Editor"))
        # Wait for redirect to Query Editor page
        self.wait_top_blue_circle_progressbar()
        # Check if successfully redirected to Query Editor
        try:
            # Check if editor content contains Snowflake related text
            editor_content = self.sb.get_text('div[class="cm-content"]')
            if "Snowflake" not in editor_content:
                raise Exception("Snowflake template content not found in Query Editor")
            LOGGER.info("Successfully loaded Snowflake template to Query Editor")
        except Exception as e:
            LOGGER.error(f"Failed to redirect to Query Editor: {str(e)}")
            self.sb.attach_allure_screenshot("gsql_editor_load_failed")
            raise e
        self.sb.attach_allure_screenshot("load Snowflake template successfully screenshot")
        LOGGER.info("Snowflake template loading completed")

    else:

        # Configure Snowflake connection settings
        self.sb.wait_for_element_visible(WorkgroupLocators.load_data_parent_sibling_button.format("Data Source Configuration"),
                                       timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.wait_and_click(WorkgroupLocators.load_data_parent_sibling_button.format("Data Source Configuration"))

        # Generate unique connection alias
        timestamp = int(time.time())
        alias = f"snowflake_alias_{timestamp}"

        # Get Snowflake configuration from test data
        snowflake_config = read_test_data(file="tgcloud_test_data.json").get("snowflake_config")

        # Input connection information
        self.sb.type(WorkgroupLocators.input_field_after_label.format("Connection alias"), alias)
        self.sb.type(WorkgroupLocators.input_field_after_label.format("Snowflake username"), snowflake_config.get("username"))
        self.sb.type(WorkgroupLocators.input_field_after_label.format("Snowflake password"), snowflake_config.get("password"))
        self.sb.type(WorkgroupLocators.snowflake_url, snowflake_config.get("url"))

        # Save Snowflake configuration
        self.wait_and_click(WorkgroupLocators.common_button.format("Connect"))
        self.wait_top_blue_circle_progressbar(visible_timeout=120)
        self.wait_and_click(WorkgroupLocators.select_tables_div)
        # Click the TAG info
        self.wait_and_click(WorkgroupLocators.tag_info)

        self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
        self.sb.attach_allure_screenshot("save snowflake config successfully screenshot")
        self.wait_and_click(WorkgroupLocators.next_button)
        self.wait_and_click(WorkgroupLocators.common_button.format("Schema Designer"))
        self.wait_and_click(WorkgroupLocators.create_new_vertex_button)
        # Configure data mapping
        self.wait_top_blue_circle_progressbar(visible_timeout=5)

        self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
        self.wait_and_click(WorkgroupLocators.quick_map_button)
        self.wait_and_click(WorkgroupLocators.common_button.format("Map all from source"))
        self.wait_and_click(WorkgroupLocators.common_button.format("Save"))

        # self.wait_and_click(WorkgroupLocators.common_button.format("Save"))
        self.sb.attach_allure_screenshot("config map successfully screenshot")
        self.wait_and_click(WorkgroupLocators.common_button.format("Next"))
        self.wait_and_click(WorkgroupLocators.common_button.format("Confirm"))
        self.check_save_schema_job(timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)

        self.sb.type('input[placeholder="Search Job or File Name"]', "TAG" + Keys.ENTER)
        self.wait_top_blue_circle_progressbar(visible_timeout=30)
        # Wait for loading to complete
        self.wait_top_blue_circle_progressbar()
        self.sb.wait_for_text_visible("FINISHED", timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)

        job_status = self.sb.get_text("span[title='FINISHED']")
        assert job_status == "FINISHED", f"Job status is {job_status}, expected FINISHED"
        LOGGER.info("Load data job completed successfully with status: FINISHED")
        self.sb.attach_allure_screenshot("load from snowflake successfully screenshot")

  def connect_load_data_from_spark(self, graph_name="test_"):
    """Load data template from Spark to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")

    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)

    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)

    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)

    # Select Spark as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Spark"))

    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()

    # Check if Spark keyword exists
    self.sb.wait_for_element_visible('button[aria-label="Spark"]', timeout=common_settings.WAIT_RENDER_TIME_OUT)
    LOGGER.info("Found Spark template")

    # Click "OPEN IN GSQL EDITOR" button
    self.wait_and_click(WorkgroupLocators.common_button.format("Open In Query Editor"))

    # Wait for redirect to Query Editor page
    self.wait_top_blue_circle_progressbar()

    # Check if successfully redirected to Query Editor
    try:
        # Check if editor content contains Spark related text
        editor_content = self.sb.get_text('div[class="cm-content"]')
        if "Spark" not in editor_content:
            raise Exception("Spark template content not found in Query Editor")
        LOGGER.info("Successfully loaded Spark template to Query Editor")
    except Exception as e:
        LOGGER.error(f"Failed to redirect to Query Editor: {str(e)}")
        self.sb.attach_allure_screenshot("gsql_editor_load_failed")
        raise e

    self.sb.attach_allure_screenshot("load Spark template successfully screenshot")
    LOGGER.info("Spark template loading completed")

  def connect_load_data_from_kafka(self, graph_name="test_"):
    """Load data template from Kafka to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")

    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)

    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)

    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)

    # Click "Show More" button first
    # Use data-baseweb attribute to locate button more reliably
    self.wait_and_click('button[data-baseweb="button"] span:contains("show more")')
    LOGGER.info("Clicked Show More button")

    # Select Kafka as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Kafka"))

    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()

    # Check if Kafka keyword exists
    self.sb.wait_for_element_visible('button[aria-label="Kafka"]', timeout=common_settings.WAIT_RENDER_TIME_OUT)
    LOGGER.info("Found Kafka template")

    # Click "OPEN IN GSQL EDITOR" button
    self.wait_and_click(WorkgroupLocators.common_button.format("Open In Query Editor"))

    # Wait for redirect to Query Editor page
    self.wait_top_blue_circle_progressbar()

    # Check if successfully redirected to Query Editor
    try:
        # Check if editor content contains Kafka related text
        editor_content = self.sb.get_text('div[class="cm-content"]')
        if "Kafka" not in editor_content:
            raise Exception("Kafka template content not found in Query Editor")
        LOGGER.info("Successfully loaded Kafka template to Query Editor")
    except Exception as e:
        LOGGER.error(f"Failed to redirect to Query Editor: {str(e)}")
        self.sb.attach_allure_screenshot("gsql_editor_load_failed")
        raise e

    self.sb.attach_allure_screenshot("load Kafka template successfully screenshot")
    LOGGER.info("Kafka template loading completed")

  def connect_load_data_from_postgres(self, graph_name="test_"):
    """Load data template from Postgres to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")

    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)

    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)

    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)

    # Click "Show More" button first
    # Use data-baseweb attribute to locate button more reliably
    self.wait_and_click('button[data-baseweb="button"] span:contains("show more")')
    LOGGER.info("Clicked Show More button")

    # Select Postgres as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("Postgres"))

    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()

    # Check if Postgres keyword exists
    self.sb.wait_for_element_visible('button[aria-label="Postgres"]', timeout=common_settings.WAIT_RENDER_TIME_OUT)
    LOGGER.info("Found Postgres template")

    # Click "OPEN IN GSQL EDITOR" button
    self.wait_and_click(WorkgroupLocators.common_button.format("Open In Query Editor"))

    # Wait for redirect to Query Editor page
    self.wait_top_blue_circle_progressbar()

    # Check if successfully redirected to Query Editor
    try:
        # Check if editor content contains Postgres related text
        editor_content = self.sb.get_text('div[class="cm-content"]')
        if "PostgreSQL" not in editor_content:
            raise Exception("PostgreSQL template content not found in Query Editor")
        LOGGER.info("Successfully loaded PostgreSQL template to Query Editor")
    except Exception as e:
        LOGGER.error(f"Failed to redirect to Query Editor: {str(e)}")
        self.sb.attach_allure_screenshot("gsql_editor_load_failed")
        raise e

    self.sb.attach_allure_screenshot("load PostgreSQL template successfully screenshot")
    LOGGER.info("PostgreSQL template loading completed")

  def connect_load_data_from_bigquery(self, graph_name="test_"):
    """Load data template from BigQuery to workspace"""
    self.turn_to_workgroup()
    self.connect_tools("Load Data")

    # Create new graph
    self.wait_and_click(WorkgroupLocators.add_load_data)
    self.wait_and_click(WorkgroupLocators.graph_select)
    self.wait_and_click(WorkgroupLocators.create_new_graph)

    # Generate unique graph name with timestamp
    timestamp = int(time.time())
    graph_name = graph_name + str(timestamp)
    LOGGER.info("create graph_name: " + graph_name)

    # Input graph name and complete creation
    self.sb.type(WorkgroupLocators.input_new_graph_name, graph_name)
    self.wait_and_click(WorkgroupLocators.common_button.format("Done"))
    self.sb.attach_allure_screenshot("create new graph successfully screenshot")
    self.wait_top_blue_circle_progressbar(visible_timeout=5)

    # Click "Show More" button first
    # Use data-baseweb attribute to locate button more reliably
    self.wait_and_click('button[data-baseweb="button"] span:contains("show more")')
    LOGGER.info("Clicked Show More button")

    # Select BigQuery as data source
    self.wait_and_click(WorkgroupLocators.common_div_button.format("BigQuery"))

    # Wait for loading to complete
    self.wait_top_blue_circle_progressbar()

    # Check if BigQuery keyword exists
    self.sb.wait_for_element_visible('button[aria-label="BigQuery"]', timeout=common_settings.WAIT_RENDER_TIME_OUT)
    LOGGER.info("Found BigQuery template")

    # Click "OPEN IN GSQL EDITOR" button
    self.wait_and_click(WorkgroupLocators.common_button.format("Open In Query Editor"))

    # Wait for redirect to Query Editor page
    self.wait_top_blue_circle_progressbar()

    # Check if successfully redirected to Query Editor
    try:
        # Check if editor content contains BigQuery related text
        editor_content = self.sb.get_text('div[class="cm-content"]')
        if "BigQuery" not in editor_content:
            raise Exception("BigQuery template content not found in Query Editor")
        LOGGER.info("Successfully loaded BigQuery template to Query Editor")
    except Exception as e:
        LOGGER.error(f"Failed to redirect to Query Editor: {str(e)}")
        self.sb.attach_allure_screenshot("gsql_editor_load_failed")
        raise e

    self.sb.attach_allure_screenshot("load BigQuery template successfully screenshot")
    LOGGER.info("BigQuery template loading completed")

  def create_solution_workspace(self, workspace_name="solution_workspace"):
    """Create a new solution workspace with Mule Account Detection template"""
    LOGGER.info("before create solution workspace, check if WG existed")
    self.check_workgroup_and_delete_if_existed(work_group_name="solution_workgroup", workspace_name="solution_workspace")
    # Create new workgroup
    self.create_new_workgroup(workgroup_name="solution_workgroup")

    # Create new workspace
    self.sb.type(WorkgroupLocators.input_WS_name, workspace_name)

    # Type TG version if specified
    tg_version = self.get_tgcloud_test_data_value("tg_version")
    if tg_version != "":
        self.sb.type(WorkgroupLocators.input_TG_version, tg_version)
    else:
        LOGGER.info("use default TG version")

    # Select workspace details
    self.wait_and_click(WorkgroupLocators.edit_WS_detail)
    self.sb.find_element(WorkgroupLocators.select_WS_size).click()
    self.wait_and_click(WorkgroupLocators.select_TG_size)
    self.sb.attach_allure_screenshot("edit WS size")
    self.wait_and_click(WorkgroupLocators.save_button)

    # Select Mule Account Detection solution
    try:
        # Wait for solution cards to be visible
        self.sb.wait_for_element_visible(WorkgroupLocators.mule_account_detection,
                                       timeout=common_settings.WAIT_RENDER_TIME_OUT)

        # Click on Mule Account Detection card
        self.wait_and_click(WorkgroupLocators.mule_account_detection)
        LOGGER.info("Selected Mule Account Detection solution")
        self.sb.attach_allure_screenshot("selected mule account detection")
    except Exception as e:
        LOGGER.error("Failed to select Mule Account Detection solution")
        self.sb.attach_allure_screenshot("failed to select solution")
        raise e

    # Create workspace
    LOGGER.info("create solution workspace:" + workspace_name)
    self.sb.type(WorkgroupLocators.input_RW_DB_name, workspace_name)
    self.wait_and_click(WorkgroupLocators.create_button)

    # Wait for creation to complete
    self.sb.wait_for_element_not_visible(WorkgroupLocators.create_button,
                                        timeout=common_settings.WAIT_CLICK_TIME_OUT)
    self.sb.attach_allure_screenshot("begin to create WS")

    LOGGER.info("wait_horizon_progressbar: not_visible_timeout=" +
                str(common_settings.WAIT_CREATE_WS_TIME_OUT))
    self.wait_horizon_progressbar(not_visible_timeout=common_settings.WAIT_CREATE_WS_TIME_OUT)

    # Wait for workspace to be ready
    try:
        LOGGER.info("prepare to check_congratuations_pop_windows")
        self.check_congratuations_pop_windows(timeout=common_settings.WAIT_CREATE_WS_TIME_OUT,
                                            is_RW_WS=True)

        # Additional check to ensure workspace is active
        self.sb.wait_for_element_visible(WorkgroupLocators.common_span_button.format("Connect"),
                                       timeout=common_settings.WAIT_RESUME_WS_TIME_OUT)
        LOGGER.info("Workspace is active and ready")

    except Exception as e:
        LOGGER.error("Workspace creation failed or workspace is not active")
        self.sb.attach_allure_screenshot("workspace_creation_failed")
        raise Exception("finally wait_pop_windows and assert WS is not active")

    self.sb.attach_allure_screenshot("create solution WS done")

  def verify_mule_detection_query(self):
    """Verify Mule Account Detection query execution in Query Editor"""
    self.turn_to_workgroup(work_group_name="solution_workgroup")
    self.connect_tools("Query Editor")
    self.close_tips_panel()
    # Click Queries tab
    try:
        self.wait_and_click(WorkgroupLocators.common_button.format("Got it!"))
    except:
        LOGGER.info("No 'Got it!' button found, skipping")
    self.wait_and_click('button[role="tab"]:contains("Queries")')
    LOGGER.info("Clicked Queries tab")

    # Wait for and expand Mule_Account_Detection folder
    try:
        # Wait for tree item to be visible
        self.sb.wait_for_element_visible(WorkgroupLocators.mule_detection_folder,
                                       timeout=common_settings.WAIT_RENDER_TIME_OUT)

        # Click to expand
        self.wait_and_click(WorkgroupLocators.mule_detection_folder)
        LOGGER.info("Expanded Mule_Account_Detection folder")
        self.sb.attach_allure_screenshot("expanded mule account detection folder")
    except Exception as e:
        LOGGER.error("Failed to expand Mule_Account_Detection folder")
        self.sb.attach_allure_screenshot("failed to expand folder")
        raise e

    try:
        self.wait_and_click(WorkgroupLocators.common_div_button.format("account_account_with_weights"))
        LOGGER.info("Selected account_account_with_weights query")
        self.sb.attach_allure_screenshot("selected account_account_with_weights query")
    except Exception as e:
        LOGGER.error("Failed to select account_account_with_weights query")
        self.sb.attach_allure_screenshot("failed to select query")
        raise e

    # Click Run Query button
    try:
        self.wait_and_click(WorkgroupLocators.run_query_button)
        LOGGER.info("Clicked Run Query button")

        # Wait for and verify query results
        self.sb.wait_for_element_visible(WorkgroupLocators.query_results_panel,
                                       timeout=common_settings.WAIT_RENDER_TIME_OUT)

        # Get query results text
        results_text = self.sb.get_text(WorkgroupLocators.query_results_panel + ' pre')

        # Verify results contain expected text
        if '"Status": "Account_Account Edges Inserted "' not in results_text:
            raise Exception("Expected status text not found in query results")
        LOGGER.info("Query results verification successful")
        self.sb.attach_allure_screenshot("query results verified")
    except Exception as e:
        LOGGER.error(f"Query execution or verification failed: {str(e)}")
        self.sb.attach_allure_screenshot("query verification failed")
        raise e

  def check_shared_file_in_gsql_editor(self):
    """Check shared file in Query Editor"""
    self.turn_to_workgroup()
    # Enter Query Editor
    self.connect_tools("Query Editor")

    try:
        self.wait_and_click(WorkgroupLocators.common_button.format("Got it!"))
    except:
        LOGGER.info("Got it button not found, continuing...")

    # Click the down arrow to expand file list
    self.wait_and_click(WorkgroupLocators.common_span_button.format("Shared Folder"))

    # Check if test_share_file exists
    shared_file = WorkgroupLocators.common_span_button.format("test_share_file")
    if not self.sb.is_element_present(shared_file):
        raise Exception("Shared file 'test_share_file' not found")

    # Check file content
    self.wait_and_click(shared_file)

    # Wait for editor content to load
    editor_content = WorkgroupLocators.input_cmd
    self.sb.wait_for_element_present(editor_content)

    # Verify if test_share_file content is present
    if not self.sb.is_element_present('//span[contains(text(),"just_for_test_share_file")]'):
        raise Exception("test_share_file content not found in editor")

    self.sb.attach_allure_screenshot("shared file check result")

  def check_gsql_tutorials_files(self):
    """
    Check GSQL Tutorials files in GSQL Editor
    """
    try:
        LOGGER.info("Start checking GSQL Tutorials files...")
        self.turn_to_workgroup()

        # Enter GSQL Editor
        LOGGER.info("Entering GSQL Editor...")
        self.connect_tools("Query Editor")

        try:
            self.wait_and_click(WorkgroupLocators.common_button.format("Got it!"))
        except:
            LOGGER.info("Got it button not found, continuing...")

        # Click GSQL Tutorials
        LOGGER.info("Clicking GSQL Tutorials...")
        try:
            self.wait_and_click(WorkgroupLocators.common_span_button.format("GSQL Tutorials"))
        except Exception as e:
            LOGGER.error(f"Failed to click GSQL Tutorials: {str(e)}")
            raise Exception("GSQL Tutorials button not found or not clickable")

        # Click Financial
        LOGGER.info("Clicking Financial...")
        try:
            self.wait_and_click(WorkgroupLocators.common_span_button.format("Financial"))
        except Exception as e:
            LOGGER.error(f"Failed to click Financial: {str(e)}")
            raise Exception("Financial button not found or not clickable")

        # Check and click 00_schema.gsql
        LOGGER.info("Checking 00_schema.gsql...")
        try:
            self.wait_and_click(WorkgroupLocators.common_span_button.format("00_schema.gsql"))
            self.sb.attach_allure_screenshot("00_schema.gsql file screenshot")
            LOGGER.info("00_schema.gsql file found successfully")
        except Exception as e:
            LOGGER.error(f"Failed to check 00_schema.gsql: {str(e)}")
            raise Exception("00_schema.gsql file check failed")

        # Check and click 01_load.gsql
        LOGGER.info("Checking 01_load.gsql...")
        try:
            self.wait_and_click(WorkgroupLocators.common_span_button.format("01_load.gsql"))
            self.sb.attach_allure_screenshot("01_load.gsql file screenshot")
            LOGGER.info("01_load.gsql file found successfully")
        except Exception as e:
            LOGGER.error(f"Failed to check 01_load.gsql: {str(e)}")
            raise Exception("01_load.gsql file check failed")

        # Take screenshot for verification
        LOGGER.info("Taking screenshot of GSQL Tutorials files check result...")
        self.sb.attach_allure_screenshot("GSQL Tutorials files check result")

        LOGGER.info("GSQL Tutorials files check completed successfully")

    except Exception as e:
        LOGGER.error(f"Error during GSQL Tutorials files check: {str(e)}")
        self.sb.attach_allure_screenshot("GSQL Tutorials files check failed")
        raise Exception(f"GSQL Tutorials files check failed: {str(e)}")



  def check_cypher_tutorials_files(self):
    """Check Cypher Tutorials files including c0_schema.cypher and c0_load.cypher"""
    try:
        # Enter GSQL Editor
        self.turn_to_workgroup()
        LOGGER.info("Entering GSQL Editor...")
        self.connect_tools("Query Editor")

        try:
            self.wait_and_click(WorkgroupLocators.common_button.format("Got it!"))
        except:
            LOGGER.info("Got it button not found, continuing...")

        # Click Cypher Tutorials
        LOGGER.info("Clicking Cypher Tutorials...")
        try:
            self.wait_and_click(WorkgroupLocators.common_span_button.format("Cypher Tutorials"))
        except Exception as e:
            LOGGER.error(f"Failed to click Cypher Tutorials: {str(e)}")
            raise Exception("Cypher Tutorials button not found or not clickable")

        # Check and click c0_schema.cypher
        LOGGER.info("Checking c0_schema.cypher...")
        try:
            self.wait_and_click(WorkgroupLocators.common_span_button.format("c0_schema.cypher"))
            self.sb.attach_allure_screenshot("c0_schema.cypher file screenshot")
            LOGGER.info("c0_schema.cypher file found successfully")
        except Exception as e:
            LOGGER.error(f"Failed to check c0_schema.cypher: {str(e)}")
            raise Exception("c0_schema.cypher file check failed")

        # Check and click c0_load.cypher
        LOGGER.info("Checking c0_load.cypher...")
        try:
            self.wait_and_click(WorkgroupLocators.common_span_button.format("c0_load.cypher"))
            self.sb.attach_allure_screenshot("c0_load.cypher file screenshot")
            LOGGER.info("c0_load.cypher file found successfully")
        except Exception as e:
            LOGGER.error(f"Failed to check c0_load.cypher: {str(e)}")
            raise Exception("c0_load.cypher file check failed")

        # Take screenshot for verification
        LOGGER.info("Taking screenshot of Cypher Tutorials files check result...")
        self.sb.attach_allure_screenshot("Cypher Tutorials files check result")

        LOGGER.info("Cypher Tutorials files check completed successfully")

    except Exception as e:
        LOGGER.error(f"Error during Cypher Tutorials files check: {str(e)}")
        self.sb.attach_allure_screenshot("Cypher Tutorials files check failed")
        raise Exception(f"Cypher Tutorials files check failed: {str(e)}")



  def connect_gsql_profile(self):
    self.turn_to_workgroup()
    tg_version = self.get_current_workspace_version()
    self.sb.attach_allure_screenshot("get_workspace_version")
    if tg_version and tg_version < "420":
        pytest.skip("Skip this test case because DB version should >= 4.2.0")
    self.sb.attach_allure_screenshot("get_workspace_version")
    LOGGER.info("tg_version: " + str(tg_version))
    if tg_version and tg_version < "420":
        LOGGER.info("tg_version is less than 420, skip connect gsql profile")

    # Enter Query Editor
    self.connect_tools("Query Editor")

    try:
        self.wait_and_click(WorkgroupLocators.common_button.format("Got it!"))
    except:
        LOGGER.info("Got it button not found, continuing...")
    self.wait_and_click(WorkgroupLocators.graph_select)
    graph_elements = self.sb.find_elements(WorkgroupLocators.graph_option)
    if len(graph_elements) > 0:
        graph_name = graph_elements[1].text
    else:
        graph_name = "test_" + str(random.randint(1000, 9999))
    # Create new file
    self.wait_and_click(WorkgroupLocators.create_new_file)
    self.wait_and_click(WorkgroupLocators.common_button.format("GSQL File"))
    query = f"""USE GRAPH {graph_name}
    CREATE OR REPLACE DISTRIBUTED QUERY vertexCount() FOR GRAPH {graph_name} {{
      PRINT "new_query_2 works!";
    """
    self.sb.type(WorkgroupLocators.input_cmd, query)
    # Run query
    self.wait_and_click(WorkgroupLocators.run_all_button)
    query = f"""    install query vertexCount
    run query -profile basic vertexCount()
    """
    self.wait_and_click(WorkgroupLocators.create_new_file)
    self.wait_and_click(WorkgroupLocators.common_button.format("GSQL File"))
    self.sb.type(WorkgroupLocators.input_cmd, query)
    # Run query
    self.wait_and_click(WorkgroupLocators.run_all_button)
    # Get message logs content
    self.sb.wait_for_element_visible(WorkgroupLocators.profile_button, timeout=60)
    self.wait_and_click(WorkgroupLocators.profile_button)
    try:
        self.sb.wait_for_element_visible(WorkgroupLocators.profile_overview, timeout=60)
    except:
        self.sb.attach_allure_screenshot("Profile Overview not found")
        raise Exception("Profile Overview not found in the page, query profiling failed")
    self.sb.attach_allure_screenshot("Profile Overview found")

  def create_vertex_in_schema(self):
    """Create a vertex in design schema"""
    self.turn_to_workgroup()
    # Enter Design Schema
    self.connect_tools("Design Schema")
    # Handle Got it button if present
    try:
        self.wait_and_click(WorkgroupLocators.common_button.format("Got it!"))
    except:
        LOGGER.info("Got it button not found, continuing...")
    # Create a new graph with random name
    try:
        # Click graph select dropdown
        self.wait_and_click(WorkgroupLocators.graph_select)
        LOGGER.info("Clicked graph select dropdown")
        self.wait_and_click(WorkgroupLocators.create_new_graph)
        graph_name = "test_" + str(random.randint(1000, 9999))
        # Try to click Confirm button if exists
        try:
            self.wait_and_click(WorkgroupLocators.confirm_button)
            LOGGER.info("Clicked Confirm button")
        except:
            LOGGER.info("Confirm button not found, continuing...")
        self.sb.type(WorkgroupLocators.graph_name_input, graph_name)
        self.wait_and_click(WorkgroupLocators.common_button.format("Done"))

        # Wait for graph creation to complete
        self.wait_horizon_progressbar()

        LOGGER.info(f"Created new graph: {graph_name}")
        self.sb.attach_allure_screenshot("graph created successfully")
    except Exception as e:
        self.sb.attach_allure_screenshot("create graph failed")
        raise Exception(f"Failed to create graph: {str(e)}")
    try:
        # Click Create Vertex button
        self.wait_and_click(WorkgroupLocators.create_new_vertex_button)
        vertex_name = "test_vertex_" + str(int(time.time()))
        self.sb.type('input[placeholder="Enter vertex name"]', vertex_name)

        self.wait_and_click(WorkgroupLocators.vertex_image_button)
        self.sb.type(WorkgroupLocators.search_input, "test")
        self.sb.clear(WorkgroupLocators.search_input)
        self.wait_and_click(WorkgroupLocators.save_schema_button)
        self.sb.wait_for_text_visible("Successfully saved the schema style")
    except Exception as e:
        self.sb.attach_allure_screenshot("test prevent event propagation when deleting search text failed")
        raise Exception(f"Failed to test prevent event propagation when deleting search text: {str(e)}")

  def verify_duplicate_vertex_error(self):
    try:
        self.turn_to_workgroup()
        # Only connect to design schema, without creating vertex
        self.connect_tools("Design Schema")
        try:
          self.wait_and_click(WorkgroupLocators.common_button.format("Got it!"))
        except:
          LOGGER.info("Got it button not found, continuing...")
        # Create new graph
        LOGGER.info("Start creating new graph")
        self.sb.wait_for_element_visible(WorkgroupLocators.graph_select)
        self.wait_and_click(WorkgroupLocators.graph_select)
        self.wait_and_click(WorkgroupLocators.create_new_graph)
        try:
            self.wait_and_click(WorkgroupLocators.confirm_button)
            LOGGER.info("Clicked Confirm button")
        except:
            LOGGER.info("Confirm button not found, continuing...")
        graph_name = "test_" + str(random.randint(1000, 9999))
        self.sb.type(WorkgroupLocators.graph_name_input, graph_name)
        self.wait_and_click(WorkgroupLocators.common_button.format("Done"))

        # Create first vertex
        LOGGER.info("Creating first vertex")
        self.wait_and_click(WorkgroupLocators.create_new_vertex_button)
        vertex_name = "test_vertex_" + str(int(time.time()))
        self.sb.type(WorkgroupLocators.vertex_name_input, vertex_name)

        self.wait_and_click(WorkgroupLocators.save_schema_button)
        self.sb.wait_for_text_visible("Successfully saved the schema style")
        self.click_center_position()  # Click empty space to blur focus
        # Try to create vertex with same name
        LOGGER.info("Attempting to create vertex with same name")
        self.wait_and_click(WorkgroupLocators.create_new_vertex_button)
        self.sb.type(WorkgroupLocators.vertex_name_input, vertex_name)
        # Verify error message

        expected_error = f'Vertex or edge name "{vertex_name}" cannot be duplicated in one graph'
        self.sb.wait_for_text_visible(expected_error, timeout=common_settings.WAIT_RENDER_TIME_OUT)
        self.sb.attach_allure_screenshot("duplicate vertex error message")
    except Exception as e:
        self.sb.attach_allure_screenshot("verify_duplicate_vertex_error failed")
        raise Exception(f"Failed to verify duplicate vertex error: {str(e)}")

  def verify_query_result(self):
     """Verify Mule Account Detection query execution in Query Editor"""
     self.turn_to_workgroup(work_group_name="solution_workgroup")
     self.connect_tools("Query Editor")
     self.close_tips_panel()

     # Handle Got it button if present
     try:
         self.wait_and_click(WorkgroupLocators.got_it_button)
     except:
         LOGGER.info("No 'Got it!' button found, skipping")

     # Click Queries tab
     self.wait_and_click(WorkgroupLocators.queries_tab)
     LOGGER.info("Clicked Queries tab")

     # Wait for and click Mule Account Detection folder
     try:
         # Wait for folder to be visible
         self.sb.wait_for_element_visible(WorkgroupLocators.mule_detection_folder,
                                        timeout=common_settings.WAIT_RENDER_TIME_OUT)

         # Click to select
         self.wait_and_click(WorkgroupLocators.mule_detection_folder)
         LOGGER.info("Selected Mule Account Detection folder")
         self.sb.attach_allure_screenshot("selected mule account detection folder")
     except Exception as e:
         LOGGER.error("Failed to select Mule Account Detection folder")
         self.sb.attach_allure_screenshot("failed to select folder")
         raise e

     try:
         # Select the insights_get_net_gain_numbers_and_percentages query
         self.wait_and_click(WorkgroupLocators.common_div_button.format("insights_get_net_gain_numbers_and_percentages"))
         LOGGER.info("Selected insights_get_net_gain_numbers_and_percentages query")
         self.sb.attach_allure_screenshot("selected insights query")
     except Exception as e:
         LOGGER.error("Failed to select insights_get_net_gain_numbers_and_percentages query")
         self.sb.attach_allure_screenshot("failed to select query")
         raise e

     # Run the query
     self.wait_and_click(WorkgroupLocators.run_query_button)
     LOGGER.info("Clicked Run Query button")

     # Wait for results and verify JSON format
     try:
         # Wait for results panel to be visible
         self.sb.wait_for_element_visible(WorkgroupLocators.query_results_panel,
                                        timeout=common_settings.WAIT_RENDER_TIME_OUT)
         result_element = self.sb.find_element(WorkgroupLocators.query_results_panel)
         cm_line_elements = result_element.find_elements("css selector", WorkgroupLocators.cm_line_elements)
         if len(cm_line_elements) > 1:
             LOGGER.info("Query results are displayed in multiple lines")
             self.sb.attach_allure_screenshot("multi line query results")
         else:
             LOGGER.error("Query results are displayed in a single line, which is user unfriendly")
             self.sb.attach_allure_screenshot("single line query results")
             raise Exception("Query results are displayed in a single line, which is user unfriendly")

         self.sb.attach_allure_screenshot("query execution results")
     except Exception as e:
         LOGGER.error(f"Failed to verify query results: {str(e)}")
         self.sb.attach_allure_screenshot("query results verification failed")
         raise e


