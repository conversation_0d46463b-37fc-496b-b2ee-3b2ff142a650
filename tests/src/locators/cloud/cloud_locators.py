"""
The locators for Cloud elements, update the elements
when we have any major or minor UI changes.
"""


class LogInLocators:
    welcome_p = "//p[contains(.,'continue to tgcloud-dev-lobby')]"
    sign_up_btn = "//button[text()='Sign up']"
    login_btn = "//button[text()='Log in']"
    login_title = "Log in | TigerGraph Savanna"
    # login with ORG
    login_with_org_btn = "//a[text()='Login with organization']"
    org_title_h1 = "//h1[text()='Enter Organization']"
    org_input = "#organizationName"
    login_org_p = "//p[text()='Log in to {} to continue to tgcloud-dev.']"
    continue_btn = "//button[text()='Continue']"
    continue_login_btn = "//button[@data-action-button-primary='true' and text()='Continue']"
    user_input = "#username"
    email_input = "#email"
    password_input = "#password"
    org_name_invalid = "//span[contains(.,'The organization you entered is invalid')]"
    user_password_invalid = "//span[contains(.,'Wrong email or password')]"
    forget_password = "//a[text()='Forgot password?']"
    tips_reset_password = "//p[contains(., 'Enter your email address and we will send you instructions to reset your password if you have an account using password in this organization.')]"
    check_your_email = "//h1[text()='Check Your Email']"
    accept_invation_tips = "//h1[contains(.,'Accept your invitation to sign up')]"

    #register
    sign_up_link = "//a[text()='Sign up']"
    log_in_link = "//a[text()='Log in']"
    verify_email = "//div[text()='Great! Now verify your email']"
    verify_email_link = "//a[text()='Verify my email address']"
    accept_button = "//button[text()='Accept']"
    decline_button = "//button[text()='Decline']"
    accept_invation_link = "//a[text()='Accept Invitation']"
    start_tutorials = "//div[text()='Start your journey with our tutorials']"
    register_error_text = "//span[contains(., '{}')]"
    survey_title = "//h1[text()='Get started with TigerGraph Savanna']"
    survey_firstname = "//input[@name='firstName']"
    survey_lastname = "//input[@name='lastName']"
    survey_company = "//input[@name='company']"
    survey_role = "//label[text()='Your Role']/../../div[3]/div[@data-baseweb='select']"
    role_business = "//div[text()='Business Executive']"
    survey_how = "//label[text()='Your Role']/../../div[4]/div[@data-baseweb='select']"
    how_test = "//div[text()='Testing and Evaluation']"
    input_antiMoney = "//input[@value='Fraud']/../../label[2]"
    survey_submit = "//button[@type='submit']"


    #register --- maildrop
    current_register_email_is_empty = "//div[contains(.,'@maildrop.cc is currently empty.')]"
    mail_in_loading = "//div[contains(.,'maildrop.cc') and contains(.,'Loading ')]"
    refresh_mail_button_first = "//span[text()='Refresh Mailbox']"
    refresh_mail_button_second = "//span[text()='Refresh']"
    view_mailbox = "//span[text()='View Mailbox']/.."
    email_content = "//p[contains(.,'If you are having trouble with the button above, copy and paste this URL to your web browser:')]"
    email_value = "//input[@placeholder='view-this-mailbox']"
    direct_email_verify_url = "{}/span[1]".format(email_content)
    direct_email_accept_url = "{}/font[1]".format(email_content)

    # login with lobby
    lobby_continue_btn = "//button[text()='Continue' and @data-action-button-primary='true']"

    # login with okta sso
    continue_okta_btn = "//button[contains(.,'Continue with Okta')]"
    okta_signin_main = "#okta-sign-in"
    okta_login_username_input = "#okta-signin-username"
    okta_login_password_input = "#okta-signin-password"
    okta_login_submit = "#okta-signin-submit"

    # login with aad sso
    continue_aad_btn = "//button[contains(.,'Continue with AAD')]"
    microsoft_login_header = "#loginHeader"
    microsoft_email_input = "//input[@type='email']"
    next_btn = "//input[@type='submit']"
    microsoft_pwd_input = "//input[@type='password']"
    stay_signed_in_div = "//div[text()='Stay signed in?']"

    # login with Google
    google_continue_btn = "//button[contains(.,'Continue with Google')]"
    google_signin_div = "#headingText"
    google_user_input = "#identifierId"
    google_next_btn = "//button[contains(.,'Next')]"
    google_pwd_input = "//input[@name='password']"


class LogOutLocators:
    account_icon = "//img[contains(@src,'account')]"
    logout_span = "//span[contains(.,'Sign out')]"
    login_page_div = "//h1[text()='Welcome']"

class BillingLocators:
    remarning_credit_balance = "//div[text()='Credit Balance']/../div[text()='$ {}.00']"


class CloudEndpoint:
    cluster_endpoint = "app/clusters"
    tools_endpoint = "app/tools"


class CloudHomeLocators:
    tgcloud_icon="//a[@aria-label='Home']"
    org_name_div = tgcloud_icon + "/following-sibling::div[text()='{}']"
    org_logo = "//img[@src='{}']"

    # account info
    user_info_span = "//button/span[contains(.,'{}')]"
    account_info_div = "//div[@data-baseweb='popover']"
    account_info_email = account_info_div + "//div[text()='{}']"
    account_info_role = account_info_div + "//div[text()='{}']/following-sibling::div"
    account_info_name = account_info_div + "//div[text()='{}']/preceding-sibling::div"
    user_profile_span = account_info_div + "//span[text()='User profile']"

    # user profile
    user_profile_page = "//div[@data-baseweb='typo-headinglarge' and text()='User Profile']"
    user_info_div = "//div[@data-baseweb='typo-headingsmallbold']/following-sibling::div[text()='{}']"
    user_name_div = "//div[@data-baseweb='typo-headingsmallbold']/following-sibling::div/div[text()='{}']"

    # ORG List
    rename_btn = "//div[@aria-selected='true' and text()='{}']/ancestor::div[@data-baseweb='select']/following-sibling::button"
    edit_orgName_div = "//div[text()='Edit Organization Name']/ancestor::div[@role='dialog']"

    org_list_dropdown_btn = "//button//*[name()='svg' and @title='Triangle Down']"
    switch_org_btn = "//div[@role='menuitem' and contains(.,'Switch Organization')]"
    go_to_legacy_cloud = "//div[@role='menuitem' and contains(.,'Go to TigerGraph Cloud Classic')]"
    org_panel_h5 = "//h5[@data-baseweb='typo-headingsmallbold' and text()='Switch Organization']"
    org_list_tr = org_panel_h5 + "/following-sibling::div//tr"
    org_option_li = org_list_tr + "[contains(.,'{}')]"
    display_name_tr = org_option_li + "/td[2]"
    current_org_label = "//div[text()='Current']/ancestor::td[1]"
    close_org_panel_btn = "//button[@aria-label='Close']"

    #legacy cloud
    my_cluster = "//div[text()='My Clusters']"
    try_cloud_v4 = "//button[text()='Try TigerGraph Savanna']"

    # account
    okta_login_circle_progressbar = 'div > i'
    account_icon = 'svg[title="Triangle Down"]'
    logout_button = "//span[contains(.,'Logout')]"
    common_div_button = '//div[normalize-space(text())="{0}"]'
    common_span_button = '//span[normalize-space(text())="{0}"]'
    tips_button = '//button[contains(.,"Got it!")]'
    guide_button = '//div[contains(text(),"Never Show")]'

class CloudNavigationLocators:
    Admin_span = "//li//span[text()='Admin']"
    ORG_Setting_span = "//li//span[text()='Setting']"
    under_admin_manage_span = "//li//span[text()='{}']"

class CloudInviteUsersLocators:
    # invite the new user
    invite_users = "//button/span[text()='Invite Users']"
    add_email_box = "//input[@placeholder='Add email']"
    add_button = "//button[text()='Add']"
    invite_button = "//button[text()='Invite']"
    cancel_button = "//button[text()='Cancel']"
    org_admin = "//input[@value='super-admins']/.."
    org_member = "//input[@value='users']/.."
    user_exists = "//div[text()='This user is already in your organization']"

class CloudORGSettingLocators:
    org_setting_div = "//div[@data-baseweb='tab-list']//button[text()='Organization Setting']"
    org_name_input = "//span[contains(., 'Organization Name')]/following-sibling::div//input"
    display_name_input = "//span[contains(., 'Display Name')]/following-sibling::div//input"
    logo_url_input = "//span[contains(., 'Organization Logo Url')]/following-sibling::div//input"
    update_btn = "//button[text()='Update']"
    successful_update_div = "//div[@role='alert' and contains(.,'Update organization successfully.')]"

class CloudClusterAccessToolsLocators:
    # tool name keys
    gst = 'GraphStudio'
    ins = 'TigerGraph Insights'
    gsql = 'GSQL Shell'
    adp = 'Admin Portal'
    gql = 'GraphQL'
    mlwb = 'Machine Learning Workbench'

    # tools naming convert dict
    convert = {
        gst: "graphstudio",
        ins: "insights",
        gsql: "gsqlshell",
        adp: "adminportal",
        gql: "graphql"
    }
    # tools naming dict
    tool_div_selector_for = "//div[text()='{}']"
    view_more_tools_div = "//div[text()='View more tools']"
    cluster_name_tools_div = "//div[@data-baseweb='typo-headinglarge' and contains(.,'{}')]"

    # tool card divs
    tools_card = "//section[@data-baseweb='card']//div[@data-baseweb='typo-headingmenu' and contains(.,'{}')]/ancestor::a"
    # tool card div match patterns for according tool name
    match_pattern_tools = {
        gst: "Design, develop, map and load",
        ins: "applications",
        gsql: "GSQL",
        adp: "system",
        gql: "GraphQL",
        mlwb: "machine learning"
    }

    # logo svg
    tool_logo_svg_selector_for = "//img[@alt='{}']"
    mlwb_logo_svg = "//div[@id='jp-MainLogo']//*[local-name()='svg' and @data-icon='ui-components:jupyter']"
    tools_logo_header = "//span[text()='{}']/ancestor::div[@data-baseweb='select']/following-sibling::img"

    # menu
    menu_svg = "//img[contains(@src,'menu')]"
    menu_nav = "//nav[@role='navigation']"
    menu_panel_p_selector_for = "//div[@data-baseweb='block']//div[text()='{}']"
    menu_clusters_p = menu_panel_p_selector_for.format("Clusters")

    # graphstudio
    gst_home_design_schema_btn = "//a[text()=' Design Schema ']"
    gst_menu_panel_design_schema_p = "//p[text()='Design Schema']"
    gst_menu_panel_load_data_p = "//p[text()='Load Data']"
    gst_menu_panel_write_queries_p = "//p[text()='Write Queries']"
    gst_switch_graph_btn = "//button[@aria-label='Switch graph']"
    imported_query_p_selector_for = "//div[@class='mat-list-text']//p[@class='mat-line' and contains(text(), '{}')]"
    imported_graph_name_span_selector_for = "//*[@id='one-single-graph']/div[1]/div/span[contains(text(), '{}')]"

    # Insights
    ins_my_apps_tab_btn = "//*[contains(text(), 'My Application')]"  # 3.8.0
    ins_other_apps_tab_btn = "//*[contains(text(), 'Other Application')]"  # 3.8.0
    ins_new_app_span = "//span[text()='New Application']"
    ins_close_new_app_btn = "//button[@aria-label='Close']"

    # admin portal
    # monitor
    adp_menu_panel_monitor_p = "//p[text()='Monitor']"
    adp_menu_panel_monitor_queries_p = "//p[text()='Queries']"  # only for 3.8.0
    adp_monitor_queries_overview_div = "//div[text()='Overview']"
    adp_monitor_queries_overview_current_running_queries_p = "//p[text()='Current running queries']"
    # management
    adp_menu_panel_management_p = "//p[text()='Management']"
    adp_menu_panel_management_components_p = "//p[text()='Components']"
    # backup and restore
    adp_menu_panel_backup_restore_p = "//p[text()='Backup & Restore']"
    cron_job_p = "//p[contains(.,'Cron Job Configurations')]"

    # cron_job_config_element_id
    cron_job_span = "//span[text()='{}']"
    cron_job_input = cron_job_span + "/ancestor::span/preceding-sibling::input"
    cron_job_hint = cron_job_input + "/ancestor::mat-form-field/div/div[3]//mat-hint"
    minute = "Minute"
    hour = "Hour"
    day_of_month = "Day of month"
    month = "Month"
    day_of_week = "Day of week"

    cron_job_config_scop = {
        minute: "0 - 59, *",
        hour: "0 - 23, *",
        day_of_month: "1 - 31, *",
        month: "1 - 12, Jan - Dec, *",
        day_of_week: "0 - 6, Mon - Sun, *"
    }

    cron_job_update_btn = "//button[contains(.,'UPDATE')]"
    confirm_h1 = "//h1[contains(.,'Confirm')]"
    ok_btn = "//button[contains(.,'OK')]"
    update_successfully_span = "//span[contains(.,'Successfully updated backup schedule')]"
    mat_error = "//mat-error/pre"

    backup_section = "//section[contains(.,'Back up/Restore')]"
    backup_tag_input = backup_section + "//input"
    backup_error_msg = backup_section + "//mat-error[contains(.,'Please enter backup tag')]"
    backup_progress_log_pre = backup_section + "//pre"
    backup_btn = "//button[contains(.,'BACK UP')]"
    restore_btn = "//button[contains(.,'RESTORE')]"

    backup_successfully_span = "//span[contains(.,'Successfully created backup {}')]"
    restore_successfully_span = "//span[contains(.,'Successfully restored backup {}')]"
    remove_backup_successfully_span = "//span[contains(.,'Successfully removed backup {}')]"
    manage_backup_section = "//section[contains(.,'Manage Backups')]"
    backup_list_tr = manage_backup_section + "//td[contains(.,'{}')]"
    backup_delete_btn = backup_list_tr + "/ancestor::tr//button[contains(.,'delete')]"
    manage_process_log_pre = manage_backup_section + "//pre"

    schedule_backup_section = "//section[contains(.,'Scheduled Backup Status')]"
    schedule_backup_list_span = schedule_backup_section + "//span[contains(.,'{}')]"
    schedule_process_log_pre = schedule_backup_section + "//pre"
    schedule_backup_name = schedule_backup_list_span + "/ancestor::mat-row/mat-cell[1]/span"

    # graphql
    gql_exec_query_btn = "//button[contains(@title, 'Execute Query')]"
    gql_Prettify_btn = "//button[@class='toolbar-button' and text()='Prettify']"
    gql_Merge_btn = "//button[@class='toolbar-button' and text()='Merge']"
    gql_Copy_btn = "//button[@class='toolbar-button' and text()='Copy']"
    gql_History_btn = "//button[@class='toolbar-button' and text()='History']"
    gql_no_existing_graph_msg_p = "//p[contains(text(), 'No graphs are currently available. Please create a new graph to get started.')]"

    # gsql shell
    gsql_help_shortcuts_h3 = "//h3[contains(., 'Editor Shortcuts')]"

    # MlWB
    mlwb_readme_span = "//li[contains(@title,'Name: README.md') and contains(@title,'GraphML')]//span[text()='README.md']"
    mlwb_main_dock_pannel = "//div[@id='jp-main-dock-panel']"
    mlwb_title_h1 = "//h1[@id='TigerGraph-ML-Workbench:-Graph-ML-as-a-Service']"
