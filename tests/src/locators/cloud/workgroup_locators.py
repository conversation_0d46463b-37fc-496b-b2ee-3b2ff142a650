"""
The locators for Cloud elements, update the elements
when we have any major or minor UI changes.
"""

from common.common_settings import DEFAULT_WORKSPACE_NAME

class WorkgroupLocators:
    workgroup_line = 'a[href="/groups"]'
    create_workgroup = 'g[clip-path="url(#clip0_358_1776)"]'
    workgroup_name = '//span[text()="{0}"]'
    get_started_now = '//span[contains(text(), "Get Started Now")]'
    create_WS = '//span[text()="Create Workspace"]'
    delete_WG = '//button[text()="Delete Workgroup"]'
    delete_WG_OK = '//button[text()="OK"]'
    horizon_progressbar = 'div[data-baseweb="progress-bar"]'
    top_blue_circle_progressbar = 'div > i'
    backup_list = 'div[testid="loader"]'
    WS_operate_circle_progressbar = 'div > button > i'
    upload_file_progressbar = 'div[data-baseweb="progress-bar"]'
    input_WG_name = 'input[placeholder="Enter workgroup name"]'
    default_cp_aws = '//div[@data-baseweb="form-control-container"]//div[text()="AWS"]'
    next_button = '//button[contains(text(),"Next")]'
    input_WS_name = 'input[placeholder="Enter workspace name"]'
    input_TG_version = 'input[name="tg_version"]'

    workspace_button = f'//button[text()="{DEFAULT_WORKSPACE_NAME}"]'
    workspace_box_button = 'div[class*="workspace-box"] button:contains("QE_E2E_WS")'

    workspace_version_label = '//span[text()="Version:"]'
    workspace_version = '//span[text()="Version:"]/following-sibling::span'
    # edit WS
    edit_WS_detail = 'button > svg > g'
    select_DB_click = 'div[data-baseweb="select"]'  # select the second
    select_WS_size = "//div[text()='MEMORY:']"
    select_TG_size = '//span[text()="TG-0"]'
    input_auto_stop_minutes = 'input[name="auto_stop_minutes"]'
    auto_resume_button = 'input[name="enable_auto_start"]'
    save_button = '//button[text()="Save"]'
    input_RW_DB_name = 'input[placeholder="Enter database name"]'
    select_DB_radio = 'label[data-baseweb="radio"]'  # 0: create new DB,  1: select existed DB
    select_DB_name = '//div[text()="{0}"]'
    create_button = 'button[type="submit"]'
    addons_GAP_button = 'img[alt="Admin Portal"]'
    # delete all WS
    more_button = 'svg[title="Overflow"]'
    checkbox_button = 'label[data-baseweb="checkbox"] > span'
    terminate_button = '//div[text()="Terminate"]'
    comfirm_delete_WS_button = '//button[text()="Confirm"]'

    # workspace general
    create_WS_button = "//button[@aria-label='create workspace']"
    WS_status_icon = 'div[aria-expanded="false"]'
    save_addon_button = 'button[aria-label="addon-save"]'

    # design schema
    common_span_button = '//span[normalize-space(text())="{0}"]'
    common_button = '//button[normalize-space(text())="{0}"]'
    common_div_button = '//div[normalize-space(text())="{0}"]'
    common_a_button = '//a[normalize-space(text())="{0}"]'
    connect_to_tools = '//div[normalize-space(text())="{0}"]'
    toast_window = 'div[data-baseweb="toast"]>div>div>div>div>div'
    create_new_vertex_button = '//button[text()="Create Vertex"]'
    select_id_as_attribute = 'label[data-baseweb="checkbox"]'
    add_attribute_button = 'button[aria-label="Add attribute"]'
    delete_attribute_button = 'button[aria-label="Delete attribute"]'
    graph_select_button = 'div[aria-label="graph-select"]'
    refresh_graph_button = 'button[aria-label="refresh-schema"]'
    vertex_name_input = 'input[placeholder="Enter vertex name"]'
    vertex_image_button = '//div[@data-baseweb="base-input"]/../following-sibling::div/button'
    search_input = 'input[placeholder="Search Icon"]'
    bottom_ui_third_button = 'div[data-testid="bottom-ui"] button:nth-child(3)'
    vertex_dropdown_options = 'ul[role="listbox"] li[role="option"] div[aria-selected="false"]'
    graph_name_input = 'input[aria-label="graph-name-input"]'
    confirm_button = 'button[data-baseweb="button"]:contains("Confirm")'
    # attribute_list = 'tbody > tr'
    attribute_list = 'label[data-baseweb="checkbox"]'
    # delete_attribute_button = 'g[clip-path="url(#clip0_1583_10036)"]'
    save_schema_button = 'button[aria-label="save-schema"]'
    close_schema_button = 'button[aria-label="Close"]'
    assert_save_schema_text = 'You have not saved your changes to the schema'
    #  load data
    load_data = '//span[text()="Load Data"]'
    add_load_data = 'button[data-baseweb="button"]'
    graph_select = 'div[aria-label="graph-select"]'
    graph_option = 'li[role="option"]'
    delete_graph_buttons = 'li > div > div > button'
    create_new_graph = '//span[text()="Create New Graph"]'
    input_new_graph_name = 'input[aria-label="graph-name-input"]'
    upload_file_input = 'input[accept=".csv,.tsv,.json,.jsonl"]'
    # GSQL editor
    create_new_file = 'button[aria-label="add-file-folder"]'
    input_cmd = 'div[class="cm-content"]'
    inverted_triangle = 'svg[title="open"]'  # 0:WS  1:graph
    select_graph = '//div[contains(text(),"{0}")]'
    input_explore_query = 'input[aria-haspopup="listbox"]'
    select_explore_vertex = 'div[aria-selected="false"]'  # 0:show schema  >1: vertex
    operate_button = 'button[data-baseweb="button"]'  # 0:setting  1: run
    run_all_button = "//button[contains(.,'Run')]"
    # get data profile
    select_graph_trangle = 'svg[title="open"]'
    preview_button = '.lucide.lucide-eye'
    delete_button = '.lucide.lucide-trash2'
    profile_button = '//button[contains(@id,"profile")]'
    profile_overview = '//span[contains(text(), "Profile Overview")]'
    # solution
    input_solution_name = 'input[placeholder="Enter solution name"]'
    # edit workspace
    checkbox_button_workspace = 'label[data-baseweb="checkbox"]'
    checkbox_status_workspace = 'input[type="checkbox"]'
    # monitor
    overall_system_health_button = '.dashboard-row__title'
    # connect
    redirect_to_addons_button = 'a[href="/marketplace/addons"]'
    common_h4_addons = '//h4[normalize-space(text())="{0}"]'
    close_button = 'button[aria-label="Close"]'
    # access management
    delete_role_button = 'div[role="radiogroup"] + button'
    input_role = 'input[aria-autocomplete="list"]'
    select_account = 'ul[role="listbox"]>li'
    # backup and restore
    input_hour = 'input[aria-invalid="false"]'
    # network access
    label_checkbox = 'label[data-baseweb="checkbox"]'
    ip_allow_list = 'input[type="checkbox"]'
    input_ip = 'input[name="cidr"]'
    input_note = 'input[name="note"]'
    #load data
    load_data_parent_sibling_input = "//span[text()='{0}']/parent::div/following-sibling::input"
    load_data_parent_sibling_button = "//span[text()='{0}']/parent::div/following-sibling::button"
    input_field_after_label = "//div[text()='{0}']/following-sibling::div//input"
    input_load_data_config = 'input[placeholder="Add {0}"]'
    snowflake_url = 'input[placeholder="Enter Snowflake URL"]'
    select_tables_div = '//div[contains(text(), "Select tables and views")]'
    tag_info = '//span[text()="TAG"]/following-sibling::button'
    quick_map_button = '//button[contains(text(), "Quick Map")]'
    add_data_source_button = '//span[text()="Data Source Configuration"]/parent::div/following-sibling::button'
    s3_uri_input = 'input[placeholder="Enter S3 URI"]'
    gcs_uri_input = 'input[placeholder="Enter GCS URI"]'
    abs_uri_input = 'input[placeholder="Enter ABS URI"]'

    # Delta Lake
    gsql_editor_content = 'div[class="cm-content"]'

    # Solution workspace related
    mule_account_detection = 'button[type="button"] img[alt="Mule Account Detection"]'

    # Query Editor related
    mule_detection_folder = '//div[text()="Mule_Account_Detection"]'
    run_query_button = 'button[data-baseweb="button"]:contains("Run Query")'
    query_results_panel = 'div[role="textbox"][aria-multiline="true"][aria-readonly="true"][aria-autocomplete="list"]'
    insights_query = 'li[data-nodeid*="insights_get_net_gain_numbers_and_percentages"]'
    queries_tab = 'button[role="tab"]:contains("Queries")'
    got_it_button = '//button[normalize-space(text())="Got it!"]'

    # Query Results related
    cm_line_elements = '.cm-line'
    tips_panel = 'div[data-baseweb="toast"]'
    tips_panel_close_button = 'button[aria-label="Close"]'
    query_results_textbox = 'div[role="textbox"][aria-multiline="true"][aria-readonly="true"]'
    query_results_json = 'div[class*="json-viewer"]'
    query_results_error = 'div[class*="error-message"]'
    query_results_success = 'div[class*="success-message"]'
    query_results_loading = 'div[class*="loading-indicator"]'



class GAPHomeLocators:
    # Monitor
    monitor = "//p[contains(.,'Monitor')]"
    logs = "//p[contains(.,'Logs')]"
    log_pattern = "//input[@aria-label='Search pattern']"
    search = "//button[contains(.,'center_focus_weakSEARCH')]"
    all_component = "//span[text()='Components :']/../../../div[2]/mat-grid-list[1]/div/mat-grid-tile[1]/figure/mat-checkbox/label/span"
    assert_search_log_text = "m1:"


