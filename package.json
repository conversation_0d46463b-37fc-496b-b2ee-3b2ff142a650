{"name": "cloud-portal", "version": "1.0.0", "description": "cloud portal", "private": true, "scripts": {"dev": "vite --host --config vite.config.ts --mode dev", "dev:https": "vite --host --config vite.config.https.ts", "build": "NODE_OPTIONS=\"--max-old-space-size=8192\" vite build --mode dev", "build:uat": "NODE_OPTIONS=\"--max-old-space-size=8192\" vite build --mode uat", "build:staging": "NODE_OPTIONS=\"--max-old-space-size=8192\" vite build --mode staging", "build:production": "NODE_OPTIONS=\"--max-old-space-size=8192\" vite build --mode production", "test": "vitest run", "test:ui": "vitest --ui", "test:watch": "vitest", "test:ci": "vitest run --coverage", "serve": "vite preview", "prepare": "husky install && rm -rf .git/hooks && ln -s ../.husky .git/hooks", "typescript:check": "tsc -p ./tsconfig.json --noEmit", "eslint": "eslint --ext .js,.jsx,.ts,.tsx --max-warnings=0 -- src", "lint": "yarn typescript:check && yarn eslint", "prettier": "prettier . --write", "prettier:check": "prettier . --check", "mock": "json-server --watch ./mock/db.json --port 3800", "size": "source-map-explorer 'dist/assets/*.js' --no-border-checks", "lezer": "lezer-generator src/pages/editor/GSQL/script.grammar -o src/pages/editor/GSQL/script.grammar.ts && lezer-generator src/pages/editor/GSQL/query.grammar -o src/pages/editor/GSQL/query.grammar.ts"}, "dependencies": {"@amberflo/uikit": "^2.2.12", "@atlaskit/pragmatic-drag-and-drop": "^1.4.0", "@auth0/auth0-react": "^2.2.4", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-sql": "^6.7.1", "@codemirror/language": "^6.3.3", "@codemirror/legacy-modes": "^6.3.3", "@dagrejs/dagre": "^1.1.4", "@fortawesome/fontawesome-free": "^6.1.1", "@fortawesome/fontawesome-svg-core": "^1.3.0", "@fortawesome/free-regular-svg-icons": "^6.0.0", "@fortawesome/free-solid-svg-icons": "^6.0.0", "@fortawesome/react-fontawesome": "^0.1.17", "@neo4j-cypher/editor-support": "^1.0.2", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.0.2", "@stripe/react-stripe-js": "^2.3.0", "@stripe/stripe-js": "^2.1.6", "@testing-library/user-event": "^14.5.2", "@tigergraph/app-ui-lib": "0.3.36", "@tigergraph/cytoscape-edgehandles": "^4.0.1", "@tigergraph/tools-models": "1.1.49", "@tigergraph/tools-ui": "0.2.14-APPS-3787-9", "@types/mixpanel": "^2.14.8", "@types/mixpanel-browser": "^2.49.0", "@types/ua-parser-js": "^0.7.39", "@uiw/codemirror-extensions-langs": "^4.23.6", "@uiw/codemirror-theme-vscode": "^4.23.0", "@uiw/codemirror-theme-xcode": "^4.21.20", "@uiw/react-codemirror": "^4.21.9", "@xyflow/react": "^12.3.6", "ahooks": "^3.7.8", "axios": "1.7.4", "baseui": "^11.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "compare-versions": "^6.1.1", "cron-validate": "^1.4.5", "currency.js": "^2.0.4", "cytoscape": "^3.28.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.0.0", "eventemitter3": "^5.0.1", "generate-password-ts": "^1.6.5", "immutable": "^5.0.0-beta.4", "inline-style-expand-shorthand": "^1.6.0", "jotai": "^2.6.2", "json-bigint": "^1.0.0", "lodash-es": "^4.17.21", "lucide-react": "^0.274.0", "marked": "^12.0.2", "mixpanel-browser": "^2.49.0", "msw": "^2.6.4", "nanoid": "^5.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^3.1.4", "react-grid-layout": "https://github.com/tigergraph/react-grid-layout#ac9280e250c060cce5e847b1a724172288686bac", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "react-icons": "^4.10.1", "react-json-view": "^1.21.3", "react-query": "^3.39.3", "react-router-dom": "^6.15.0", "react-use-clipboard": "^1.0.9", "resumablejs": "^1.1.0", "rifm": "^0.12.1", "short-uuid": "^4.2.2", "source-map-explorer": "^2.5.3", "styletron-engine-atomic": "^1.4.8", "styletron-react": "^6.0.2", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^1.0.37"}, "devDependencies": {"@lezer/generator": "^1.7.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.1.2", "@types/cytoscape": "3.19.10", "@types/json-bigint": "^1.0.4", "@types/lodash-es": "^4.17.11", "@types/node": "^18.16.3", "@types/react": "^18.3.9", "@types/react-color": "^3.0.10", "@types/react-dom": "^18.3.0", "@types/react-grid-layout": "^1.3.1", "@types/resumablejs": "^1.1.0", "@types/styletron-engine-atomic": "^1.1.1", "@types/styletron-react": "^5.0.3", "@types/styletron-standard": "^2.0.2", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "@vitejs/plugin-react-swc": "^3.3.0", "@vitest/coverage-v8": "^2.1.5", "@vitest/ui": "^2.1.5", "autoprefixer": "^10.4.20", "c8": "^7.13.0", "canvas": "^3.1.0", "eslint": "^8.55.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-baseui": "^11.2.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-testing-library": "^5.10.3", "eslint-plugin-unused-imports": "^2.0.0", "happy-dom": "^15.11.6", "husky": "^8.0.3", "jsdom": "^25.0.1", "json-server": "^0.17.3", "lint-staged": "^13.2.2", "postcss": "^8.4.29", "prettier": "^2.8.8", "react-color": "^2.19.3", "sass": "^1.70.0", "tailwindcss": "^3.3.3", "typescript": "5.1.3", "vite": "^5.4.11", "vite-plugin-dynamic-import": "^1.5.0", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.2.0", "vitest": "^2.1.5", "vitest-monocart-coverage": "^2.1.2"}, "resolutions": {"d3-color": "3.1.0", "axios": "1.7.4", "braces": "3.0.3", "dompurify": "2.5.4", "micromatch": "4.0.8", "jspdf": "3.0.1"}, "resolve": {"fallback": {"crypto": false}}, "msw": {"workerDirectory": ["public"]}}