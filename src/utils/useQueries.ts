import { axiosCluster } from '@/lib/network';
import { Return } from '@/lib/type';
import { WorkspaceT } from '@/pages/workgroup/type';
import { GsqlQueryMeta } from '@tigergraph/tools-models';
import { useQuery } from 'react-query';

export const getQueriesInfoReq = (graphName: string) => {
  return axiosCluster.get<Return<GsqlQueryMeta[]>>(`/api/queries/${graphName}/info`);
};

export const useQueries = (wp: WorkspaceT | null, graphName: string, enable: boolean = true) => {
  return useQuery(
    ['queries', wp?.workspace_id, graphName],
    async () => {
      const resp = await getQueriesInfoReq(graphName);
      const queries = resp.data.results || [];
      return queries;
    },
    {
      enabled: !!(wp && graphName) && enable,
    }
  );
};
