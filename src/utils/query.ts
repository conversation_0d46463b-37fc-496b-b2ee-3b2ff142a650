import { GsqlQueryMeta, QuerySyntax } from '@tigergraph/tools-models';

export const getQuerySyntax = (query: GsqlQueryMeta): QuerySyntax => {
  if (query.syntax!.toLowerCase().includes('gsql')) {
    return 'GSQL';
  }

  if (query.syntax!.toLowerCase().includes('cypher')) {
    return 'CYPHER';
  }

  return query.syntax! as QuerySyntax;
};

export enum QueryStatus {
  Installing = 'Installing',
  Disabled = 'Disabled',
  Installed = 'Installed',
  Draft = 'Draft',
  Unknown = 'Unknown',
}

export const getQueryStatus = (query: GsqlQueryMeta): string => {
  switch (true) {
    case query.installing:
      return QueryStatus.Installing;
    case query.installed && !query.enabled:
      return QueryStatus.Disabled;
    case query.installed:
      return QueryStatus.Installed;
    default:
      return QueryStatus.Draft;
  }
};

export const getQueryContentUnderNewName = (query: GsqlQueryMeta, newName: string) => {
  // The regex pattern matches "QUERY (arbitrary whitespace) name (arbitrary whitespace) ("
  const regex = `(QUERY\\s+)(${query.name})(\\s*\\()`;

  return query.code.replace(new RegExp(regex, 'i'), `$1${newName}$3`);
};
