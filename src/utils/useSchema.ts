import { Return } from '@/lib/type';
import { WorkspaceT } from '@/pages/workgroup/type';
import { getGraphSchema, GLOBAL_GRAPH_NAME } from '@tigergraph/tools-models';
import { CommonRequestConfig } from '@tigergraph/tools-models/api/common';
import { Schema } from '@tigergraph/tools-ui/esm/graph/type';
import { useQuery } from 'react-query';

const reqGraphSchema = (graphName: string, config: CommonRequestConfig) => {
  const params = graphName === GLOBAL_GRAPH_NAME ? {} : { graph: graphName };
  return getGraphSchema(params, config).then((res) => res.data as Return<Schema>);
};

export const emptySchema: Schema = {
  GraphName: '',
  VertexTypes: [],
  EdgeTypes: [],
};

export function useSchema(wp: WorkspaceT | null | undefined, graph: string, enable = true) {
  return useQuery(
    ['schema', graph, wp?.nginx_host, wp?.tg_version],
    async () => {
      // if no current workspace, return empty schema
      if (!wp) {
        return emptySchema;
      }

      const response = await reqGraphSchema(graph, {
        baseURL: `https://${wp.nginx_host}`,
        version: wp.tg_version,
      });

      return response.results!;
    },
    {
      enabled: enable && !!graph,
    }
  );
}
