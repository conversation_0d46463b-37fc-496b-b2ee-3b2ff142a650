import { Gsql<PERSON><PERSON><PERSON><PERSON><PERSON>, QuerySyntax } from '@tigergraph/tools-models';

export const testQuery: GsqlQueryMeta = {
  name: 'TestQuery',
  syntax: 'GSQL' as QuerySyntax,
  draft: '',
  code: 'CREATE OR REPLACE DISTRIBUTED QUERY batch_party_cc_features(INT connections=25000, STRING output_file_path = "/home/<USER>/gsql_output/batch_party_cc_features.csv") {\n\n  MapAccum<INT, INT> @@connect_component, @@distinct_name, @@distinct_dob, @@distinct_email, @@distinct_phone, @@distinct_address, @@distinct_ip, @@distinct_id, @@distinct_device, @@distinct_party, @@distinct_account, @@distinct_card, @@connected_via_name, @@connected_via_dob, @@connected_via_email, @@connected_via_phone, @@connected_via_address, @@connected_via_ip, @@connected_via_id, @@connected_via_device, @@connected_via_party, @@connected_via_account, @@connected_via_card, @@only_connected_via_name, @@only_connected_via_dob, @@only_connected_via_email, @@only_connected_via_phone, @@only_connected_via_address, @@only_connected_via_ip, @@only_connected_via_id, @@only_connected_via_device, @@only_connected_via_party, @@only_connected_via_account, @@only_connected_via_card;\n  OrAccum @connected_via_name, @connected_via_dob, @connected_via_email, @connected_via_phone, @connected_via_address, @connected_via_ip, @connected_via_id, @connected_via_device, @connected_via_party, @connected_via_account, @connected_via_card;\n  SetAccum<STRING> @middle_type;\n  SetAccum<INT> @comm_ids;\n  MinAccum<INT> @cc_id; // each vertex\'s tentative component id\n  MaxAccum<INT> @f_apps_count;\n  SumAccum<INT> @@total_count, @@unified_count, @fraud_apps;\n  ListAccum<int> @result;\n  MaxAccum<INT> @is_fraud;\n  SetAccum<STRING> @@edge_type_set = (\n    "Has_Full_Name",\n    "Has_DOB",\n    "Has_Email",\n    "Has_Phone",\n    "Has_Address",\n    "Has_IP",\n    "Has_ID",\n    "Has_Device",\n    "Has_Party",\n    "Has_Account",\n    "Has_Card"\n  );\n  \n  FILE f (output_file_path);\n  \n  f.println("Party", "Party_Fraud_Status", "Connected_Component", "Connected_Component_Party_Nodes", "Connected_Component_Fraud_Party_Nodes", "Distinct_Name_Nodes", "Nodes_Connected_By_Name", "Nodes_Only_Connected_By_Name", "Distinct_DOB_Nodes", "Nodes_Connected_By_DOB", "Nodes_Only_Connected_By_DOB", "Distinct_Email_Nodes", "Nodes_Connected_By_Email", "Nodes_Only_Connected_By_Email", "Distinct_Phone_Nodes", "Nodes_Connected_By_Phone", "Nodes_Only_Connected_By_Phone", "Distinct_Address_Nodes", "Nodes_Connected_By_Address", "Nodes_Only_Connected_By_Address", "Distinct_IP_Nodes", "Nodes_Connected_By_IP", "Nodes_Only_Connected_By_IP", "Distinct_ID_Nodes", "Nodes_Connected_By_ID", "Nodes_Only_Connected_By_ID", "Distinct_Device_Nodes", "Nodes_Connected_By_Device", "Nodes_Only_Connected_By_Device", "Distinct_Party_Nodes", "Nodes_Connected_By_Party", "Nodes_Only_Connected_By_Party", "Distinct_Account_Nodes", "Nodes_Connected_By_Account", "Nodes_Only_Connected_By_Account", "Distinct_Card_Nodes", "Nodes_Connected_By_Card", "Nodes_Only_Connected_By_Card");\n  \n  Start(ANY) = {Party.*};\n\n  If Start.size() > 0 THEN\n\n    Start = SELECT t\n            FROM Start:s - (Entity_In_Ring) - Connected_Component:t\n            ACCUM s.@cc_id += t.id,\n            s.@is_fraud = s.is_fraud,\n            if s.is_fraud then t.@fraud_apps += 1 end;\n\n    Start = SELECT t\n            FROM Start:s - (Entity_In_Ring) - Party:t\n            ACCUM s.@cc_id += t.@cc_id,\n                  t.@f_apps_count = s.@fraud_apps;\n\n      # Calculate the first part\n      Part_one = select s from Start:s -(@@edge_type_set:e)- :t\n                  ACCUM\n                    t.@comm_ids += s.@cc_id\n                  POST-ACCUM\n                    @@connect_component += (s.@cc_id -> 1),\n                    FOREACH comm_id in t.@comm_ids DO\n                      if t.type == "Full_Name" then @@distinct_name += (comm_id->1) end,\n                      if t.type == "DOB" then @@distinct_dob += (comm_id->1) end,\n                      if t.type == "Email" then @@distinct_email += (comm_id->1) end,\n                      if t.type == "Phone" then @@distinct_phone += (comm_id->1) end,\n                      if t.type == "Address" then @@distinct_address += (comm_id->1) end,\n                      if t.type == "IP" then @@distinct_ip += (comm_id->1) end,\n                      if t.type == "ID" then @@distinct_id += (comm_id->1) end,\n                      if t.type == "Device" then @@distinct_device += (comm_id->1) end,\n                      if t.type == "Party" then @@distinct_party += (comm_id->1) end,\n                      if t.type == "Account" then @@distinct_account += (comm_id->1) end,\n                      if t.type == "Card" then @@distinct_card += (comm_id->1) end\n                    END;\n\n      Part_two = select s from Start:s-(@@edge_type_set:e)->:t\n                 where t.outdegree(@@edge_type_set) >= 2 and t.outdegree() < connections\n                 accum\n\n                   s.@middle_type += t.type,\n\n                   if t.type == "Full_Name" then s.@connected_via_name += true end,\n                   if t.type == "DOB" then s.@connected_via_dob += true end,\n                   if t.type == "Email" then s.@connected_via_email += true end,\n                   if t.type == "Phone" then s.@connected_via_phone += true end,\n                   if t.type == "Address" then s.@connected_via_address += true end,\n                   if t.type == "IP" then s.@connected_via_ip += true end,\n                   if t.type == "ID" then s.@connected_via_id += true end,\n                   if t.type == "Device" then s.@connected_via_device += true end,\n                   if t.type == "Party" then s.@connected_via_party += true end,\n                   if t.type == "Account" then s.@connected_via_account += true end,\n                   if t.type == "Card" then s.@connected_via_card += true end\n  \n                post-accum\n  \n                  if s.@connected_via_name then @@connected_via_name += (s.@cc_id->1) end,\n                  IF s.@connected_via_dob then @@connected_via_dob += (s.@cc_id->1) end,\n                  IF s.@connected_via_email then @@connected_via_email += (s.@cc_id->1) end,\n                  IF s.@connected_via_phone then @@connected_via_phone += (s.@cc_id->1) end,\n                  IF s.@connected_via_address then @@connected_via_address += (s.@cc_id->1) end,\n                  IF s.@connected_via_ip then @@connected_via_ip += (s.@cc_id->1) end ,\n                  IF s.@connected_via_id then @@connected_via_id += (s.@cc_id->1) end,\n                  IF s.@connected_via_device then @@connected_via_device += (s.@cc_id->1) end,\n                  IF s.@connected_via_party then @@connected_via_party += (s.@cc_id->1) end,\n                  IF s.@connected_via_account then @@connected_via_account += (s.@cc_id->1) end,\n                  IF s.@connected_via_card then @@connected_via_card += (s.@cc_id->1) end\n                \n                having s.@middle_type.size() ==1;\n\n      Part_three = select s from Part_two:s POST-ACCUM\n\n        IF "Full_Name" in s.@middle_type then @@only_connected_via_name += (s.@cc_id->1) end,\n        IF "DOB" in s.@middle_type then @@only_connected_via_dob += (s.@cc_id->1) end,\n        IF "Email" in s.@middle_type then @@only_connected_via_email += (s.@cc_id->1) end,\n        IF "Phone" in s.@middle_type then @@only_connected_via_phone += (s.@cc_id->1) end,\n        IF "Address" in s.@middle_type then @@only_connected_via_address += (s.@cc_id->1) end,\n        IF "IP" in s.@middle_type then @@only_connected_via_ip += (s.@cc_id->1) end,\n        IF "ID" in s.@middle_type then @@only_connected_via_id += (s.@cc_id->1) end,\n        IF "Device" in s.@middle_type then @@only_connected_via_device += (s.@cc_id->1) end,\n        IF "Party" in s.@middle_type then @@only_connected_via_party += (s.@cc_id->1) end,\n        IF "Account" in s.@middle_type then @@only_connected_via_account += (s.@cc_id->1) end,\n        IF "Card" in s.@middle_type then @@only_connected_via_card += (s.@cc_id->1) end;\n\n      Start = select s from Start:s POST-ACCUM f.println(s, s.@is_fraud, s.@cc_id, @@connect_component.get(s.@cc_id), s.@f_apps_count, @@distinct_name.get(s.@cc_id), @@connected_via_name.get(s.@cc_id), @@only_connected_via_name.get(s.@cc_id), @@distinct_dob.get(s.@cc_id), @@connected_via_dob.get(s.@cc_id), @@only_connected_via_dob.get(s.@cc_id), @@distinct_email.get(s.@cc_id), @@connected_via_email.get(s.@cc_id), @@only_connected_via_email.get(s.@cc_id), @@distinct_phone.get(s.@cc_id), @@connected_via_phone.get(s.@cc_id), @@only_connected_via_phone.get(s.@cc_id), @@distinct_address.get(s.@cc_id), @@connected_via_address.get(s.@cc_id), @@only_connected_via_address.get(s.@cc_id), @@distinct_ip.get(s.@cc_id), @@connected_via_ip.get(s.@cc_id), @@only_connected_via_ip.get(s.@cc_id), @@distinct_id.get(s.@cc_id), @@connected_via_id.get(s.@cc_id), @@only_connected_via_id.get(s.@cc_id), @@distinct_device.get(s.@cc_id), @@connected_via_device.get(s.@cc_id), @@only_connected_via_device.get(s.@cc_id), @@distinct_party.get(s.@cc_id), @@connected_via_party.get(s.@cc_id), @@only_connected_via_party.get(s.@cc_id), @@distinct_account.get(s.@cc_id), @@connected_via_account.get(s.@cc_id), @@only_connected_via_account.get(s.@cc_id), @@distinct_card.get(s.@cc_id), @@connected_via_card.get(s.@cc_id), @@only_connected_via_card.get(s.@cc_id));\n\n      PRINT "Results file created!" AS result;\n  \n  ELSE\n\n    Print "Connected Component not found for Party" AS result;\n\n  END;\n\n}',
  installed: false,
  installMode: 'UDF',
  callerQueries: [],
  enabled: true,
  graphUpdate: false,
  installing: false,
  optimizedLevel: 0,
  endpoint: {
    query: {
      Entity_Resolution_KYC: {
        batch_party_cc_features: {
          'GET/POST': {
            action: 'query',
            alternative_endpoint: '/query/batch_party_cc_features',
            createDataList: {},
            deleteDataList: {},
            enabled: true,
            executeGranted: false,
            executorList: [],
            function: 'queryDispatcher',
            graphUpdate: false,
            graph_name: 'Entity_Resolution_KYC',
            libudf: 'libudf-Entity_Resolution_KYC-1',
            needCurrentRoles: false,
            needReadRole: false,
            parameters: {
              connections: {
                defaultValue: '25000',
                index: 0,
                min_count: 0,
                type: 'INT64',
              },
              output_file_path: {
                defaultValue: '/home/<USER>/gsql_output/batch_party_cc_features.csv',
                index: 1,
                min_count: 0,
                type: 'STRING',
              },
              query: {
                default: 'batch_party_cc_features',
                type: 'STRING',
              },
            },
            payload: [
              {
                rule: 'AS_JSON',
              },
              {
                rule: 'AS_QUERY_STRING',
              },
            ],
            readDataList: {
              Account: [
                'id',
                'create_Time',
                'is_fraud',
                'account_type',
                'account_level',
                'com_size',
                'pagerank',
                'shortest_path_length',
                'ip_collision',
                'fraud_ip',
                'device_collision',
                'fraud_device',
                'trans_in_mule_ratio',
                'trans_out_mule_ratio',
                'mule_cnt',
                'com_id',
              ],
              Address: ['address'],
              Application: ['id', 'created_at', 'status', 'line_of_credit', 'annual_percentage_rate', 'is_fraud'],
              Application_Has_Party: ['from', 'to'],
              Assigned_To: ['from', 'to'],
              Assigned_To_County: ['from', 'to'],
              Card: ['card_number', 'is_fraud', 'pagerank', 'c_id', 'c_size', 'occupation'],
              City: ['id', 'city', 'population'],
              Connected_Component: ['id'],
              Country: ['country'],
              County: ['id'],
              DOB: ['dob'],
              Device: ['id', 'is_blocked'],
              Email: ['email'],
              Entity_In_Ring: ['from', 'to'],
              Full_Name: ['name'],
              Has_Address: ['from', 'to'],
              Has_DOB: ['from', 'to'],
              Has_Device: ['from', 'to'],
              Has_Email: ['from', 'to'],
              Has_Full_Name: ['from', 'to'],
              Has_ID: ['from', 'to'],
              Has_IP: ['from', 'to'],
              Has_Phone: ['from', 'to'],
              ID: ['id', 'id_type'],
              IP: ['id', 'is_blocked'],
              Located_In: ['from', 'to'],
              Located_In_Country: ['from', 'to'],
              Located_In_State: ['from', 'to'],
              Party: ['id', 'is_fraud', 'gender', 'dob', 'party_type', 'name', 'created_at'],
              Party_Has_Account: ['from', 'to'],
              Party_Has_Card: ['from', 'to'],
              Phone: ['phone_number'],
              Same_As: ['from', 'to'],
              State: ['id'],
              Zipcode: ['id'],
            },
            summary: 'This is query entrance',
            target: 'GPE',
            updateDataList: {},
          },
        },
      },
    },
  },
  isHidden: false,
  isACLSpecified: false,
};
