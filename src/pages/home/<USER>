/* v8 ignore start */
import clsx from 'clsx';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { SVGProps } from 'react';
import SpaceSvgIcon from './icons/space.svg?react';
import DatabaseSvgIcon from './icons/database.svg?react';
import GroupSvgIcon from './icons/group.svg?react';

export function MenuCloseIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2585_22690)">
        <path
          d="M9.69516 1.74416C9.40932 1.45833 8.94849 1.45833 8.66266 1.74416L3.81516 6.59166C3.58766 6.81916 3.58766 7.18666 3.81516 7.41416L8.66266 12.2617C8.94849 12.5475 9.40932 12.5475 9.69516 12.2617C9.98099 11.9758 9.98099 11.515 9.69516 11.2292L5.47182 6.99999L9.70099 2.77083C9.98099 2.49083 9.98099 2.02416 9.69516 1.74416Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2585_22690">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function MenuOpenIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16.6667 5L7.5 5C7.04167 5 6.66667 5.375 6.66667 5.83333C6.66667 6.29167 7.04167 6.66667 7.5 6.66667L16.6667 6.66667C17.125 6.66667 17.5 6.29167 17.5 5.83333C17.5 5.375 17.125 5 16.6667 5ZM16.6667 9.16667L10 9.16667C9.54167 9.16667 9.16667 9.54167 9.16667 10C9.16667 10.4583 9.54167 10.8333 10 10.8333L16.6667 10.8333C17.125 10.8333 17.5 10.4583 17.5 10C17.5 9.54167 17.125 9.16667 16.6667 9.16667ZM17.5 14.1667C17.5 13.7083 17.125 13.3333 16.6667 13.3333L7.5 13.3333C7.04167 13.3333 6.66667 13.7083 6.66667 14.1667C6.66667 14.625 7.04167 15 7.5 15L16.6667 15C17.125 15 17.5 14.625 17.5 14.1667ZM3.08333 7.6L5.48333 10L3.08333 12.4C2.75833 12.725 2.75833 13.25 3.08333 13.575C3.40833 13.9 3.93333 13.9 4.25833 13.575L7.25 10.5833C7.575 10.2583 7.575 9.73333 7.25 9.40833L4.25833 6.41667C3.93334 6.09167 3.40834 6.09167 3.08333 6.41667C2.76667 6.74167 2.75833 7.275 3.08333 7.6Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function OrgIcon({ className }: { className?: string }) {
  return (
    <svg
      className={clsx(className)}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <g clipPath="url(#clip0_318_11775)">
        <path
          d="M12 7V5C12 3.9 11.1 3 10 3H4C2.9 3 2 3.9 2 5V19C2 20.1 2.9 21 4 21H20C21.1 21 22 20.1 22 19V9C22 7.9 21.1 7 20 7H12ZM10 19H4V17H10V19ZM10 15H4V13H10V15ZM10 11H4V9H10V11ZM10 7H4V5H10V7ZM20 19H12V9H20V19ZM18 11H14V13H18V11ZM18 15H14V17H18V15Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_318_11775">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function HelpIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 32 32" fill="currentColor">
      <g clipPath="url(#clip0_30_5288)">
        <g clipPath="url(#clip1_30_5288)">
          <g clipPath="url(#clip2_30_5288)">
            <path
              d="M16 6C10.48 6 6 10.48 6 16C6 21.52 10.48 26 16 26C21.52 26 26 21.52 26 16C26 10.48 21.52 6 16 6ZM16 24C11.59 24 8 20.41 8 16C8 11.59 11.59 8 16 8C20.41 8 24 11.59 24 16C24 20.41 20.41 24 16 24ZM15 20H17V22H15V20ZM16.61 10.04C14.55 9.74 12.73 11.01 12.18 12.83C12 13.41 12.44 14 13.05 14H13.25C13.66 14 13.99 13.71 14.13 13.33C14.45 12.44 15.4 11.83 16.43 12.05C17.38 12.25 18.08 13.18 18 14.15C17.9 15.49 16.38 15.78 15.55 17.03C15.55 17.04 15.54 17.04 15.54 17.05C15.53 17.07 15.52 17.08 15.51 17.1C15.42 17.25 15.33 17.42 15.26 17.6C15.25 17.63 15.23 17.65 15.22 17.68C15.21 17.7 15.21 17.72 15.2 17.75C15.08 18.09 15 18.5 15 19H17C17 18.58 17.11 18.23 17.28 17.93C17.3 17.9 17.31 17.87 17.33 17.84C17.41 17.7 17.51 17.57 17.61 17.45C17.62 17.44 17.63 17.42 17.64 17.41C17.74 17.29 17.85 17.18 17.97 17.07C18.93 16.16 20.23 15.42 19.96 13.51C19.72 11.77 18.35 10.3 16.61 10.04Z"
              fill="currentColor"
            />
          </g>
        </g>
      </g>
      <defs>
        <clipPath id="clip0_30_5288">
          <rect width="32" height="32" rx="5" fill="white" />
        </clipPath>
        <clipPath id="clip1_30_5288">
          <rect width="24" height="24" fill="white" transform="translate(4 4)" />
        </clipPath>
        <clipPath id="clip2_30_5288">
          <rect width="24" height="24" fill="white" transform="translate(4 4)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function GroupIconXL() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8.25 14.2031V8.20312H3.75L3.75 14.2031H8.25ZM3 15.7031C2.58579 15.7031 2.25 15.3673 2.25 14.9531L2.25 7.45312C2.25 7.03891 2.58579 6.70312 3 6.70312H9C9.41421 6.70312 9.75 7.03891 9.75 7.45312V10.4531H11.25L11.25 3.75C11.25 3.33579 11.5858 3 12 3H15.75V1.5C15.75 1.08579 16.0858 0.749999 16.5 0.749999H21C21.4142 0.749999 21.75 1.08579 21.75 1.5V6C21.75 6.41421 21.4142 6.75 21 6.75L16.5 6.75C16.0858 6.75 15.75 6.41421 15.75 6V4.5H12.75L12.75 10.4531H15.75V8.95312C15.75 8.53891 16.0858 8.20312 16.5 8.20312L21 8.20312C21.4142 8.20312 21.75 8.53891 21.75 8.95312V13.4531C21.75 13.8673 21.4142 14.2031 21 14.2031H16.5C16.0858 14.2031 15.75 13.8673 15.75 13.4531V11.9531H12.75V17.9531H15.75V16.5C15.75 16.0858 16.0858 15.75 16.5 15.75H21C21.4142 15.75 21.75 16.0858 21.75 16.5V21C21.75 21.4142 21.4142 21.75 21 21.75H16.5C16.0858 21.75 15.75 21.4142 15.75 21V19.4531H12C11.5858 19.4531 11.25 19.1173 11.25 18.7031V11.9531H9.75V14.9531C9.75 15.3673 9.41421 15.7031 9 15.7031H3ZM17.25 18.7031V20.25H20.25V17.25H17.25V18.7031ZM17.25 4.68269V5.25H20.25V2.25H17.25V4.68269ZM17.25 11.9531V12.7031H20.25V9.70312H17.25V11.9531Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function GroupIcon(props: SVGProps<SVGSVGElement>) {
  const [, theme] = useStyletron();
  return <GroupSvgIcon color={theme.colors['icon.primary']} {...props} />;
}

export function WorkgroupIcon() {
  return (
    <svg width={16} height={16} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        opacity={0.4}
        d="M10.717 1.3335H12.9743C13.9092 1.3335 14.6667 2.0974 14.6667 3.04014V5.31651C14.6667 6.25925 13.9092 7.02315 12.9743 7.02315H10.717C9.78216 7.02315 9.02466 6.25925 9.02466 5.31651V3.04014C9.02466 2.0974 9.78216 1.3335 10.717 1.3335Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.02566 1.3335H5.28297C6.21782 1.3335 6.97532 2.0974 6.97532 3.04014V5.31651C6.97532 6.25925 6.21782 7.02315 5.28297 7.02315H3.02566C2.09082 7.02315 1.33331 6.25925 1.33331 5.31651V3.04014C1.33331 2.0974 2.09082 1.3335 3.02566 1.3335ZM3.02566 8.97717H5.28297C6.21782 8.97717 6.97532 9.74107 6.97532 10.6838V12.9602C6.97532 13.9023 6.21782 14.6668 5.28297 14.6668H3.02566C2.09082 14.6668 1.33331 13.9023 1.33331 12.9602V10.6838C1.33331 9.74107 2.09082 8.97717 3.02566 8.97717ZM12.9743 8.97717H10.717C9.78214 8.97717 9.02464 9.74107 9.02464 10.6838V12.9602C9.02464 13.9023 9.78214 14.6668 10.717 14.6668H12.9743C13.9091 14.6668 14.6666 13.9023 14.6666 12.9602V10.6838C14.6666 9.74107 13.9091 8.97717 12.9743 8.97717Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function SpaceIcon(props: SVGProps<SVGSVGElement>) {
  const [, theme] = useStyletron();
  return <SpaceSvgIcon color={theme.colors['icon.primary']} {...props} />;
}

export function GraphIcon() {
  return (
    <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.39107 4.33313C6.94336 4.33313 7.39107 3.88541 7.39107 3.33313C7.39107 2.78085 6.94336 2.33313 6.39107 2.33313C5.83879 2.33313 5.39107 2.78085 5.39107 3.33313C5.39107 3.88541 5.83879 4.33313 6.39107 4.33313ZM6.39107 5.33313C6.66663 5.33313 6.92917 5.2774 7.16804 5.17661L9.24082 7.61448C9.05138 7.92012 8.94202 8.2806 8.94202 8.66663C8.94202 9.7712 9.83745 10.6666 10.942 10.6666C12.0466 10.6666 12.942 9.7712 12.942 8.66663C12.942 8.39426 12.8876 8.1346 12.789 7.89795L14.1837 6.79901C14.4478 6.92753 14.7444 6.99963 15.0578 6.99963C16.1624 6.99963 17.0578 6.1042 17.0578 4.99963C17.0578 3.89506 16.1624 2.99963 15.0578 2.99963C13.9533 2.99963 13.0578 3.89506 13.0578 4.99963C13.0578 5.42151 13.1885 5.81288 13.4115 6.1355L12.1864 7.10077C11.8448 6.82901 11.4124 6.66663 10.942 6.66663C10.5872 6.66663 10.2539 6.75904 9.96498 6.92111L7.96435 4.5681C8.23164 4.22805 8.39107 3.79921 8.39107 3.33313C8.39107 2.22856 7.49564 1.33313 6.39107 1.33313C5.2865 1.33313 4.39107 2.22856 4.39107 3.33313C4.39107 3.95741 4.67709 4.51488 5.12524 4.88165L3.18779 10.6816C3.10724 10.6717 3.02522 10.6666 2.94202 10.6666C1.83745 10.6666 0.942017 11.5621 0.942017 12.6666C0.942017 13.7712 1.83745 14.6666 2.94202 14.6666C4.04659 14.6666 4.94202 13.7712 4.94202 12.6666C4.94202 12.0022 4.61799 11.4134 4.11932 11.0497L6.03923 5.30228C6.15345 5.32255 6.27103 5.33313 6.39107 5.33313ZM10.942 9.66663C11.4943 9.66663 11.942 9.21891 11.942 8.66663C11.942 8.11434 11.4943 7.66663 10.942 7.66663C10.3897 7.66663 9.94202 8.11434 9.94202 8.66663C9.94202 9.21891 10.3897 9.66663 10.942 9.66663ZM16.0578 4.99963C16.0578 5.55192 15.6101 5.99963 15.0578 5.99963C14.5056 5.99963 14.0578 5.55192 14.0578 4.99963C14.0578 4.44735 14.5056 3.99963 15.0578 3.99963C15.6101 3.99963 16.0578 4.44735 16.0578 4.99963ZM2.94202 13.6666C3.4943 13.6666 3.94202 13.2189 3.94202 12.6666C3.94202 12.1143 3.4943 11.6666 2.94202 11.6666C2.38973 11.6666 1.94202 12.1143 1.94202 12.6666C1.94202 13.2189 2.38973 13.6666 2.94202 13.6666Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function DatabaseIcon(props: SVGProps<SVGSVGElement>) {
  const [, theme] = useStyletron();
  return <DatabaseSvgIcon {...props} color={theme.colors['icon.primary']} />;
}

export function IngestionIcon() {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="currentColor">
      <g clipPath="url(#clip0_304_6903)">
        <path d="M2.66668 13.4912H13.3333C14.0667 13.4912 14.6667 12.8912 14.6667 12.1579C14.6667 11.4246 14.0667 10.8246 13.3333 10.8246H2.66668C1.93334 10.8246 1.33334 11.4246 1.33334 12.1579C1.33334 12.8912 1.93334 13.4912 2.66668 13.4912ZM2.66668 11.4912H4.00001V12.8246H2.66668V11.4912ZM1.33334 4.15789C1.33334 4.89122 1.93334 5.49122 2.66668 5.49122H13.3333C14.0667 5.49122 14.6667 4.89122 14.6667 4.15789C14.6667 3.42455 14.0667 2.82455 13.3333 2.82455H2.66668C1.93334 2.82455 1.33334 3.42455 1.33334 4.15789ZM4.00001 4.82455H2.66668V3.49122H4.00001V4.82455ZM2.66668 9.49122H13.3333C14.0667 9.49122 14.6667 8.89122 14.6667 8.15789C14.6667 7.42455 14.0667 6.82455 13.3333 6.82455H2.66668C1.93334 6.82455 1.33334 7.42455 1.33334 8.15789C1.33334 8.89122 1.93334 9.49122 2.66668 9.49122ZM2.66668 7.49122H4.00001V8.82455H2.66668V7.49122Z" />
      </g>
      <defs>
        <clipPath id="clip0_304_6903">
          <rect width="16" height="16" fill="white" transform="translate(0 0.157898)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function QueriesIcon() {
  const [_, theme] = useStyletron();

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <g clipPath="url(#clip0_806_85330)">
        <path
          d="M12.2966 2.5835H2.17636C1.71252 2.5835 1.33301 3.01773 1.33301 3.54845C1.33301 4.07918 1.71252 4.51341 2.17636 4.51341H12.2966C12.7605 4.51341 13.14 4.07918 13.14 3.54845C13.14 3.01773 12.7605 2.5835 12.2966 2.5835Z"
          fill={theme.colors['icon.primary']}
        />
        <path
          d="M6.21177 6.81104H2.17636C1.71252 6.81104 1.33301 7.24527 1.33301 7.77599C1.33301 8.30672 1.71252 8.74095 2.17636 8.74095H6.21177C6.67561 8.74095 7.05512 8.30672 7.05512 7.77599C7.05512 7.24527 6.67561 6.81104 6.21177 6.81104Z"
          fill={theme.colors['icon.primary']}
        />
        <path
          d="M2.17636 10.9465H7.32527C7.78911 10.9465 8.16862 11.3807 8.16862 11.9114C8.16862 12.4421 7.78911 12.8764 7.32527 12.8764H2.17636C1.71252 12.8764 1.33301 12.4421 1.33301 11.9114C1.33301 11.3807 1.71252 10.9465 2.17636 10.9465Z"
          fill={theme.colors['icon.primary']}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.8878 10.2101L12.5399 10.2506L12.4305 10.1413C12.8135 9.68008 13.0441 9.08128 13.0441 8.42988C13.0441 6.97739 11.9068 5.80002 10.5037 5.80002C9.10065 5.80002 7.96333 6.97739 7.96333 8.42988C7.96333 9.88237 9.10065 11.0597 10.5037 11.0597C11.133 11.0597 11.7114 10.821 12.1569 10.4245L12.2625 10.5378L12.2234 10.8979L14.1775 12.9168L14.838 12.2331L12.8878 10.2101ZM11.9025 8.43864C11.9025 9.23352 11.28 9.8779 10.5122 9.8779C9.74435 9.8779 9.12189 9.23352 9.12189 8.43864C9.12189 7.64376 9.74435 6.99938 10.5122 6.99938C11.28 6.99938 11.9025 7.64376 11.9025 8.43864Z"
          fill={theme.colors['icon.primary']}
        />
      </g>
      <defs>
        <clipPath id="clip0_806_85330">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function EditorIcon() {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="currentColor">
      <g clipPath="url(#clip0_304_6907)">
        <path d="M5.73347 11.2246L2.6668 8.1579L5.73347 5.09123L4.80013 4.1579L0.800132 8.1579L4.80013 12.1579L5.73347 11.2246ZM10.2667 11.2246L13.3333 8.1579L10.2667 5.09123L11.2 4.1579L15.2 8.1579L11.2 12.1579L10.2667 11.2246Z" />
        <path d="M7.33332 12.1579L8.66666 4.1579" stroke="currentColor" fill="currentColor" strokeWidth="2" />
      </g>
      <defs>
        <clipPath id="clip0_304_6907">
          <rect width="16" height="16" fill="white" transform="translate(0 0.157898)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ExplorerIcon() {
  return (
    <svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.75004 11.75H8.25004V12.25H7.75004V11.75ZM7.75004 10.75H8.25004V11.25H7.75004V10.75ZM11.1018 6.16929L11.5285 5.95604L11.7523 6.40304L11.3253 6.61654L11.1018 6.16929ZM13.0343 5.76254L13.2528 5.65329C13.4453 5.87379 13.7175 6.00004 14.001 6.00004C14.1544 5.99996 14.3057 5.9646 14.4433 5.89668C14.5808 5.82877 14.7009 5.73013 14.7942 5.60839C14.8875 5.48664 14.9516 5.34507 14.9814 5.19461C15.0113 5.04415 15.0061 4.88885 14.9663 4.74071C14.9265 4.59257 14.8532 4.45556 14.752 4.34029C14.6508 4.22502 14.5244 4.13458 14.3827 4.07596C14.241 4.01734 14.0876 3.99211 13.9346 4.00222C13.7815 4.01234 13.6328 4.05753 13.5 4.13429C13.115 4.35629 12.9385 4.79504 13.0263 5.20729L12.8103 5.31529L13.0343 5.76254ZM13.75 4.56729C13.8273 4.52254 13.911 4.50004 13.9993 4.50004C14.1778 4.50004 14.3438 4.59579 14.433 4.75004C14.4767 4.826 14.4997 4.9121 14.4997 4.99973C14.4996 5.08735 14.4766 5.17343 14.4329 5.24936C14.3891 5.32529 14.3262 5.3884 14.2504 5.4324C14.1747 5.47639 14.0887 5.49972 14.001 5.50004C13.9132 5.49997 13.8269 5.47684 13.7508 5.43297C13.6747 5.3891 13.6114 5.32603 13.5673 5.25004C13.5012 5.13523 13.4833 4.99888 13.5176 4.8709C13.5518 4.74291 13.6354 4.63374 13.75 4.56729ZM11.9558 5.74254L12.3833 5.52879L12.6068 5.97604L12.1793 6.18954L11.9558 5.74254ZM10.2475 6.59679L10.6745 6.38304L10.8983 6.83029L10.4708 7.04379L10.2475 6.59679ZM4.24754 6.40329L4.47104 5.95604L4.89779 6.16954L4.67429 6.61679L4.24754 6.40329ZM3.39304 5.97604L3.61654 5.52904L4.04404 5.74254L3.82029 6.18979L3.39304 5.97604ZM5.10154 6.83054L5.32504 6.38304L5.75254 6.59679L5.52879 7.04429L5.10154 6.83054ZM1.50004 5.86579C1.65754 5.95679 1.82954 6.00004 1.99904 6.00004C2.28254 6.00004 2.55479 5.87379 2.74754 5.65329L2.96579 5.76254L3.18954 5.31504L2.97354 5.20729C3.06104 4.79479 2.88479 4.35629 2.50004 4.13404C2.38634 4.06836 2.26082 4.02572 2.13064 4.00857C2.00045 3.99141 1.86817 4.00008 1.74133 4.03407C1.6145 4.06805 1.49561 4.1267 1.39145 4.20666C1.28729 4.28661 1.19991 4.38631 1.13429 4.50004C1.00175 4.72967 0.965821 5.00253 1.03441 5.25864C1.10299 5.51475 1.27048 5.73314 1.50004 5.86579ZM1.56704 4.75004C1.6112 4.67406 1.67452 4.61099 1.75068 4.56712C1.82683 4.52325 1.91316 4.50012 2.00104 4.50004C2.08867 4.50037 2.17467 4.5237 2.25045 4.56769C2.32623 4.61169 2.38913 4.6748 2.43287 4.75073C2.4766 4.82666 2.49964 4.91274 2.49967 5.00036C2.4997 5.08799 2.47672 5.17408 2.43304 5.25004C2.34379 5.40429 2.17779 5.50004 1.99904 5.50004C1.91142 5.49972 1.82542 5.47639 1.74964 5.4324C1.67386 5.3884 1.61095 5.32529 1.56722 5.24936C1.52348 5.17343 1.50045 5.08735 1.50042 4.99973C1.50039 4.9121 1.52336 4.826 1.56704 4.75004ZM8.50004 13.134C8.4217 13.089 8.3376 13.0547 8.25004 13.0323V12.75H7.75004V13.0325C7.52569 13.0906 7.32846 13.2248 7.19209 13.4122C7.05571 13.5996 6.98864 13.8285 7.00236 14.0598C7.01607 14.2912 7.10971 14.5106 7.26727 14.6805C7.42483 14.8505 7.63653 14.9604 7.86617 14.9916C8.09582 15.0227 8.32915 14.9731 8.5263 14.8513C8.72344 14.7295 8.87215 14.543 8.94701 14.3236C9.02186 14.1043 9.01821 13.8658 8.93669 13.6489C8.85516 13.4319 8.70082 13.2498 8.50004 13.134ZM8.43304 14.25C8.34379 14.4043 8.17779 14.5 7.99904 14.5C7.91142 14.4997 7.82542 14.4764 7.74964 14.4324C7.67386 14.3884 7.61095 14.3253 7.56722 14.2494C7.52348 14.1734 7.50045 14.0873 7.50042 13.9997C7.50039 13.9121 7.52336 13.826 7.56704 13.75C7.6112 13.6741 7.67452 13.611 7.75068 13.5671C7.82683 13.5233 7.91316 13.5001 8.00104 13.5C8.08867 13.5004 8.17467 13.5237 8.25045 13.5677C8.32623 13.6117 8.38913 13.6748 8.43287 13.7507C8.4766 13.8267 8.49964 13.9127 8.49967 14.0004C8.4997 14.088 8.47672 14.1741 8.43304 14.25Z"
        fill="currentColor"
      />
      <path
        d="M13.5 9.85001H13.5C13.0592 9.85001 12.663 10.0258 12.3687 10.306L9.99289 8.79402C10.0953 8.54241 10.1487 8.27314 10.15 8.00068V8.00001C10.15 7.7946 10.1159 7.59983 10.0618 7.41629L10.111 7.39167L10.2452 7.32457L10.1781 7.19039L9.95435 6.74314L9.88721 6.60894L9.75303 6.67613L9.70976 6.6978C9.39367 6.2846 8.92992 5.99634 8.39995 5.89698V4.09658C9.11684 3.91785 9.64995 3.27351 9.64995 2.49976C9.64995 2.06215 9.47611 1.64246 9.16668 1.33303C8.85724 1.02359 8.43756 0.849756 7.99995 0.849756C7.56234 0.849756 7.14266 1.02359 6.83322 1.33303C6.52379 1.64246 6.34995 2.06215 6.34995 2.49976C6.34995 3.27351 6.88306 3.91785 7.59995 4.09658V5.89698C7.06997 5.99637 6.60623 6.28481 6.29016 6.69782L6.24687 6.67613L6.11266 6.60892L6.04554 6.74317L5.82179 7.19067L5.75462 7.325L5.88903 7.392L5.93803 7.41643C5.88398 7.59979 5.84995 7.79451 5.84995 8.00001C5.84995 8.28281 5.90859 8.54922 6.00768 8.79374L3.63121 10.306C3.3369 10.0258 2.94071 9.85001 2.49995 9.85001H2.4999C2.14547 9.85013 1.8005 9.96438 1.51607 10.1758C1.23163 10.3873 1.02284 10.6847 0.920613 11.0241L1.06407 11.0673L0.920613 11.0241C0.818384 11.3635 0.828149 11.7267 0.94846 12.0601C1.06877 12.3935 1.29324 12.6793 1.58862 12.8751C1.88401 13.071 2.23461 13.1666 2.58854 13.1477C2.94246 13.1287 3.28089 12.9964 3.55371 12.7701C3.82654 12.5439 4.01927 12.2358 4.10337 11.8915C4.17733 11.5887 4.16381 11.272 4.06571 10.9779L6.4403 9.46665C6.74958 9.79634 7.15577 10.0193 7.59995 10.1032V10.25V10.4H7.74995H8.24995H8.39995V10.25V10.1032C8.84412 10.0193 9.2503 9.7963 9.5596 9.46664L11.9344 10.9779C11.8363 11.272 11.8228 11.5887 11.8968 11.8915C11.9809 12.2359 12.1736 12.5439 12.4465 12.7702C12.7193 12.9964 13.0578 13.1288 13.4117 13.1477C13.7657 13.1666 14.1163 13.071 14.4116 12.8751C14.707 12.6792 14.9315 12.3933 15.0517 12.0599C15.172 11.7265 15.1818 11.3633 15.0795 11.0239C14.9772 10.6845 14.7684 10.3871 14.4839 10.1757C14.1994 9.96427 13.8544 9.85007 13.5 9.85001ZM2.49995 12.35C2.03129 12.35 1.64995 11.9687 1.64995 11.5C1.64995 11.0313 2.03129 10.65 2.49995 10.65C2.96861 10.65 3.34995 11.0313 3.34995 11.5C3.34995 11.9687 2.96861 12.35 2.49995 12.35ZM7.14995 2.50001C7.14995 2.03135 7.53129 1.65001 7.99995 1.65001C8.46861 1.65001 8.84995 2.03135 8.84995 2.50001C8.84995 2.96866 8.46861 3.35001 7.99995 3.35001C7.53129 3.35001 7.14995 2.96866 7.14995 2.50001ZM7.99995 9.35001C7.25579 9.35001 6.64995 8.74416 6.64995 8.00001C6.64995 7.25585 7.25579 6.65001 7.99995 6.65001C8.74411 6.65001 9.34995 7.25585 9.34995 8.00001C9.34995 8.74416 8.74411 9.35001 7.99995 9.35001ZM13.5 12.35C13.0313 12.35 12.65 11.9687 12.65 11.5C12.65 11.0313 13.0313 10.65 13.5 10.65C13.9686 10.65 14.35 11.0313 14.35 11.5C14.35 11.9687 13.9686 12.35 13.5 12.35Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.3"
      />
    </svg>
  );
}

export function IntegrationIcon({ height = 16, width = 16 }: { height?: number; width?: number }) {
  return (
    <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_507_639)">
        <path
          d="M12.6667 2H3.33333C2.6 2 2 2.6 2 3.33333V8C2 8.73333 2.6 9.33333 3.33333 9.33333H12.6667C13.4 9.33333 14 8.73333 14 8V3.33333C14 2.6 13.4 2 12.6667 2ZM12.6667 6H10.5733C10.26 6 10.0133 6.22 9.92667 6.52C9.68667 7.36 8.9 8 8 8C7.1 8 6.31333 7.36 6.07333 6.52C5.98667 6.22 5.74 6 5.42667 6H3.33333V4C3.33333 3.63333 3.63333 3.33333 4 3.33333H12C12.3667 3.33333 12.6667 3.63333 12.6667 4V6ZM10.58 10.6667H13.3333C13.7 10.6667 14 10.9667 14 11.3333V12.6667C14 13.4 13.4 14 12.6667 14H3.33333C2.6 14 2 13.4 2 12.6667V11.3333C2 10.9667 2.3 10.6667 2.66667 10.6667H5.42C5.73333 10.6667 5.98667 10.8933 6.07333 11.2C6.30667 12.0467 7.08 12.6667 8 12.6667C8.92 12.6667 9.69333 12.0467 9.92667 11.2C10.0133 10.8933 10.2667 10.6667 10.58 10.6667Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_507_639">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function AdminIcon() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10.8081 9.81122C10.1563 9.81122 9.62601 10.3413 9.62601 10.9925C9.62601 11.6439 10.1563 12.1739 10.8081 12.1739C11.4601 12.1739 11.9904 11.6439 11.9904 10.9925C11.9904 10.3413 11.4601 9.81122 10.8081 9.81122ZM10.8081 11.5652C10.4921 11.5652 10.2351 11.3083 10.2351 10.9925C10.2351 10.6769 10.4921 10.4199 10.8081 10.4199C11.1242 10.4199 11.3814 10.6769 11.3814 10.9925C11.3814 11.3083 11.1242 11.5652 10.8081 11.5652ZM13.934 10.3753C13.8896 10.1414 13.7394 9.99021 13.5465 9.99021H13.5198C13.1507 9.99021 12.8503 9.69024 12.8503 9.3216C12.8503 9.22479 12.8927 9.10585 12.9088 9.06857C13.0002 8.86297 12.9299 8.61064 12.7268 8.47015L11.9441 8.03715C11.8828 8.0107 11.8165 7.99719 11.7471 7.99719C11.6053 7.99719 11.4652 8.05491 11.3724 8.15102C11.2749 8.25144 11.016 8.45868 10.8376 8.45868C10.6575 8.45868 10.397 8.24742 10.2993 8.14549C10.206 8.04699 10.0646 7.98816 9.92127 7.98816C9.8526 7.98816 9.78731 8.00127 9.72866 8.02661L9.71403 8.03261L8.91753 8.46969L8.89859 8.48192C8.71344 8.61064 8.64272 8.86285 8.73349 9.06665C8.73955 9.08121 8.79278 9.21064 8.79278 9.3216C8.79278 9.69024 8.49244 9.99021 8.12325 9.99021H8.09206C7.90386 9.99021 7.75351 10.1414 7.70917 10.3756C7.70241 10.4116 7.64312 10.735 7.64312 10.9965C7.64312 11.2581 7.70241 11.5814 7.70917 11.6178C7.7535 11.8516 7.90373 12.0029 8.09673 12.0029H8.12319C8.49238 12.0029 8.79278 12.3029 8.79278 12.6715C8.79278 12.7683 8.7504 12.8872 8.73431 12.9245C8.64307 13.1299 8.71315 13.3819 8.91549 13.5229L9.68342 13.9504C9.74463 13.977 9.81092 13.9907 9.88052 13.9907C10.0249 13.9907 10.1665 13.9304 10.259 13.8296C10.3535 13.727 10.6221 13.5023 10.8056 13.5023C10.9909 13.5023 11.2565 13.7275 11.3558 13.8363C11.4489 13.9388 11.5914 14 11.737 14C11.8054 14 11.8706 13.9869 11.9498 13.9518L12.7262 13.5232L12.7448 13.5109C12.9297 13.3825 13.0003 13.1305 12.9096 12.9265C12.9036 12.9118 12.8503 12.7825 12.8503 12.6715C12.8503 12.3029 13.1507 12.0029 13.5198 12.0029H13.5506C13.7392 12.0029 13.8896 11.8516 13.9338 11.6184C13.9344 11.6151 14 11.2716 14 10.9965C14 10.735 13.9408 10.4116 13.934 10.3753ZM13.3522 11.4051C12.7217 11.4875 12.2413 12.0254 12.2413 12.6715C12.2413 12.8206 12.2774 12.9623 12.3091 13.0582L11.7488 13.3676C11.6984 13.3183 11.618 13.2444 11.5184 13.1706C11.2708 12.987 11.0309 12.8936 10.8056 12.8936C10.5822 12.8936 10.3443 12.9852 10.0982 13.1654C9.99998 13.2375 9.92041 13.309 9.86922 13.3586L9.33366 13.0592C9.36543 12.9632 9.40175 12.8206 9.40175 12.6715C9.40175 12.0254 8.92151 11.4875 8.29098 11.4051C8.27483 11.3008 8.25221 11.1308 8.25221 10.9965C8.25221 10.8615 8.27471 10.692 8.29098 10.588C8.92139 10.5056 9.40175 9.96755 9.40175 9.3216C9.40175 9.17284 9.36572 9.03124 9.33407 8.93531L9.91236 8.61821C9.96255 8.66539 10.0431 8.7364 10.142 8.80676C10.3848 8.97969 10.6189 9.06747 10.8376 9.06747C11.0541 9.06747 11.2862 8.98138 11.5274 8.81165C11.6255 8.7428 11.705 8.67396 11.7556 8.62701L12.3093 8.93443C12.2776 9.03042 12.2414 9.17243 12.2414 9.3216C12.2414 9.96766 12.7216 10.5056 13.3521 10.588C13.3686 10.6942 13.391 10.8633 13.391 10.9966C13.391 11.1307 13.3686 11.2993 13.3522 11.4051ZM2.79028 13.1347V12.4657C2.79028 10.1912 4.54581 8.31668 6.78241 8.10543L6.78144 8.10496C6.78144 8.10496 6.78369 8.10496 6.78727 8.10508C6.92438 8.0922 7.06309 8.08498 7.20353 8.08498C7.22467 8.08498 7.2455 8.08615 7.26654 8.08638C7.76119 8.04805 8.50965 7.93193 8.90081 7.58307L9.09226 7.434C9.84008 6.85052 10.269 5.97881 10.269 5.04253C10.269 3.36483 8.89364 2 7.20292 2C5.5122 2 4.13684 3.36483 4.13684 5.04252C4.13684 5.97879 4.5658 6.85051 5.31362 7.43399L5.50501 7.58306L5.27949 7.67253C3.28729 8.46283 2 10.3442 2 12.4657V13.526C2 13.7419 2.17713 13.9175 2.39507 13.9175H8.16822C7.98027 13.677 7.82008 13.4147 7.69058 13.1347H2.79028ZM4.92712 5.04252C4.92712 3.79646 5.948 2.78277 7.20292 2.78277C8.45782 2.78277 9.47887 3.79646 9.47887 5.04252C9.47887 6.28861 8.45782 7.30224 7.20292 7.30224C5.948 7.30223 4.92712 6.28861 4.92712 5.04252Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.5"
      />
    </svg>
  );
}

export function UserIcon() {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="currentColor">
      <path d="M6.00001 8.15791C7.28668 8.15791 8.33334 7.11124 8.33334 5.82457C8.33334 4.53791 7.28668 3.49124 6.00001 3.49124C4.71334 3.49124 3.66668 4.53791 3.66668 5.82457C3.66668 7.11124 4.71334 8.15791 6.00001 8.15791ZM6.00001 4.82457C6.55334 4.82457 7.00001 5.27124 7.00001 5.82457C7.00001 6.37791 6.55334 6.82458 6.00001 6.82458C5.44668 6.82458 5.00001 6.37791 5.00001 5.82457C5.00001 5.27124 5.44668 4.82457 6.00001 4.82457ZM6.00001 9.32457C4.44001 9.32457 1.33334 10.1046 1.33334 11.6579V12.1579C1.33334 12.5246 1.63334 12.8246 2.00001 12.8246H10C10.3667 12.8246 10.6667 12.5246 10.6667 12.1579V11.6579C10.6667 10.1046 7.56001 9.32457 6.00001 9.32457ZM2.89334 11.4912C3.45334 11.1046 4.80668 10.6579 6.00001 10.6579C7.19334 10.6579 8.54668 11.1046 9.10668 11.4912H2.89334ZM10.6933 9.36458C11.4667 9.92458 12 10.6712 12 11.6579V12.8246H14C14.3667 12.8246 14.6667 12.5246 14.6667 12.1579V11.6579C14.6667 10.3112 12.3333 9.54458 10.6933 9.36458ZM10 8.15791C11.2867 8.15791 12.3333 7.11124 12.3333 5.82457C12.3333 4.53791 11.2867 3.49124 10 3.49124C9.64001 3.49124 9.30668 3.57791 9.00001 3.72457C9.42001 4.31791 9.66668 5.04457 9.66668 5.82457C9.66668 6.60457 9.42001 7.33124 9.00001 7.92457C9.30668 8.07124 9.64001 8.15791 10 8.15791Z" />
    </svg>
  );
}

export function ActivityIcon() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <mask id="path-1-inside-1_440_388" fill="white">
        <path d="M7.99331 1.33325C4.31331 1.33325 1.33331 4.31992 1.33331 7.99992C1.33331 11.6799 4.31331 14.6666 7.99331 14.6666C11.68 14.6666 14.6666 11.6799 14.6666 7.99992C14.6666 4.31992 11.68 1.33325 7.99331 1.33325ZM7.99998 13.3333C5.05331 13.3333 2.66665 10.9466 2.66665 7.99992C2.66665 5.05325 5.05331 2.66659 7.99998 2.66659C10.9466 2.66659 13.3333 5.05325 13.3333 7.99992C13.3333 10.9466 10.9466 13.3333 7.99998 13.3333ZM7.85331 4.66659H7.81331C7.54665 4.66659 7.33331 4.87992 7.33331 5.14659V8.29325C7.33331 8.52659 7.45331 8.74659 7.65998 8.86659L10.4266 10.5266C10.6533 10.6599 10.9466 10.5933 11.08 10.3666C11.1135 10.3126 11.1357 10.2524 11.1455 10.1897C11.1552 10.1269 11.1522 10.0628 11.1366 10.0012C11.1211 9.93965 11.0933 9.88182 11.0549 9.8312C11.0166 9.78058 10.9684 9.7382 10.9133 9.70659L8.33331 8.17325V5.14659C8.33331 4.87992 8.11998 4.66659 7.85331 4.66659Z" />
      </mask>
      <path d="M7.99331 1.33325C4.31331 1.33325 1.33331 4.31992 1.33331 7.99992C1.33331 11.6799 4.31331 14.6666 7.99331 14.6666C11.68 14.6666 14.6666 11.6799 14.6666 7.99992C14.6666 4.31992 11.68 1.33325 7.99331 1.33325ZM7.99998 13.3333C5.05331 13.3333 2.66665 10.9466 2.66665 7.99992C2.66665 5.05325 5.05331 2.66659 7.99998 2.66659C10.9466 2.66659 13.3333 5.05325 13.3333 7.99992C13.3333 10.9466 10.9466 13.3333 7.99998 13.3333ZM7.85331 4.66659H7.81331C7.54665 4.66659 7.33331 4.87992 7.33331 5.14659V8.29325C7.33331 8.52659 7.45331 8.74659 7.65998 8.86659L10.4266 10.5266C10.6533 10.6599 10.9466 10.5933 11.08 10.3666C11.1135 10.3126 11.1357 10.2524 11.1455 10.1897C11.1552 10.1269 11.1522 10.0628 11.1366 10.0012C11.1211 9.93965 11.0933 9.88182 11.0549 9.8312C11.0166 9.78058 10.9684 9.7382 10.9133 9.70659L8.33331 8.17325V5.14659C8.33331 4.87992 8.11998 4.66659 7.85331 4.66659Z" />
      <path
        d="M7.65998 8.86659L8.17448 8.00909L8.16832 8.0054L8.16212 8.0018L7.65998 8.86659ZM10.4266 10.5266L9.91213 11.3841L9.91963 11.3885L10.4266 10.5266ZM11.08 10.3666L10.2301 9.83955L10.224 9.84949L10.218 9.85957L11.08 10.3666ZM10.9133 9.70659L10.4024 10.5662L10.409 10.5701L10.4156 10.5739L10.9133 9.70659ZM8.33331 8.17325H7.33331V8.74221L7.82242 9.03289L8.33331 8.17325ZM7.99331 0.333252C3.75973 0.333252 0.333313 3.76894 0.333313 7.99992H2.33331C2.33331 4.8709 4.8669 2.33325 7.99331 2.33325V0.333252ZM0.333313 7.99992C0.333313 12.2309 3.75973 15.6666 7.99331 15.6666V13.6666C4.8669 13.6666 2.33331 11.1289 2.33331 7.99992H0.333313ZM7.99331 15.6666C12.2316 15.6666 15.6666 12.2329 15.6666 7.99992H13.6666C13.6666 11.127 11.1283 13.6666 7.99331 13.6666V15.6666ZM15.6666 7.99992C15.6666 3.76698 12.2316 0.333252 7.99331 0.333252V2.33325C11.1283 2.33325 13.6666 4.87285 13.6666 7.99992H15.6666ZM7.99998 12.3333C5.6056 12.3333 3.66665 10.3943 3.66665 7.99992H1.66665C1.66665 11.4989 4.50103 14.3333 7.99998 14.3333V12.3333ZM3.66665 7.99992C3.66665 5.60554 5.6056 3.66659 7.99998 3.66659V1.66659C4.50103 1.66659 1.66665 4.50097 1.66665 7.99992H3.66665ZM7.99998 3.66659C10.3944 3.66659 12.3333 5.60554 12.3333 7.99992H14.3333C14.3333 4.50097 11.4989 1.66659 7.99998 1.66659V3.66659ZM12.3333 7.99992C12.3333 10.3943 10.3944 12.3333 7.99998 12.3333V14.3333C11.4989 14.3333 14.3333 11.4989 14.3333 7.99992H12.3333ZM7.85331 3.66659H7.81331V5.66659H7.85331V3.66659ZM7.81331 3.66659C6.99436 3.66659 6.33331 4.32763 6.33331 5.14659H8.33331C8.33331 5.4322 8.09893 5.66659 7.81331 5.66659V3.66659ZM6.33331 5.14659V8.29325H8.33331V5.14659H6.33331ZM6.33331 8.29325C6.33331 8.86811 6.63034 9.42508 7.15784 9.73137L8.16212 8.0018C8.27628 8.06809 8.33331 8.18506 8.33331 8.29325H6.33331ZM7.14548 9.72408L9.91215 11.3841L10.9411 9.66909L8.17448 8.00909L7.14548 9.72408ZM9.91963 11.3885C10.5917 11.7839 11.5109 11.6064 11.9419 10.8736L10.218 9.85957C10.3824 9.58015 10.7149 9.53597 10.9337 9.66465L9.91963 11.3885ZM11.9298 10.8936C12.034 10.7256 12.1033 10.5383 12.1336 10.3429L10.1573 10.0364C10.1681 9.96657 10.1929 9.89962 10.2301 9.83955L11.9298 10.8936ZM12.1336 10.3429C12.1639 10.1476 12.1546 9.94809 12.1062 9.75639L10.1671 10.2461C10.1498 10.1775 10.1464 10.1062 10.1573 10.0364L12.1336 10.3429ZM12.1062 9.75639C12.0578 9.56469 11.9713 9.3847 11.8519 9.22714L10.258 10.4353C10.2153 10.3789 10.1844 10.3146 10.1671 10.2461L12.1062 9.75639ZM11.8519 9.22714C11.7325 9.06958 11.5825 8.93766 11.411 8.83925L10.4156 10.5739C10.3543 10.5387 10.3007 10.4916 10.258 10.4353L11.8519 9.22714ZM11.4242 8.84694L8.84421 7.31361L7.82242 9.03289L10.4024 10.5662L11.4242 8.84694ZM9.33331 8.17325V5.14659H7.33331V8.17325H9.33331ZM9.33331 5.14659C9.33331 4.32763 8.67226 3.66659 7.85331 3.66659V5.66659C7.5677 5.66659 7.33331 5.4322 7.33331 5.14659H9.33331Z"
        mask="url(#path-1-inside-1_440_388)"
      />
    </svg>
  );
}

export function BillIcon() {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="currentColor">
      <path d="M13.3333 2.82455H2.66668C1.92668 2.82455 1.34001 3.41789 1.34001 4.15789L1.33334 12.1579C1.33334 12.8979 1.92668 13.4912 2.66668 13.4912H13.3333C14.0733 13.4912 14.6667 12.8979 14.6667 12.1579V4.15789C14.6667 3.41789 14.0733 2.82455 13.3333 2.82455ZM13.3333 12.1579H2.66668V8.15789H13.3333V12.1579ZM13.3333 5.49122H2.66668V4.15789H13.3333V5.49122Z" />
    </svg>
  );
}

export function SettingsIcon() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_440_401)">
        <path d="M12.9533 8.65325C12.98 8.43992 13 8.22659 13 7.99992C13 7.77325 12.98 7.55992 12.9533 7.34659L14.36 6.24659C14.4867 6.14659 14.52 5.96659 14.44 5.81992L13.1067 3.51325C13.0267 3.36659 12.8467 3.31325 12.7 3.36659L11.04 4.03325C10.6933 3.76659 10.32 3.54659 9.91335 3.37992L9.66002 1.61325C9.64002 1.45325 9.50002 1.33325 9.33335 1.33325H6.66668C6.50002 1.33325 6.36002 1.45325 6.34002 1.61325L6.08668 3.37992C5.68002 3.54659 5.30668 3.77325 4.96002 4.03325L3.30002 3.36659C3.14668 3.30659 2.97335 3.36659 2.89335 3.51325L1.56001 5.81992C1.47335 5.96659 1.51335 6.14659 1.64002 6.24659L3.04668 7.34659C3.02002 7.55992 3.00002 7.77992 3.00002 7.99992C3.00002 8.21992 3.02002 8.43992 3.04668 8.65325L1.64002 9.75325C1.51335 9.85325 1.48001 10.0333 1.56001 10.1799L2.89335 12.4866C2.97335 12.6333 3.15335 12.6866 3.30002 12.6333L4.96002 11.9666C5.30668 12.2333 5.68002 12.4533 6.08668 12.6199L6.34002 14.3866C6.36002 14.5466 6.50002 14.6666 6.66668 14.6666H9.33335C9.50002 14.6666 9.64002 14.5466 9.66002 14.3866L9.91335 12.6199C10.32 12.4533 10.6933 12.2266 11.04 11.9666L12.7 12.6333C12.8533 12.6933 13.0267 12.6333 13.1067 12.4866L14.44 10.1799C14.52 10.0333 14.4867 9.85325 14.36 9.75325L12.9533 8.65325V8.65325ZM8.00002 10.3333C6.71335 10.3333 5.66668 9.28659 5.66668 7.99992C5.66668 6.71325 6.71335 5.66659 8.00002 5.66659C9.28668 5.66659 10.3333 6.71325 10.3333 7.99992C10.3333 9.28659 9.28668 10.3333 8.00002 10.3333Z" />
      </g>
      <defs>
        <clipPath id="clip0_440_401">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function AddIcon() {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="currentColor">
      <g clipPath="url(#clip0_358_1776)">
        <path d="M8 4.82454C7.63333 4.82454 7.33333 5.12454 7.33333 5.49121V7.49121H5.33333C4.96666 7.49121 4.66666 7.79121 4.66666 8.15788C4.66666 8.52454 4.96666 8.82454 5.33333 8.82454H7.33333V10.8245C7.33333 11.1912 7.63333 11.4912 8 11.4912C8.36666 11.4912 8.66666 11.1912 8.66666 10.8245V8.82454H10.6667C11.0333 8.82454 11.3333 8.52454 11.3333 8.15788C11.3333 7.79121 11.0333 7.49121 10.6667 7.49121H8.66666V5.49121C8.66666 5.12454 8.36666 4.82454 8 4.82454ZM8 1.49121C4.32666 1.49121 1.33333 4.48454 1.33333 8.15788C1.33333 11.8312 4.32666 14.8245 8 14.8245C11.6733 14.8245 14.6667 11.8312 14.6667 8.15788C14.6667 4.48454 11.6733 1.49121 8 1.49121ZM8 13.4912C5.06 13.4912 2.66666 11.0979 2.66666 8.15788C2.66666 5.21788 5.06 2.82454 8 2.82454C10.94 2.82454 13.3333 5.21788 13.3333 8.15788C13.3333 11.0979 10.94 13.4912 8 13.4912Z" />
      </g>
      <defs>
        <clipPath id="clip0_358_1776">
          <rect width="16" height="16" fill="white" transform="translate(0 0.157898)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function VertexIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 14.5C11.5899 14.5 14.5 11.5899 14.5 8C14.5 4.41015 11.5899 1.5 8 1.5C4.41015 1.5 1.5 4.41015 1.5 8C1.5 11.5899 4.41015 14.5 8 14.5ZM14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8ZM8 13C10.7614 13 13 10.7614 13 8C13 5.23858 10.7614 3 8 3C5.23858 3 3 5.23858 3 8C3 10.7614 5.23858 13 8 13Z"
        fill={theme.colors['icon.primary']}
      />
    </svg>
  );
}

export function EdgeIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M14 3.5C14 4.32843 13.3284 5 12.5 5C12.3849 5 12.2728 4.98703 12.1651 4.96247L4.96247 12.1651C4.98703 12.2728 5 12.3849 5 12.5C5 13.3284 4.32843 14 3.5 14C2.67157 14 2 13.3284 2 12.5C2 11.6716 2.67157 11 3.5 11C3.71793 11 3.92501 11.0465 4.11187 11.1301L11.1301 4.11187C11.0465 3.92501 11 3.71793 11 3.5C11 2.67157 11.6716 2 12.5 2C13.3284 2 14 2.67157 14 3.5Z"
        fill={theme.colors['icon.primary']}
      />
    </svg>
  );
}

export function FolderIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="folder" clipPath="url(#clip0_2659_24121)">
        <path
          id="Vector"
          d="M8.33329 3.3335H3.33329C2.41663 3.3335 1.67496 4.0835 1.67496 5.00016L1.66663 15.0002C1.66663 15.9168 2.41663 16.6668 3.33329 16.6668H16.6666C17.5833 16.6668 18.3333 15.9168 18.3333 15.0002V6.66683C18.3333 5.75016 17.5833 5.00016 16.6666 5.00016H9.99996L8.33329 3.3335Z"
          fill={theme.colors['icon.primary']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2659_24121">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function SharedFolderIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="folder_shared" clipPath="url(#clip0_2659_21428)">
        <path
          id="Vector"
          d="M16.6666 5.00016H9.99996L8.33329 3.3335H3.33329C2.41663 3.3335 1.67496 4.0835 1.67496 5.00016L1.66663 15.0002C1.66663 15.9168 2.41663 16.6668 3.33329 16.6668H16.6666C17.5833 16.6668 18.3333 15.9168 18.3333 15.0002V6.66683C18.3333 5.75016 17.5833 5.00016 16.6666 5.00016ZM12.5 7.50016C13.4166 7.50016 14.1666 8.25016 14.1666 9.16683C14.1666 10.0835 13.4166 10.8335 12.5 10.8335C11.5833 10.8335 10.8333 10.0835 10.8333 9.16683C10.8333 8.25016 11.5833 7.50016 12.5 7.50016ZM15.8333 14.1668H9.16663V13.3335C9.16663 12.2252 11.3916 11.6668 12.5 11.6668C13.6083 11.6668 15.8333 12.2252 15.8333 13.3335V14.1668Z"
          fill={theme.colors['icon.primary']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2659_21428">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function AlgorithmCategoryIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M12.7357 5.93917C11.4639 5.50635 9.78262 5.26885 8.00293 5.26885H7.88887C8.35918 4.65948 8.84355 4.13292 9.31543 3.71885C9.72949 3.35635 10.1186 3.09854 10.4404 2.97354C10.6654 2.88604 10.8498 2.86885 10.9467 2.92354C11.0514 2.98448 11.1311 3.17354 11.1639 3.44385C11.2107 3.8251 11.1639 4.34229 11.0279 4.93917L12.1248 5.1876C12.2904 4.45479 12.3436 3.82198 12.2795 3.30635C12.2248 2.85792 12.0467 2.25948 11.5076 1.94854C10.9904 1.6501 10.0623 1.56729 8.57324 2.87198C7.86074 3.49542 7.13105 4.34385 6.45137 5.32979C5.99355 5.36729 5.55137 5.42042 5.12793 5.48917C5.05137 5.22823 4.98887 4.9751 4.94043 4.73292C4.83262 4.19229 4.80449 3.72667 4.85762 3.38604C4.89512 3.14854 4.97012 2.97979 5.06699 2.92354C5.22637 2.83135 5.79355 2.89698 6.79355 3.80479L7.5498 2.97198C7.01074 2.48292 6.50137 2.1376 6.03418 1.94542C5.62793 1.77823 5.03262 1.64385 4.50449 1.94854C3.9873 2.24698 3.45137 3.01104 3.8373 4.95167C3.88574 5.19854 3.94824 5.45323 4.02324 5.71729C3.75918 5.78448 3.50762 5.85792 3.27012 5.93917C1.39668 6.57667 1.00293 7.42198 1.00293 8.01885C1.00293 8.63292 1.42168 9.08292 1.7748 9.35167C2.17949 9.66104 2.74043 9.93135 3.44199 10.1532L3.78262 9.08135C2.4748 8.66729 2.12949 8.20479 2.12949 8.01885C2.12949 7.90792 2.2373 7.75635 2.4248 7.60635C2.69355 7.39073 3.1123 7.18292 3.63262 7.00479C3.86699 6.9251 4.11699 6.85323 4.37949 6.78917C4.53105 7.19073 4.70762 7.60167 4.90449 8.01729C4.62949 8.59854 4.39512 9.1751 4.20762 9.72667C3.87949 10.6985 3.70762 11.5845 3.71074 12.286C3.71387 12.8939 3.85449 13.697 4.50918 14.0751C4.73887 14.2079 4.97949 14.2564 5.21387 14.2564C5.52168 14.2564 5.81387 14.1704 6.04668 14.0751C6.51699 13.8814 7.02949 13.5314 7.57012 13.0376L6.8123 12.2064C5.80293 13.1267 5.23262 13.1939 5.07168 13.1001C4.95918 13.0345 4.83887 12.786 4.83574 12.2798C4.83262 11.7001 4.98418 10.9407 5.27324 10.0845C5.36074 9.8251 5.45918 9.56104 5.57012 9.29229C5.58887 9.32354 5.60605 9.35479 5.6248 9.3876C6.22012 10.4173 6.89824 11.3767 7.5873 12.1595C8.26543 12.9298 8.94668 13.522 9.55605 13.8704C9.90137 14.0689 10.3514 14.2564 10.8029 14.2564C11.0404 14.2564 11.2795 14.2048 11.5061 14.0735C12.1607 13.6954 12.3014 12.8907 12.3045 12.2829C12.3076 11.5814 12.1357 10.6954 11.8076 9.72198C11.6404 9.2251 11.4326 8.70635 11.192 8.18292L10.1686 8.65635C10.3936 9.14385 10.5857 9.62354 10.7404 10.0845C11.0295 10.9407 11.1811 11.7001 11.1779 12.2814C11.1764 12.7876 11.0545 13.0376 10.942 13.1017C10.8295 13.1673 10.5529 13.147 10.1139 12.8954C9.61074 12.6079 9.02793 12.097 8.43105 11.4173C7.79043 10.6892 7.15605 9.79385 6.59824 8.82667C6.44199 8.55635 6.29512 8.2876 6.15762 8.01729C6.29668 7.74385 6.44512 7.47198 6.60137 7.20167C6.75605 6.93292 6.91543 6.67198 7.07949 6.41885C7.38262 6.40323 7.69043 6.39542 8.00293 6.39542C9.6623 6.39542 11.2154 6.6126 12.3732 7.00635C12.8951 7.18292 13.3123 7.39073 13.5811 7.60792C13.7686 7.75948 13.8764 7.90948 13.8764 8.02042C13.8764 8.14073 13.7529 8.30323 13.5373 8.46729C13.2342 8.69698 12.767 8.91417 12.1873 9.09542L12.5232 10.1689C13.2357 9.94698 13.8061 9.6751 14.217 9.36417C14.5748 9.09385 15.0014 8.64073 15.0014 8.02042C15.0029 7.42198 14.6092 6.57667 12.7357 5.93917ZM5.62637 6.6376C5.60605 6.67198 5.5873 6.70635 5.56699 6.74073C5.54355 6.68448 5.52168 6.62823 5.49824 6.57198C5.55762 6.5626 5.61855 6.55479 5.67949 6.54698C5.6623 6.5751 5.64512 6.60635 5.62637 6.6376Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M7.01074 8.22363C7.01074 8.59659 7.1589 8.95428 7.42262 9.218C7.68635 9.48172 8.04403 9.62988 8.41699 9.62988C8.78995 9.62988 9.14764 9.48172 9.41136 9.218C9.67508 8.95428 9.82324 8.59659 9.82324 8.22363C9.82324 7.85067 9.67508 7.49299 9.41136 7.22926C9.14764 6.96554 8.78995 6.81738 8.41699 6.81738C8.04403 6.81738 7.68635 6.96554 7.42262 7.22926C7.1589 7.49299 7.01074 7.85067 7.01074 8.22363Z"
        fill={theme.colors['icon.primary']}
      />
    </svg>
  );
}

export function AlgorithmIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M6.58588 4.41421C6.21081 4.03914 6.0001 3.53043 6.0001 3C6.0001 2.46957 6.21081 1.96086 6.58588 1.58579C6.96096 1.21071 7.46967 1 8.0001 1C8.53053 1 9.03924 1.21071 9.41431 1.58579C9.78938 1.96086 10.0001 2.46957 10.0001 3C10.0001 3.53043 9.78938 4.03914 9.41431 4.41421C9.35982 4.46871 9.30251 4.51973 9.24271 4.56714C8.90198 4.83827 8.4701 5 8.0001 5C7.53003 5 7.09808 4.83822 6.75733 4.56702C6.69759 4.51964 6.64033 4.46866 6.58588 4.41421Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M1.84609 11.3013C1.74558 11.0587 1.69385 10.7986 1.69385 10.5359C1.69385 10.0055 1.90456 9.4968 2.27963 9.12172C2.65471 8.74665 3.16341 8.53594 3.69385 8.53594C4.22428 8.53594 4.73299 8.74665 5.10806 9.12172C5.48313 9.4968 5.69385 10.0055 5.69385 10.5359C5.69385 10.7986 5.64212 11.0587 5.54161 11.3013C5.4411 11.544 5.29378 11.7644 5.10806 11.9502C4.92234 12.1359 4.70187 12.2832 4.45921 12.3837C4.21656 12.4842 3.95649 12.5359 3.69385 12.5359C3.4312 12.5359 3.17113 12.4842 2.92848 12.3837C2.68583 12.2832 2.46535 12.1359 2.27963 11.9502C2.09392 11.7644 1.9466 11.544 1.84609 11.3013Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M12.2985 12.5359C13.4032 12.5359 14.2985 11.6406 14.2985 10.5359C14.2985 9.66406 13.7407 8.92187 12.961 8.64844C12.7548 8.575 12.5313 8.53594 12.2985 8.53594C12.1845 8.53594 12.0735 8.54531 11.9642 8.56406C11.0188 8.72344 10.2985 9.54531 10.2985 10.5359C10.2985 10.7672 10.3376 10.9906 10.411 11.1984C10.5235 11.5219 10.7188 11.8062 10.9688 12.0297C11.322 12.3453 11.7876 12.5359 12.2985 12.5359Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M9.53291 11.7C9.53447 11.7016 9.53447 11.7031 9.53447 11.7031C9.66729 12.0172 9.8501 12.3031 10.0782 12.5531C9.44385 12.8438 8.74072 13.0047 7.99854 13.0047C7.25479 13.0047 6.54854 12.8422 5.91416 12.5516C6.14229 12.3 6.32354 12.0156 6.45635 11.7031C6.45635 11.7023 6.45674 11.7016 6.45713 11.7008C6.45752 11.7 6.45791 11.6992 6.45791 11.6984C6.94697 11.9016 7.46416 12.0047 7.99854 12.0047C8.53135 12.0047 9.04697 11.9016 9.53291 11.7Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M5.1376 3.9C5.16572 3.99062 5.19854 4.07969 5.23604 4.16875C5.33135 4.39375 5.45322 4.60469 5.6001 4.79844C5.44854 4.9125 5.30635 5.0375 5.17041 5.17187C4.80322 5.54062 4.51416 5.96875 4.3126 6.44531C4.1626 6.8 4.06572 7.17031 4.02354 7.55312C3.91416 7.54219 3.80479 7.53594 3.69541 7.53594C3.4626 7.53594 3.23447 7.5625 3.0126 7.61406C3.13135 6.07656 3.94385 4.73437 5.1376 3.9Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M10.7642 4.16875C10.8017 4.07969 10.8345 3.99062 10.8626 3.9C12.0579 4.73437 12.8704 6.07812 12.9876 7.61719C12.7642 7.56406 12.5329 7.5375 12.2985 7.5375C12.1907 7.5375 12.0845 7.54375 11.9782 7.55469C11.9361 7.17335 11.8387 6.80018 11.6892 6.44687C11.4876 5.97031 11.1985 5.54062 10.8313 5.17344C10.6954 5.0375 10.5532 4.91406 10.4017 4.8C10.547 4.60625 10.6688 4.39375 10.7642 4.16875Z"
        fill={theme.colors['icon.primary']}
      />
    </svg>
  );
}

export function GSQLFolderIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="folder_special" clipPath="url(#clip0_2659_24104)">
        <path
          id="Vector"
          d="M16.6666 5.00016H9.99996L8.82496 3.82516C8.50829 3.5085 8.08329 3.3335 7.64163 3.3335H3.33329C2.41663 3.3335 1.66663 4.0835 1.66663 5.00016V15.0002C1.66663 15.9168 2.41663 16.6668 3.33329 16.6668H16.6666C17.5833 16.6668 18.3333 15.9168 18.3333 15.0002V6.66683C18.3333 5.75016 17.5833 5.00016 16.6666 5.00016ZM14.1166 13.6752L12.5 12.7335L10.8833 13.6752C10.5666 13.8585 10.1833 13.5752 10.2666 13.2168L10.6916 11.3835L9.28329 10.1668C9.00829 9.92516 9.14996 9.46683 9.51663 9.4335L11.375 9.27516L12.1083 7.5585C12.25 7.22516 12.7333 7.22516 12.875 7.5585L13.6083 9.27516L15.4666 9.4335C15.8333 9.46683 15.9833 9.92516 15.7 10.1668L14.2916 11.3835L14.7166 13.2168C14.8083 13.5752 14.425 13.8585 14.1166 13.6752Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2659_24104">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function GraphFolderIconLight() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <g clipPath="url(#clip0_2977_74540)">
        <path
          d="M16.6666 5.00001H9.99996L8.33329 3.33334H3.33329C2.40829 3.33334 1.67496 4.07501 1.67496 5.00001L1.66663 15C1.66663 15.925 2.40829 16.6667 3.33329 16.6667H16.6666C17.5916 16.6667 18.3333 15.925 18.3333 15V6.66668C18.3333 5.74168 17.5916 5.00001 16.6666 5.00001ZM16.6666 15H3.33329V5.00001H7.64163L8.81663 6.17501L9.30829 6.66668H16.6666V15Z"
          fill="#546A80"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.30837 6.66667H16.6667V15H3.33337V5H7.64171L9.30837 6.66667ZM8.33337 13.1546C8.33337 12.4715 8.88718 11.9177 9.57033 11.9177C9.92893 11.9177 10.2519 12.0703 10.4778 12.3141L13.1759 11.0847C13.1699 11.0349 13.1667 10.9842 13.1667 10.9328C13.1667 10.854 13.174 10.7769 13.188 10.7022L10.5275 9.52047C10.3007 9.79727 9.95614 9.9739 9.57033 9.9739C8.88718 9.9739 8.33337 9.4201 8.33337 8.73695C8.33337 8.0538 8.88718 7.5 9.57033 7.5C10.2535 7.5 10.8073 8.0538 10.8073 8.73695L10.8073 8.74185L13.6032 9.98372C13.8218 9.79611 14.106 9.68275 14.4167 9.68275C15.1071 9.68275 15.6667 10.2424 15.6667 10.9328C15.6667 11.6231 15.1071 12.1828 14.4167 12.1828C14.0748 12.1828 13.7649 12.0455 13.5393 11.823L10.8044 13.0691C10.8063 13.0974 10.8073 13.1259 10.8073 13.1546C10.8073 13.8378 10.2535 14.3916 9.57033 14.3916C8.88718 14.3916 8.33337 13.8378 8.33337 13.1546Z"
          fill="#546A80"
        />
      </g>
      <defs>
        <clipPath id="clip0_2977_74540">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function GraphFolderIconDark() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <g clipPath="url(#clip0_3004_78307)">
        <path
          d="M16.6666 4.99998H9.99996L8.33329 3.33331H3.33329C2.40829 3.33331 1.67496 4.07498 1.67496 4.99998L1.66663 15C1.66663 15.925 2.40829 16.6666 3.33329 16.6666H16.6666C17.5916 16.6666 18.3333 15.925 18.3333 15V6.66665C18.3333 5.74165 17.5916 4.99998 16.6666 4.99998ZM16.6666 15H3.33329V4.99998H7.64163L8.81663 6.17498L9.30829 6.66665H16.6666V15Z"
          fill="#BFC7CF"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.30837 6.66667H16.6667V15H3.33337V5H7.64171L9.30837 6.66667ZM8.33337 13.1546C8.33337 12.4715 8.88718 11.9177 9.57033 11.9177C9.92893 11.9177 10.2519 12.0703 10.4778 12.3141L13.1759 11.0847C13.1699 11.0349 13.1667 10.9842 13.1667 10.9328C13.1667 10.854 13.174 10.7769 13.188 10.7022L10.5275 9.52047C10.3007 9.79727 9.95614 9.9739 9.57033 9.9739C8.88718 9.9739 8.33337 9.4201 8.33337 8.73695C8.33337 8.0538 8.88718 7.5 9.57033 7.5C10.2535 7.5 10.8073 8.0538 10.8073 8.73695L10.8073 8.74185L13.6032 9.98372C13.8218 9.79611 14.106 9.68275 14.4167 9.68275C15.1071 9.68275 15.6667 10.2424 15.6667 10.9328C15.6667 11.6231 15.1071 12.1828 14.4167 12.1828C14.0748 12.1828 13.7649 12.0455 13.5393 11.823L10.8044 13.0691C10.8063 13.0974 10.8073 13.1259 10.8073 13.1546C10.8073 13.8378 10.2535 14.3916 9.57033 14.3916C8.88718 14.3916 8.33337 13.8378 8.33337 13.1546Z"
          fill="#BFC7CF"
        />
      </g>
      <defs>
        <clipPath id="clip0_3004_78307">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function InstalledIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <g clipPath="url(#clip0_2880_4741)">
        <path
          d="M12.9 6.69366C12.4467 4.39366 10.4267 2.66699 8 2.66699C6.07333 2.66699 4.4 3.76033 3.56667 5.36033C1.56 5.57366 0 7.27366 0 9.33366C0 11.5403 1.79333 13.3337 4 13.3337H12.6667C14.5067 13.3337 16 11.8403 16 10.0003C16 8.24032 14.6333 6.81366 12.9 6.69366ZM12.6667 12.0003H4C2.52667 12.0003 1.33333 10.807 1.33333 9.33366C1.33333 7.96699 2.35333 6.82699 3.70667 6.68699L4.42 6.61366L4.75333 5.98033C5.38667 4.76033 6.62667 4.00033 8 4.00033C9.74667 4.00033 11.2533 5.24033 11.5933 6.95366L11.7933 7.95366L12.8133 8.02699C13.8533 8.09366 14.6667 8.96699 14.6667 10.0003C14.6667 11.1003 13.7667 12.0003 12.6667 12.0003ZM6.66667 9.45366L5.27333 8.06033L4.33333 9.00033L6.66667 11.3337L10.6733 7.32699L9.73333 6.38699L6.66667 9.45366Z"
          fill={theme.colors['icon.primary']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2880_4741">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function InstallingIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <g clipPath="url(#clip0_2858_29656)">
        <path
          d="M3.5 0.5V5H3.5075L3.5 5.0075L6.5 8L3.5 11L3.5075 11.0075H3.5V15.5H12.5V11.0075H12.4925L12.5 11L9.5 8L12.5 5.0075L12.4925 5H12.5V0.5H3.5ZM11 11.375V14H5V11.375L8 8.375L11 11.375ZM8 7.625L5 4.625V2H11V4.625L8 7.625Z"
          fill={theme.colors['icon.primary']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2858_29656">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function DraftIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="16" viewBox="0 0 18 16" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.52682 1.19418C2.91752 0.803478 3.44743 0.583984 3.99996 0.583984H8.66663C8.86554 0.583984 9.0563 0.663002 9.19696 0.803654L9.19745 0.804152L13.863 5.46972C13.9651 5.57158 14.038 5.70269 14.068 5.84941L12.5833 7.33415V6.75065H8.66663C8.25241 6.75065 7.91663 6.41486 7.91663 6.00065V2.08398H3.99996C3.84525 2.08398 3.69688 2.14544 3.58748 2.25484C3.47808 2.36423 3.41663 2.51261 3.41663 2.66732V13.334C3.41663 13.4887 3.47808 13.6371 3.58748 13.7465C3.69688 13.8559 3.84525 13.9173 3.99996 13.9173H12C12.1547 13.9173 12.303 13.8559 12.4124 13.7465C12.5218 13.6371 12.5833 13.4887 12.5833 13.334V12.9172L14.0833 11.4172V13.334C14.0833 13.8865 13.8638 14.4164 13.4731 14.8071C13.0824 15.1978 12.5525 15.4173 12 15.4173H3.99996C3.44742 15.4173 2.91752 15.1978 2.52682 14.8071C2.13612 14.4164 1.91663 13.8865 1.91663 13.334V2.66732C1.91663 2.11478 2.13612 1.58488 2.52682 1.19418ZM9.41663 3.14464L11.5226 5.25065H9.41663V3.14464Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M9.12998 11.7969V13.1478C9.12998 13.2723 9.22775 13.37 9.35217 13.37H10.7031C10.7609 13.37 10.8186 13.3478 10.8586 13.3034L15.7113 8.45514L14.0449 6.78871L9.19664 11.6369C9.1522 11.6814 9.12998 11.7347 9.12998 11.7969ZM17 7.16644C17.1733 6.99313 17.1733 6.71317 17 6.53986L15.9601 5.5C15.7868 5.32669 15.5069 5.32669 15.3336 5.5L14.5203 6.31322L16.1868 7.97966L17 7.16644Z"
        fill={theme.colors['icon.primary']}
      />
    </svg>
  );
}

export function FileIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8.66669 1.33337H4.00002C3.6464 1.33337 3.30726 1.47385 3.05721 1.7239C2.80716 1.97395 2.66669 2.31309 2.66669 2.66671V13.3334C2.66669 13.687 2.80716 14.0261 3.05721 14.2762C3.30726 14.5262 3.6464 14.6667 4.00002 14.6667H12C12.3536 14.6667 12.6928 14.5262 12.9428 14.2762C13.1929 14.0261 13.3334 13.687 13.3334 13.3334V6.00004L8.66669 1.33337Z"
        stroke={theme.colors['icon.primary']}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.66669 1.33337V6.00004H13.3334"
        stroke={theme.colors['icon.primary']}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function ReadOnlyFileIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="file">
        <path
          id="Vector"
          d="M8.66663 1.3335V6.00016H13.3333"
          stroke={theme.colors['icon.primary']}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          id="Subtract"
          d="M4.00008 0.583496C3.44755 0.583496 2.91764 0.802989 2.52694 1.19369C2.13624 1.58439 1.91675 2.1143 1.91675 2.66683V13.3335C1.91675 13.886 2.13624 14.4159 2.52694 14.8066C2.91764 15.1973 3.44755 15.4168 4.00008 15.4168H9.00006V13.9168H4.00008C3.84537 13.9168 3.697 13.8554 3.5876 13.746C3.47821 13.6366 3.41675 13.4882 3.41675 13.3335V2.66683C3.41675 2.51212 3.47821 2.36375 3.5876 2.25435C3.697 2.14495 3.84537 2.0835 4.00008 2.0835H8.35609L12.5834 6.31082V8.03473C12.7189 8.01197 12.8581 8.00012 13.0001 8.00012C13.3881 8.00012 13.7556 8.08868 14.0834 8.24668V6.00016C14.0834 5.80125 14.0044 5.61049 13.8637 5.46983L9.19708 0.803166C9.05643 0.662514 8.86566 0.583496 8.66675 0.583496H4.00008Z"
          fill={theme.colors['icon.primary']}
        />
        <path
          id="Vector_2"
          d="M15.25 11.3333H14.875V10.6667C14.875 9.74667 14.035 9 13 9C11.965 9 11.125 9.74667 11.125 10.6667V11.3333H10.75C10.3375 11.3333 10 11.6333 10 12V15.3333C10 15.7 10.3375 16 10.75 16H15.25C15.6625 16 16 15.7 16 15.3333V12C16 11.6333 15.6625 11.3333 15.25 11.3333ZM13 14.3333C12.5875 14.3333 12.25 14.0333 12.25 13.6667C12.25 13.3 12.5875 13 13 13C13.4125 13 13.75 13.3 13.75 13.6667C13.75 14.0333 13.4125 14.3333 13 14.3333ZM14.1625 11.3333H11.8375V10.6667C11.8375 10.0967 12.3587 9.63333 13 9.63333C13.6413 9.63333 14.1625 10.0967 14.1625 10.6667V11.3333Z"
          fill={theme.colors['icon.primary']}
        />
      </g>
    </svg>
  );
}

export function ShareIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <g clipPath="url(#clip0_2659_15446)">
        <path
          d="M13.5 12.06C12.93 12.06 12.42 12.285 12.03 12.6375L6.6825 9.525C6.72 9.3525 6.75 9.18 6.75 9C6.75 8.82 6.72 8.6475 6.6825 8.475L11.97 5.3925C12.375 5.7675 12.9075 6 13.5 6C14.745 6 15.75 4.995 15.75 3.75C15.75 2.505 14.745 1.5 13.5 1.5C12.255 1.5 11.25 2.505 11.25 3.75C11.25 3.93 11.28 4.1025 11.3175 4.275L6.03 7.3575C5.625 6.9825 5.0925 6.75 4.5 6.75C3.255 6.75 2.25 7.755 2.25 9C2.25 10.245 3.255 11.25 4.5 11.25C5.0925 11.25 5.625 11.0175 6.03 10.6425L11.37 13.7625C11.3325 13.92 11.31 14.085 11.31 14.25C11.31 15.4575 12.2925 16.44 13.5 16.44C14.7075 16.44 15.69 15.4575 15.69 14.25C15.69 13.0425 14.7075 12.06 13.5 12.06Z"
          fill={theme.colors['dropdown.icon']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2659_15446">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function RenameIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <g clipPath="url(#clip0_2659_15543)">
        <path
          d="M13.8075 4.35016L12.9 3.44266C12.315 2.85766 11.3625 2.85766 10.7775 3.44266L8.7675 5.45266L2.25 11.9702V15.0002H5.28L11.835 8.44516L13.8075 6.47266C14.4 5.88766 14.4 4.93516 13.8075 4.35016ZM4.6575 13.5002H3.75V12.5927L10.245 6.09766L11.1525 7.00516L4.6575 13.5002ZM8.25 15.0002L11.25 12.0002H15.75V15.0002H8.25Z"
          fill={theme.colors['dropdown.icon']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2659_15543">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function DeleteIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <g clipPath="url(#clip0_2659_17881)">
        <path
          d="M4.5 14.25C4.5 15.075 5.175 15.75 6 15.75H12C12.825 15.75 13.5 15.075 13.5 14.25V5.25H4.5V14.25ZM6 6.75H12V14.25H6V6.75ZM11.625 3L10.875 2.25H7.125L6.375 3H3.75V4.5H14.25V3H11.625Z"
          fill={theme.colors['dropdown.text.error']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2659_17881">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function MoveIcon() {
  const [_, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <g clipPath="url(#clip0_3371_78426)">
        <path
          d="M13.334 3.99996H8.00065L6.66732 2.66663H2.66732C1.93398 2.66663 1.33398 3.26663 1.33398 3.99996V12C1.33398 12.7333 1.93398 13.3333 2.66732 13.3333H13.334C14.0673 13.3333 14.6673 12.7333 14.6673 12V5.33329C14.6673 4.59996 14.0673 3.99996 13.334 3.99996ZM9.33398 12V9.99996H6.66732V7.33329H9.33398V5.33329L12.6673 8.66663L9.33398 12Z"
          fill={theme.colors['dropdown.icon']}
        />
      </g>
      <defs>
        <clipPath id="clip0_3371_78426">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function InstallIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <g clipPath="url(#clip0_2659_17517)">
        <path
          d="M12.4425 6.75H11.25V3C11.25 2.5875 10.9125 2.25 10.5 2.25H7.5C7.0875 2.25 6.75 2.5875 6.75 3V6.75H5.5575C4.89 6.75 4.5525 7.56 5.025 8.0325L8.4675 11.475C8.76 11.7675 9.2325 11.7675 9.525 11.475L12.9675 8.0325C13.44 7.56 13.11 6.75 12.4425 6.75ZM3.75 14.25C3.75 14.6625 4.0875 15 4.5 15H13.5C13.9125 15 14.25 14.6625 14.25 14.25C14.25 13.8375 13.9125 13.5 13.5 13.5H4.5C4.0875 13.5 3.75 13.8375 3.75 14.25Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2659_17517">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function CheckIcon() {
  const [_, theme] = useStyletron();

  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2293_16434)">
        <path
          d="M6 10.8L3.66666 8.46667C3.40666 8.20667 2.99333 8.20667 2.73333 8.46667C2.47333 8.72667 2.47333 9.14 2.73333 9.4L5.52666 12.1933C5.78666 12.4533 6.20666 12.4533 6.46666 12.1933L13.5333 5.13334C13.7933 4.87334 13.7933 4.46 13.5333 4.2C13.2733 3.94 12.86 3.94 12.6 4.2L6 10.8Z"
          fill={theme.colors['dropdown.icon']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2293_16434">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function HideSchemaIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.5 14.8333H4.5C3.94771 14.8333 3.5 14.3855 3.5 13.8333V5.33325C3.5 4.78097 3.94772 4.33325 4.5 4.33325H12.5V14.8333ZM2.5 5.33325C2.5 4.22868 3.39543 3.33325 4.5 3.33325H16.3333C17.4379 3.33325 18.3333 4.22868 18.3333 5.33325V13.8333C18.3333 14.9378 17.4379 15.8333 16.3333 15.8333H4.5C3.39543 15.8333 2.5 14.9378 2.5 13.8333V5.33325Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function HideEditorIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.3333 14.8333H4.5C3.94771 14.8333 3.5 14.3855 3.5 13.8333V10.8333H17.3333V13.8333C17.3333 14.3855 16.8856 14.8333 16.3333 14.8333ZM2.5 5.33325C2.5 4.22868 3.39543 3.33325 4.5 3.33325H8.33333H12.2917H16.3333C17.4379 3.33325 18.3333 4.22868 18.3333 5.33325V13.8333C18.3333 14.9378 17.4379 15.8333 16.3333 15.8333H4.5C3.39543 15.8333 2.5 14.9378 2.5 13.8333V5.33325Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function HideFileIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.33333 14.8333H16.3333C16.8856 14.8333 17.3333 14.3855 17.3333 13.8333V5.33325C17.3333 4.78097 16.8856 4.33325 16.3333 4.33325H8.33333V14.8333ZM2.5 5.33325C2.5 4.22868 3.39543 3.33325 4.5 3.33325H16.3333C17.4379 3.33325 18.3333 4.22868 18.3333 5.33325V13.8333C18.3333 14.9378 17.4379 15.8333 16.3333 15.8333H4.5C3.39543 15.8333 2.5 14.9378 2.5 13.8333V5.33325Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function HideResultIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.5 11.1666H17.3333V5.33325C17.3333 4.78097 16.8856 4.33325 16.3333 4.33325H4.5C3.94772 4.33325 3.5 4.78097 3.5 5.33325V11.1666ZM18.3333 5.33325V11.1666V11.6666V12.1666V13.8333C18.3333 14.9378 17.4379 15.8333 16.3333 15.8333H4.5C3.39543 15.8333 2.5 14.9378 2.5 13.8333V12.1666V11.6666V11.1666V5.33325C2.5 4.22868 3.39543 3.33325 4.5 3.33325H16.3333C17.4379 3.33325 18.3333 4.22868 18.3333 5.33325Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function People() {
  return (
    <svg width="22" height="14" viewBox="0 0 22 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M15 6C16.66 6 17.99 4.66 17.99 3C17.99 1.34 16.66 0 15 0C13.34 0 12 1.34 12 3C12 4.66 13.34 6 15 6ZM7 6C8.66 6 9.99 4.66 9.99 3C9.99 1.34 8.66 0 7 0C5.34 0 4 1.34 4 3C4 4.66 5.34 6 7 6ZM7 8C4.67 8 0 9.17 0 11.5V14H14V11.5C14 9.17 9.33 8 7 8ZM15 8C14.71 8 14.38 8.02 14.03 8.05C15.19 8.89 16 10.02 16 11.5V14H22V11.5C22 9.17 17.33 8 15 8Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function Public() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM9 17.93C5.05 17.44 2 14.08 2 10C2 9.38 2.08 8.79 2.21 8.21L7 13V14C7 15.1 7.9 16 9 16V17.93ZM15.9 15.39C15.64 14.58 14.9 14 14 14H13V11C13 10.45 12.55 10 12 10H6V8H8C8.55 8 9 7.55 9 7V5H11C12.1 5 13 4.1 13 3V2.59C15.93 3.78 18 6.65 18 10C18 12.08 17.2 13.97 15.9 15.39Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function Remove() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM14 11H6C5.45 11 5 10.55 5 10C5 9.45 5.45 9 6 9H14C14.55 9 15 9.45 15 10C15 10.55 14.55 11 14 11Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function AddIconNoCircle() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2253_25257)">
        <path
          d="M15 10.8333H10.8333V15C10.8333 15.4583 10.4583 15.8333 9.99996 15.8333C9.54163 15.8333 9.16663 15.4583 9.16663 15V10.8333H4.99996C4.54163 10.8333 4.16663 10.4583 4.16663 9.99996C4.16663 9.54163 4.54163 9.16663 4.99996 9.16663H9.16663V4.99996C9.16663 4.54163 9.54163 4.16663 9.99996 4.16663C10.4583 4.16663 10.8333 4.54163 10.8333 4.99996V9.16663H15C15.4583 9.16663 15.8333 9.54163 15.8333 9.99996C15.8333 10.4583 15.4583 10.8333 15 10.8333Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2253_25257">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function PhotoLibrary() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1090_6434)">
        <path
          d="M18.3337 13.3337V3.33366C18.3337 2.41699 17.5837 1.66699 16.667 1.66699H6.66699C5.75033 1.66699 5.00033 2.41699 5.00033 3.33366V13.3337C5.00033 14.2503 5.75033 15.0003 6.66699 15.0003H16.667C17.5837 15.0003 18.3337 14.2503 18.3337 13.3337ZM9.50033 10.442L10.8587 12.2587L13.0087 9.57533C13.1753 9.36699 13.492 9.36699 13.6587 9.57533L16.1253 12.6587C16.342 12.9337 16.1503 13.3337 15.8003 13.3337H7.50033C7.15866 13.3337 6.95866 12.942 7.16699 12.667L8.83366 10.442C9.00033 10.2253 9.33366 10.2253 9.50033 10.442ZM1.66699 5.83366V16.667C1.66699 17.5837 2.41699 18.3337 3.33366 18.3337H14.167C14.6253 18.3337 15.0003 17.9587 15.0003 17.5003C15.0003 17.042 14.6253 16.667 14.167 16.667H4.16699C3.70866 16.667 3.33366 16.292 3.33366 15.8337V5.83366C3.33366 5.37533 2.95866 5.00033 2.50033 5.00033C2.04199 5.00033 1.66699 5.37533 1.66699 5.83366Z"
          fill="#AAB5BF"
        />
      </g>
      <defs>
        <clipPath id="clip0_1090_6434">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function DeleteButton() {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1583_10036)">
        <path
          d="M4.5 14.25C4.5 15.075 5.175 15.75 6 15.75H12C12.825 15.75 13.5 15.075 13.5 14.25V5.25H4.5V14.25ZM6 6.75H12V14.25H6V6.75ZM11.625 3L10.875 2.25H7.125L6.375 3H3.75V4.5H14.25V3H11.625Z"
          fill="#B93535"
        />
      </g>
      <defs>
        <clipPath id="clip0_1583_10036">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function MenuHide() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M20 6L9 6C8.45 6 8 6.45 8 7C8 7.55 8.45 8 9 8L20 8C20.55 8 21 7.55 21 7C21 6.45 20.55 6 20 6ZM20 11L12 11C11.45 11 11 11.45 11 12C11 12.55 11.45 13 12 13L20 13C20.55 13 21 12.55 21 12C21 11.45 20.55 11 20 11ZM21 17C21 16.45 20.55 16 20 16L9 16C8.45 16 8 16.45 8 17C8 17.55 8.45 18 9 18L20 18C20.55 18 21 17.55 21 17ZM3.7 9.12L6.58 12L3.7 14.88C3.31 15.27 3.31 15.9 3.7 16.29C4.09 16.68 4.72 16.68 5.11 16.29L8.7 12.7C9.09 12.31 9.09 11.68 8.7 11.29L5.11 7.7C4.72 7.31 4.09 7.31 3.7 7.7C3.32 8.09 3.31 8.73 3.7 9.12Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function Feedback() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1937_8094)">
        <path
          d="M13.3333 1.3335H2.66665C1.93331 1.3335 1.33331 1.9335 1.33331 2.66683V14.6668L3.99998 12.0002H13.3333C14.0666 12.0002 14.6666 11.4002 14.6666 10.6668V2.66683C14.6666 1.9335 14.0666 1.3335 13.3333 1.3335ZM12.6666 10.6668H3.99998L2.66665 12.0002V3.3335C2.66665 2.96683 2.96665 2.66683 3.33331 2.66683H12.6666C13.0333 2.66683 13.3333 2.96683 13.3333 3.3335V10.0002C13.3333 10.3668 13.0333 10.6668 12.6666 10.6668Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1937_8094">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function Community() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.66665 2H14C14.4 2 14.6666 2.26667 14.6666 2.66667V13.3333C14.6666 13.7333 14.4 14 14 14H1.99998C1.59998 14 1.33331 13.7333 1.33331 13.3333V8.33333C1.33331 8.13333 1.39998 7.93333 1.59998 7.8L3.99998 5.8V2.66667C3.99998 2.26667 4.26665 2 4.66665 2ZM5.99998 12.6667H7.99998V8.6L5.33331 6.4L2.66665 8.6V12.6667H4.66665V10H5.99998V12.6667ZM9.33331 12.6667H13.3333V3.33333H5.33331V4.86667C5.46665 4.86667 5.66665 4.93333 5.73331 5L9.06665 7.8C9.26665 7.93333 9.33331 8.13333 9.33331 8.33333V12.6667ZM9.33331 4.66667H7.99998V6H9.33331V4.66667ZM12 4.66667H10.6666V6H12V4.66667ZM12 7.33333H10.6666V8.66667H12V7.33333ZM12 10H10.6666V11.3333H12V10Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function Docs() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.9258 2.13909L13.1885 4.36439C13.2811 4.45561 13.3334 4.58015 13.3334 4.71015V14.1212C13.3334 14.3889 13.1162 14.6061 12.8485 14.6061H3.15154C2.88381 14.6061 2.66669 14.3889 2.66669 14.1212V2.48485C2.66669 2.21712 2.88381 2 3.15154 2H10.5858C10.713 2.00004 10.835 2.04999 10.9258 2.13909ZM3.63638 13.6364H12.3637V5.87879H10.4238C9.88941 5.87879 9.45457 5.44409 9.45411 4.91L9.45245 2.9697H3.63638V13.6364ZM10.4223 3.00409L10.4238 4.90909H12.3594L10.4223 3.00409ZM7.99998 6.84849H5.09089C4.82316 6.84849 4.60604 6.63137 4.60604 6.36364C4.60604 6.09592 4.82316 5.87879 5.09089 5.87879H7.99998C8.26771 5.87879 8.48483 6.09592 8.48483 6.36364C8.48483 6.63137 8.26771 6.84849 7.99998 6.84849ZM5.09089 9.27273C4.82316 9.27273 4.60604 9.05561 4.60604 8.78789C4.60604 8.52016 4.82316 8.30304 5.09089 8.30304H10.9091C11.1768 8.30304 11.3939 8.52016 11.3939 8.78789C11.3939 9.05561 11.1768 9.27273 10.9091 9.27273H5.09089ZM5.09089 10.7273H10.9091C11.1768 10.7273 11.3939 10.9444 11.3939 11.2121C11.3939 11.4799 11.1768 11.697 10.9091 11.697H5.09089C4.82316 11.697 4.60604 11.4799 4.60604 11.2121C4.60604 10.9444 4.82316 10.7273 5.09089 10.7273Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function ReleaseNotes() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1937_8113)">
        <path
          d="M5.99998 14.0002C5.99998 14.3668 6.29998 14.6668 6.66665 14.6668H9.33331C9.69998 14.6668 9.99998 14.3668 9.99998 14.0002V13.3335H5.99998V14.0002ZM7.99998 1.3335C5.42665 1.3335 3.33331 3.42683 3.33331 6.00016C3.33331 7.58683 4.12665 8.98016 5.33331 9.82683V11.3335C5.33331 11.7002 5.63331 12.0002 5.99998 12.0002H9.99998C10.3666 12.0002 10.6666 11.7002 10.6666 11.3335V9.82683C11.8733 8.98016 12.6666 7.58683 12.6666 6.00016C12.6666 3.42683 10.5733 1.3335 7.99998 1.3335ZM9.89998 8.7335L9.33331 9.1335V10.6668H6.66665V9.1335L6.09998 8.7335C5.19998 8.10683 4.66665 7.08683 4.66665 6.00016C4.66665 4.16016 6.15998 2.66683 7.99998 2.66683C9.83998 2.66683 11.3333 4.16016 11.3333 6.00016C11.3333 7.08683 10.8 8.10683 9.89998 8.7335Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1937_8113">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function Support() {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1943_7362)">
        <path
          d="M7.99998 1.3335C4.31998 1.3335 1.33331 4.32016 1.33331 8.00016C1.33331 11.6802 4.31998 14.6668 7.99998 14.6668C11.68 14.6668 14.6666 11.6802 14.6666 8.00016C14.6666 4.32016 11.68 1.3335 7.99998 1.3335ZM12.9733 6.08016L11.12 6.84683C10.78 5.94016 10.0666 5.22016 9.15331 4.88683L9.91998 3.0335C11.32 3.56683 12.4333 4.68016 12.9733 6.08016ZM7.99998 10.0002C6.89331 10.0002 5.99998 9.10683 5.99998 8.00016C5.99998 6.8935 6.89331 6.00016 7.99998 6.00016C9.10665 6.00016 9.99998 6.8935 9.99998 8.00016C9.99998 9.10683 9.10665 10.0002 7.99998 10.0002ZM6.08665 3.02683L6.86665 4.88016C5.94665 5.2135 5.21998 5.94016 4.87998 6.86016L3.02665 6.08683C3.56665 4.68016 4.67998 3.56683 6.08665 3.02683ZM3.02665 9.9135L4.87998 9.14683C5.21998 10.0668 5.93998 10.7868 6.85998 11.1202L6.07998 12.9735C4.67998 12.4335 3.56665 11.3202 3.02665 9.9135ZM9.91998 12.9735L9.15331 11.1202C10.0666 10.7802 10.7866 10.0602 11.12 9.14016L12.9733 9.92016C12.4333 11.3202 11.32 12.4335 9.91998 12.9735Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1943_7362">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function FullScreenButton() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1695_15565)">
        <path
          d="M7 14H5V19H10V17H7V14ZM5 10H7V7H10V5H5V10ZM17 17H14V19H19V14H17V17ZM14 5V7H17V10H19V5H14Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1695_15565">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function Backup() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2072_2206)">
        <path
          d="M11.0416 2.49999C6.7999 2.38332 3.3249 5.78332 3.3249 9.99999H1.83323C1.45823 9.99999 1.2749 10.45 1.54156 10.7083L3.86656 13.0417C4.03323 13.2083 4.29156 13.2083 4.45823 13.0417L6.78323 10.7083C7.0499 10.45 6.85823 9.99999 6.49156 9.99999H4.99156C4.99156 6.74999 7.64156 4.12499 10.9082 4.16665C14.0082 4.20832 16.6166 6.81665 16.6582 9.91665C16.6999 13.175 14.0749 15.8333 10.8249 15.8333C9.48323 15.8333 8.24156 15.375 7.25823 14.6C6.9249 14.3417 6.45823 14.3667 6.15823 14.6667C5.80823 15.025 5.83323 15.6083 6.2249 15.9167C7.49156 16.9083 9.09156 17.5 10.8249 17.5C15.0332 17.5 18.4416 14.025 18.3249 9.78332C18.2166 5.87499 14.9499 2.60832 11.0416 2.49999ZM10.6166 6.66665C10.2749 6.66665 9.99156 6.94999 9.99156 7.29165V10.3583C9.99156 10.65 10.1499 10.925 10.3999 11.075L12.9999 12.6167C13.2999 12.7917 13.6832 12.6917 13.8582 12.4C14.0332 12.1 13.9332 11.7167 13.6416 11.5417L11.2416 10.1167V7.28332C11.2416 6.94999 10.9666 6.66665 10.6166 6.66665Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2072_2206">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function Caution() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 4C7.584 4 4 7.584 4 12C4 16.416 7.584 20 12 20C16.416 20 20 16.416 20 12C20 7.584 16.416 4 12 4ZM12 12.8C11.56 12.8 11.2 12.44 11.2 12V8.8C11.2 8.36 11.56 8 12 8C12.44 8 12.8 8.36 12.8 8.8V12C12.8 12.44 12.44 12.8 12 12.8ZM12.8 16H11.2V14.4H12.8V16Z"
        fill="#B93535"
      />
    </svg>
  );
}

export function DeleteBackup() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2228_1082)">
        <path
          d="M13.3334 7.5V15.8333H6.66675V7.5H13.3334ZM12.0834 2.5H7.91675L7.08341 3.33333H4.16675V5H15.8334V3.33333H12.9167L12.0834 2.5ZM15.0001 5.83333H5.00008V15.8333C5.00008 16.75 5.75008 17.5 6.66675 17.5H13.3334C14.2501 17.5 15.0001 16.75 15.0001 15.8333V5.83333Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2228_1082">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ArrowDown() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.76644 7.7418L9.99977 10.9751L13.2331 7.7418C13.5581 7.4168 14.0831 7.4168 14.4081 7.7418C14.7331 8.0668 14.7331 8.5918 14.4081 8.9168L10.5831 12.7418C10.2581 13.0668 9.73311 13.0668 9.40811 12.7418L5.58311 8.9168C5.25811 8.5918 5.25811 8.0668 5.58311 7.7418C5.90811 7.42513 6.44144 7.4168 6.76644 7.7418Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function ArrowUp() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.2336 12.2582L10.0002 9.02487L6.76689 12.2582C6.44189 12.5832 5.91689 12.5832 5.59189 12.2582C5.26689 11.9332 5.26689 11.4082 5.59189 11.0832L9.41689 7.2582C9.74189 6.9332 10.2669 6.9332 10.5919 7.2582L14.4169 11.0832C14.7419 11.4082 14.7419 11.9332 14.4169 12.2582C14.0919 12.5749 13.5586 12.5832 13.2336 12.2582Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function CopyIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2454_21769)">
        <path
          d="M13.3332 0.833008H3.33317C2.4165 0.833008 1.6665 1.58301 1.6665 2.49967V14.1663H3.33317V2.49967H13.3332V0.833008ZM15.8332 4.16634H6.6665C5.74984 4.16634 4.99984 4.91634 4.99984 5.83301V17.4997C4.99984 18.4163 5.74984 19.1663 6.6665 19.1663H15.8332C16.7498 19.1663 17.4998 18.4163 17.4998 17.4997V5.83301C17.4998 4.91634 16.7498 4.16634 15.8332 4.16634ZM15.8332 17.4997H6.6665V5.83301H15.8332V17.4997Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2454_21769">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function CopyLinkIcon() {
  const [, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
      <g clipPath="url(#clip0_3371_91667)">
        <path
          d="M13.25 5.25H11C10.5875 5.25 10.25 5.5875 10.25 6C10.25 6.4125 10.5875 6.75 11 6.75H13.25C14.4875 6.75 15.5 7.7625 15.5 9C15.5 10.2375 14.4875 11.25 13.25 11.25H11C10.5875 11.25 10.25 11.5875 10.25 12C10.25 12.4125 10.5875 12.75 11 12.75H13.25C15.32 12.75 17 11.07 17 9C17 6.93 15.32 5.25 13.25 5.25ZM6.5 9C6.5 9.4125 6.8375 9.75 7.25 9.75H11.75C12.1625 9.75 12.5 9.4125 12.5 9C12.5 8.5875 12.1625 8.25 11.75 8.25H7.25C6.8375 8.25 6.5 8.5875 6.5 9ZM8 11.25H5.75C4.5125 11.25 3.5 10.2375 3.5 9C3.5 7.7625 4.5125 6.75 5.75 6.75H8C8.4125 6.75 8.75 6.4125 8.75 6C8.75 5.5875 8.4125 5.25 8 5.25H5.75C3.68 5.25 2 6.93 2 9C2 11.07 3.68 12.75 5.75 12.75H8C8.4125 12.75 8.75 12.4125 8.75 12C8.75 11.5875 8.4125 11.25 8 11.25Z"
          fill={theme.colors['text.link']}
        />
      </g>
      <defs>
        <clipPath id="clip0_3371_91667">
          <rect width="18" height="18" fill="white" transform="translate(0.5)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function FullScreenExit() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2087_22685)">
        <path
          d="M5 16H8V19H10V14H5V16ZM8 8H5V10H10V5H8V8ZM14 19H16V16H19V14H14V19ZM16 8V5H14V10H19V8H16Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2087_22685">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function CustomizeWorkspace() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2562_21477)">
        <path
          d="M2 20H22V16H2V20ZM4 17H6V19H4V17ZM2 4V8H22V4H2ZM6 7H4V5H6V7ZM2 14H22V10H2V14ZM4 11H6V13H4V11Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2562_21477">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function GenerateInsights() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2377_21743)">
        <path
          d="M21 8C19.55 8 18.74 9.44 19.07 10.51L15.52 14.07C15.22 13.98 14.78 13.98 14.48 14.07L11.93 11.52C12.27 10.45 11.46 9 10 9C8.55 9 7.73 10.44 8.07 11.52L3.51 16.07C2.44 15.74 1 16.55 1 18C1 19.1 1.9 20 3 20C4.45 20 5.26 18.56 4.93 17.49L9.48 12.93C9.78 13.02 10.22 13.02 10.52 12.93L13.07 15.48C12.73 16.55 13.54 18 15 18C16.45 18 17.27 16.56 16.93 15.48L20.49 11.93C21.56 12.26 23 11.45 23 10C23 8.9 22.1 8 21 8Z"
          fill="currentColor"
        />
        <path d="M15 9L15.94 6.93L18 6L15.94 5.07L15 3L14.08 5.07L12 6L14.08 6.93L15 9Z" fill="currentColor" />
        <path d="M3.5 11L4 9L6 8.5L4 8L3.5 6L3 8L1 8.5L3 9L3.5 11Z" fill="currentColor" />
      </g>
      <defs>
        <clipPath id="clip0_2377_21743">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function SwitchOrg() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_866_1844)">
        <path
          d="M12 7V5C12 3.9 11.1 3 10 3H4C2.9 3 2 3.9 2 5V19C2 20.1 2.9 21 4 21H20C21.1 21 22 20.1 22 19V9C22 7.9 21.1 7 20 7H12ZM10 19H4V17H10V19ZM10 15H4V13H10V15ZM10 11H4V9H10V11ZM10 7H4V5H10V7ZM20 19H12V9H20V19ZM18 11H14V13H18V11ZM18 15H14V17H18V15Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_866_1844">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function UserXL() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_934_4333)">
        <path
          d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V19C4 19.55 4.45 20 5 20H19C19.55 20 20 19.55 20 19V18C20 15.34 14.67 14 12 14Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_934_4333">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function UserIconXL() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_868_15914)">
        <path
          d="M16 11C17.66 11 18.99 9.66 18.99 8C18.99 6.34 17.66 5 16 5C14.34 5 13 6.34 13 8C13 9.66 14.34 11 16 11ZM8 11C9.66 11 10.99 9.66 10.99 8C10.99 6.34 9.66 5 8 5C6.34 5 5 6.34 5 8C5 9.66 6.34 11 8 11ZM8 13C5.67 13 1 14.17 1 16.5V19H15V16.5C15 14.17 10.33 13 8 13ZM16 13C15.71 13 15.38 13.02 15.03 13.05C16.19 13.89 17 15.02 17 16.5V19H23V16.5C23 14.17 18.33 13 16 13Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_868_15914">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function LinkIcon() {
  const [_, theme] = useStyletron();

  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2556_19127)">
        <path
          d="M17 7H13V9H17C18.65 9 20 10.35 20 12C20 13.65 18.65 15 17 15H13V17H17C19.76 17 22 14.76 22 12C22 9.24 19.76 7 17 7ZM11 15H7C5.35 15 4 13.65 4 12C4 10.35 5.35 9 7 9H11V7H7C4.24 7 2 9.24 2 12C2 14.76 4.24 17 7 17H11V15ZM8 11H16V13H8V11Z"
          fill={theme.colors['icon.primary']}
        />
      </g>
      <defs>
        <clipPath id="clip0_2556_19127">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function AuditLogIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={16} height={16} fill="none">
      <g clipPath="url(#a)">
        <path
          fill="currentColor"
          d="M7.993 1.333A6.663 6.663 0 0 0 1.333 8c0 3.68 2.98 6.667 6.66 6.667A6.67 6.67 0 0 0 14.667 8a6.67 6.67 0 0 0-6.674-6.667Zm.007 12A5.332 5.332 0 0 1 2.667 8 5.332 5.332 0 0 1 8 2.667 5.332 5.332 0 0 1 13.333 8 5.332 5.332 0 0 1 8 13.333Zm-.147-8.666h-.04a.478.478 0 0 0-.48.48v3.146c0 .234.12.454.327.574l2.767 1.66c.**************.653-.16a.474.474 0 0 0-.167-.66l-2.58-1.534V5.147a.478.478 0 0 0-.48-.48Z"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M0 0h16v16H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function TextResult() {
  const [, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <g clipPath="url(#clip0_3224_64419)">
        <path
          d="M2.08398 4.58301C2.08398 5.27467 2.64232 5.83301 3.33398 5.83301H6.25065V14.583C6.25065 15.2747 6.80898 15.833 7.50065 15.833C8.19232 15.833 8.75065 15.2747 8.75065 14.583V5.83301H11.6673C12.359 5.83301 12.9173 5.27467 12.9173 4.58301C12.9173 3.89134 12.359 3.33301 11.6673 3.33301H3.33398C2.64232 3.33301 2.08398 3.89134 2.08398 4.58301ZM16.6673 7.49967H11.6673C10.9757 7.49967 10.4173 8.05801 10.4173 8.74967C10.4173 9.44134 10.9757 9.99967 11.6673 9.99967H12.9173V14.583C12.9173 15.2747 13.4757 15.833 14.1673 15.833C14.859 15.833 15.4173 15.2747 15.4173 14.583V9.99967H16.6673C17.359 9.99967 17.9173 9.44134 17.9173 8.74967C17.9173 8.05801 17.359 7.49967 16.6673 7.49967Z"
          fill={`${theme.colors['icon.primary']}`}
        />
      </g>
      <defs>
        <clipPath id="clip0_3224_64419">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function JSONResult() {
  const [, theme] = useStyletron();

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
      <g clipPath="url(#clip0_3429_51491)">
        <path
          d="M7.24909 13.2503L3.99909 10.0003L7.24909 6.75026C7.57409 6.42526 7.57409 5.90859 7.24909 5.58359C6.92409 5.25859 6.40742 5.25859 6.08242 5.58359L2.25742 9.40859C1.93242 9.73359 1.93242 10.2586 2.25742 10.5836L6.08242 14.4169C6.40742 14.7419 6.92409 14.7419 7.24909 14.4169C7.57409 14.0919 7.57409 13.5753 7.24909 13.2503ZM12.7491 13.2503L15.9991 10.0003L12.7491 6.75026C12.4241 6.42526 12.4241 5.90859 12.7491 5.58359C13.0741 5.25859 13.5908 5.25859 13.9158 5.58359L17.7408 9.40859C18.0658 9.73359 18.0658 10.2586 17.7408 10.5836L13.9158 14.4169C13.5908 14.7419 13.0741 14.7419 12.7491 14.4169C12.4241 14.0919 12.4241 13.5753 12.7491 13.2503Z"
          fill={`${theme.colors['icon.primary']}`}
        />
      </g>
      <defs>
        <clipPath id="clip0_3429_51491">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function TableResultIcon() {
  const [, theme] = useStyletron();

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path
        d="M15.6667 4V15.6667H4V17.3333H15.6667C16.5833 17.3333 17.3333 16.5833 17.3333 15.6667V4H15.6667Z"
        fill={`${theme.colors['icon.primary']}`}
      />
      <path
        d="M12.3327 0.666992H2.33268C1.41602 0.666992 0.666016 1.41699 0.666016 2.33366V12.3337C0.666016 13.2503 1.41602 14.0003 2.33268 14.0003H12.3327C13.2493 14.0003 13.9993 13.2503 13.9993 12.3337V2.33366C13.9993 1.41699 13.2493 0.666992 12.3327 0.666992ZM6.49935 12.3337H2.33268V8.16699H6.49935V12.3337ZM12.3327 12.3337H8.16602V8.16699H12.3327V12.3337ZM12.3327 6.50033H2.33268V2.33366H12.3327V6.50033Z"
        fill={`${theme.colors['icon.primary']}`}
      />
    </svg>
  );
}

export function GraphResultIcon() {
  const [, theme] = useStyletron();

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" viewBox="0 0 22 20" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.73905 5.16602C8.29134 5.16602 8.73905 4.7183 8.73905 4.16602C8.73905 3.61373 8.29134 3.16602 7.73905 3.16602C7.18677 3.16602 6.73905 3.61373 6.73905 4.16602C6.73905 4.7183 7.18677 5.16602 7.73905 5.16602ZM7.73905 6.66602C8.03672 6.66602 8.32224 6.61399 8.58701 6.51854L11.2344 9.63222C11.0389 9.98857 10.9277 10.3977 10.9277 10.8329C10.9277 12.2136 12.047 13.3329 13.4277 13.3329C14.8084 13.3329 15.9277 12.2136 15.9277 10.8329C15.9277 10.5386 15.8769 10.2562 15.7835 9.99396L17.6081 8.55634C17.9049 8.68054 18.2307 8.74914 18.5725 8.74914C19.9532 8.74914 21.0725 7.62986 21.0725 6.24915C21.0725 4.86843 19.9532 3.74915 18.5725 3.74915C17.1918 3.74915 16.0725 4.86843 16.0725 6.24915C16.0725 6.73158 16.2092 7.18209 16.4459 7.56409L14.8801 8.79778C14.4707 8.50511 13.9693 8.33288 13.4277 8.33288C13.0299 8.33288 12.6537 8.42583 12.3198 8.59118L9.78237 5.60682C10.07 5.19958 10.2391 4.70253 10.2391 4.16602C10.2391 2.7853 9.11977 1.66602 7.73905 1.66602C6.35834 1.66602 5.23905 2.7853 5.23905 4.16602C5.23905 4.89776 5.55343 5.55607 6.05448 6.01326L3.60726 13.3392C3.54796 13.335 3.4881 13.3329 3.42773 13.3329C2.04702 13.3329 0.927734 14.4522 0.927734 15.8329C0.927734 17.2136 2.04702 18.3329 3.42773 18.3329C4.80845 18.3329 5.92773 17.2136 5.92773 15.8329C5.92773 15.0498 5.56773 14.3509 5.00419 13.8925L7.42472 6.64644C7.52768 6.65936 7.63259 6.66602 7.73905 6.66602ZM13.4277 11.8329C13.98 11.8329 14.4277 11.3852 14.4277 10.8329C14.4277 10.2806 13.98 9.83288 13.4277 9.83288C12.8755 9.83288 12.4277 10.2806 12.4277 10.8329C12.4277 11.3852 12.8755 11.8329 13.4277 11.8329ZM19.5725 6.24915C19.5725 6.80143 19.1248 7.24914 18.5725 7.24914C18.0202 7.24914 17.5725 6.80143 17.5725 6.24915C17.5725 5.69686 18.0202 5.24915 18.5725 5.24915C19.1248 5.24915 19.5725 5.69686 19.5725 6.24915ZM3.42773 16.8329C3.98002 16.8329 4.42773 16.3852 4.42773 15.8329C4.42773 15.2806 3.98002 14.8329 3.42773 14.8329C2.87545 14.8329 2.42773 15.2806 2.42773 15.8329C2.42773 16.3852 2.87545 16.8329 3.42773 16.8329Z"
        fill={`${theme.colors['icon.primary']}`}
      />
    </svg>
  );
}

export function Plane() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="31" height="26" viewBox="0 0 31 26" fill="none">
      <path
        d="M15.6433 17.9684L23.7635 20.5354L29.7139 1L1.78613 13.3126L8.61176 15.4088"
        stroke="#F4811F"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.9209 25L8.61133 15.4088L20.4385 9.64233L10.9209 25Z"
        stroke="#F4811F"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function Book() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="33" height="26" viewBox="0 0 33 26" fill="none">
      <path
        d="M2.55435 4.84766H1.90217C1.54348 4.84766 1.25 5.14113 1.25 5.49983V22.4564C1.25 22.815 1.54348 23.1085 1.90217 23.1085H11.4174C12.0044 23.1085 12.5717 23.3433 12.9891 23.7607C13.4065 24.1781 13.9674 24.4129 14.5609 24.4129H17.9391C18.5261 24.4129 19.0935 24.1781 19.5109 23.7607C19.9283 23.3433 20.4957 23.1085 21.0826 23.1085H30.5978C30.9565 23.1085 31.25 22.815 31.25 22.4564V5.49983C31.25 5.14113 30.9565 4.84766 30.5978 4.84766H29.9457"
        stroke="#F4811F"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.46709 2.89136H3.8584V20.5001H12.3367C13.6019 20.5001 14.4562 21.1522 14.9454 21.8044H16.2497V6.8044C16.2497 4.68484 14.978 2.89136 12.3367 2.89136H10.3801"
        stroke="#F4811F"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M28.6413 2.89136V20.5001H20.163C18.8978 20.5001 18.0435 20.8522 17.5543 21.8044H16.25V6.8044C16.25 4.68484 17.5217 2.89136 20.163 2.89136H28.6413Z"
        stroke="#F4811F"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.46777 9.413L8.4243 8.10865L10.3808 9.413V1.58691H6.46777V9.413Z"
        stroke="#F4B519"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function ByocSelected() {
  const [, theme] = useStyletron();
  return (
    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Group 47045">
        <path
          id="Subtract"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M20.6665 0H0.666504L20.6665 20V0ZM18.224 3.50172C18.5011 3.19384 18.4761 2.71962 18.1682 2.44253C17.8603 2.16544 17.3861 2.19039 17.109 2.49828L13.1379 6.91069L11.1968 4.96967C10.9039 4.67678 10.4291 4.67678 10.1362 4.96967C9.84328 5.26256 9.84328 5.73744 10.1362 6.03033L12.4871 8.38127C12.872 8.76614 13.5009 8.7496 13.865 8.34503L18.224 3.50172Z"
          fill={`${theme.colors['card.border.selected']}`}
        />
      </g>
    </svg>
  );
}

export function AWS(props: SVGProps<SVGSVGElement>) {
  const [, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="35" height="22" viewBox="0 0 35 22" fill="none" {...props}>
      <path
        d="M8.74865 7.82762C8.74865 8.22417 8.74865 8.48854 8.88021 8.88509L9.27489 9.67819C9.27489 9.81037 9.40645 9.81037 9.40645 9.94256C9.40645 10.0747 9.27489 10.2069 9.14333 10.3391L8.48553 10.8678C8.35397 10.8678 8.35397 11 8.22241 11C8.09085 11 7.95929 11 7.82773 10.8678C7.69617 10.7357 7.56461 10.6035 7.43305 10.3391C7.30149 10.2069 7.16993 9.94256 7.03837 9.67819C6.11744 10.3391 4.9334 11 3.6178 11C2.82843 11 1.90751 10.7357 1.38127 10.2069C0.855025 9.67819 0.460344 8.88509 0.591905 8.09199C0.591905 7.16671 0.986587 6.3736 1.64439 5.84487C2.43375 5.18395 3.48623 4.91959 4.40716 4.91959C4.80184 4.91959 5.19652 4.91959 5.5912 5.05177C5.98588 5.05177 6.51213 5.18395 6.90681 5.31614V4.25867C7.03837 3.59775 6.77524 2.93684 6.38056 2.4081C5.85432 2.01155 5.19652 1.87937 4.53872 1.87937C4.14404 1.87937 3.74936 1.87937 3.35467 2.01155L2.17063 2.4081C2.03907 2.4081 1.90751 2.54029 1.77595 2.54029H1.64439C1.51283 2.54029 1.38127 2.4081 1.38127 2.14374V1.74719C1.38127 1.615 1.38127 1.48282 1.51283 1.35063C1.64439 1.21845 1.77595 1.21845 1.77595 1.08627C2.30219 0.821901 2.69687 0.689718 3.22311 0.557534C3.48623 0.557534 4.14404 0.425351 4.67028 0.425351C5.72276 0.293168 6.77524 0.689718 7.69617 1.35063C8.35397 2.14374 8.74865 3.06902 8.61709 4.12649V7.82762H8.74865ZM4.01248 9.54601C4.40716 9.54601 4.80184 9.41382 5.19652 9.28164C5.5912 9.14946 5.98588 8.88509 6.249 8.62072C6.38056 8.48854 6.51212 8.22417 6.64368 7.95981C6.77524 7.56326 6.77524 7.1667 6.77524 6.90234V6.3736C6.38056 6.24142 6.11744 6.24142 5.72276 6.24142C5.32808 6.24142 5.06496 6.10924 4.67028 6.10924C4.01248 6.10924 3.48623 6.24142 2.95999 6.50579C2.56531 6.77015 2.43375 7.29889 2.43375 7.69544C2.43375 8.09199 2.56531 8.62072 2.82843 8.88509C3.09155 9.41382 3.61779 9.54601 4.01248 9.54601ZM13.2217 10.7357C13.0901 10.7357 12.9586 10.7357 12.827 10.6035C12.6955 10.4713 12.5639 10.3391 12.5639 10.2069L9.9327 1.48282C9.9327 1.35063 9.80114 1.21845 9.80114 1.08627C9.80114 0.954085 9.80114 0.954085 9.9327 0.821901C9.9327 0.821901 10.0643 0.689718 10.1958 0.689718H11.3799C11.5114 0.689718 11.643 0.689718 11.7745 0.821901C11.9061 0.954085 11.9061 1.08627 12.0377 1.21845L14.0111 8.62072L15.8529 1.21845C15.8529 1.08627 15.9845 0.954085 16.116 0.821901C16.2476 0.689718 16.3792 0.689718 16.6423 0.689718H17.5632C17.6948 0.689718 17.8263 0.689718 18.0894 0.821901C18.221 0.954085 18.221 1.08627 18.3526 1.21845L20.1944 8.75291L22.1678 1.21845C22.1678 1.08627 22.2994 0.954085 22.4309 0.821901C22.5625 0.689718 22.6941 0.689718 22.8256 0.689718H23.8781C24.0097 0.689718 24.0097 0.689718 24.1412 0.821901C24.1412 0.821901 24.2728 0.954085 24.2728 1.08627V1.21845C24.2728 1.35063 24.2728 1.35063 24.1412 1.48282L21.3785 10.2069C21.3785 10.3391 21.2469 10.4713 21.1153 10.6035C20.9838 10.7357 20.8522 10.7357 20.7206 10.7357H19.6682C19.5366 10.7357 19.405 10.7357 19.1419 10.6035C19.0104 10.4713 19.0104 10.3391 18.8788 10.2069L17.037 2.93684L15.3267 10.2069C15.3267 10.3391 15.1951 10.4713 15.0636 10.6035C14.932 10.7357 14.8004 10.7357 14.5373 10.7357H13.2217ZM28.088 11C27.4302 11 26.904 11 26.3777 10.8678C25.8515 10.7357 25.4568 10.6035 25.0621 10.4713C24.799 10.3391 24.6675 10.0747 24.6675 9.81037V9.14946C24.6675 8.88509 24.799 8.75291 24.9306 8.75291H25.1937L25.4568 8.88509C25.8515 9.01727 26.2462 9.14946 26.7724 9.28164C27.1671 9.41382 27.6934 9.41382 28.2196 9.41382C28.7458 9.41382 29.4036 9.28164 29.9299 9.01727C30.3246 8.75291 30.5877 8.35636 30.5877 7.95981C30.5877 7.69544 30.4561 7.43107 30.3246 7.16671C29.5352 7.03452 29.1405 6.77015 28.7458 6.63797L27.0355 6.10924C26.3777 5.97705 25.7199 5.5805 25.1937 4.91959C24.799 4.39085 24.6675 3.86212 24.6675 3.33339C24.6675 2.93684 24.799 2.4081 24.9306 2.14374C25.1937 1.74719 25.4568 1.48282 25.8515 1.21845C26.2462 0.954085 26.6409 0.821901 27.0355 0.689718C27.4302 0.557534 27.9565 0.425351 28.3512 0.425351H29.1405C29.4036 0.425351 29.6668 0.557534 29.9299 0.557534C30.193 0.557534 30.3246 0.689718 30.5877 0.689718C30.7192 0.689718 30.8508 0.821901 31.1139 0.821901C31.2455 0.954085 31.377 0.954085 31.5086 1.08627C31.6402 1.21845 31.6402 1.35063 31.6402 1.48282V2.01155C31.6402 2.27592 31.5086 2.4081 31.377 2.4081C31.2455 2.4081 31.1139 2.4081 30.9824 2.27592C30.3246 2.01155 29.5352 1.87937 28.7458 1.87937C28.2196 1.87937 27.6933 2.01155 27.1671 2.14374C26.7724 2.54029 26.5093 2.80465 26.5093 3.33339C26.5093 3.59775 26.6409 3.86212 26.904 4.12649C27.2987 4.39085 27.6934 4.65522 28.2196 4.7874L29.7983 5.31614C30.4561 5.44832 31.1139 5.84487 31.6402 6.3736C32.0348 6.90234 32.1664 7.43107 32.1664 7.95981C32.1664 8.35636 32.0348 8.88509 31.9033 9.28164C31.6402 9.67819 31.377 10.0747 31.1139 10.3391C30.7192 10.6035 30.3246 10.8678 29.7983 11H28.088Z"
        fill={theme.colors['card.text.primary']}
      />
      <path
        d="M31.8621 16.4031C28.0201 19.7344 22.4558 21.6 17.6864 21.6C11.3272 21.6 5.10052 18.8017 0.331133 13.738C-0.0663165 13.3382 0.331133 12.8052 0.728582 13.0717C6.0279 16.6696 11.9896 18.5352 18.0839 18.5352C22.5883 18.5352 27.0927 17.4691 31.3322 15.3371C31.9946 15.0705 32.5245 15.8701 31.8621 16.4031Z"
        fill="#FF9900"
      />
      <path
        d="M32.4543 14.8428C31.9246 14.1925 29.2757 14.5827 28.0837 14.7127C27.6863 14.7127 27.6863 14.4526 27.9512 14.1925C30.0703 12.7619 33.6463 13.1521 34.0437 13.6723C34.441 14.1925 33.9112 17.704 31.9246 19.2647C31.6597 19.5248 31.2623 19.3947 31.5272 19.0046C31.9246 17.9641 32.9841 15.4931 32.4543 14.8428Z"
        fill="#FF9900"
      />
    </svg>
  );
}

export function TutorialIcon() {
  const [, theme] = useStyletron();
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="14" viewBox="0 0 18 14" fill="none">
      <path
        d="M1.46739 2.15723H1.1087C0.911413 2.15723 0.75 2.31864 0.75 2.51592V11.842C0.75 12.0393 0.911413 12.2007 1.1087 12.2007H6.34207C6.6649 12.2007 6.97696 12.3298 7.20652 12.5594C7.43609 12.789 7.74457 12.9181 8.07098 12.9181H9.92903C10.2519 12.9181 10.5639 12.789 10.7935 12.5594C11.023 12.3298 11.3351 12.2007 11.6579 12.2007H16.8913C17.0886 12.2007 17.25 12.0393 17.25 11.842V2.51592C17.25 2.31864 17.0886 2.15723 16.8913 2.15723H16.5326"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M1.46739 2.15723H1.1087C0.911413 2.15723 0.75 2.31864 0.75 2.51592V11.842C0.75 12.0393 0.911413 12.2007 1.1087 12.2007H6.34207C6.6649 12.2007 6.97696 12.3298 7.20652 12.5594C7.43609 12.789 7.74457 12.9181 8.07098 12.9181H9.92903C10.2519 12.9181 10.5639 12.789 10.7935 12.5594C11.023 12.3298 11.3351 12.2007 11.6579 12.2007H16.8913C17.0886 12.2007 17.25 12.0393 17.25 11.842V2.51592C17.25 2.31864 17.0886 2.15723 16.8913 2.15723H16.5326"
        stroke={theme.colors['icon.primary']}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.77055 1.08203H2.18359V10.7668H6.84664C7.54251 10.7668 8.0124 11.1255 8.28142 11.4842H8.99881V3.23421C8.99881 2.06844 8.29935 1.08203 6.84664 1.08203H5.77055Z"
        fill={theme.colors['background.primary']}
        stroke={theme.colors['icon.primary']}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.8152 1.08203V10.7668H11.1522C10.4563 10.7668 9.98641 10.9605 9.71739 11.4842H9V3.23421C9 2.06844 9.69946 1.08203 11.1522 1.08203H15.8152Z"
        fill={theme.colors['background.primary']}
        stroke={theme.colors['icon.primary']}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.334 4.50033C10.334 4.04009 10.7071 3.66699 11.1673 3.66699H13.6673C14.1276 3.66699 14.5007 4.04009 14.5007 4.50033V4.50033C14.5007 4.96056 14.1276 5.33366 13.6673 5.33366H11.1673C10.7071 5.33366 10.334 4.96056 10.334 4.50033V4.50033Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M10.334 7.51497C10.334 7.05474 10.7071 6.68164 11.1673 6.68164H13.6673C14.1276 6.68164 14.5007 7.05474 14.5007 7.51497V7.51497C14.5007 7.97521 14.1276 8.34831 13.6673 8.34831H11.1673C10.7071 8.34831 10.334 7.97521 10.334 7.51497V7.51497Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M3.58398 4.50033C3.58398 4.04009 3.95708 3.66699 4.41732 3.66699H6.91732C7.37755 3.66699 7.75065 4.04009 7.75065 4.50033V4.50033C7.75065 4.96056 7.37756 5.33366 6.91732 5.33366H4.41732C3.95708 5.33366 3.58398 4.96056 3.58398 4.50033V4.50033Z"
        fill={theme.colors['icon.primary']}
      />
      <path
        d="M3.58398 7.51497C3.58398 7.05474 3.95708 6.68164 4.41732 6.68164H6.91732C7.37756 6.68164 7.75065 7.05474 7.75065 7.51497V7.51497C7.75065 7.97521 7.37756 8.34831 6.91732 8.34831H4.41732C3.95708 8.34831 3.58398 7.97521 3.58398 7.51497V7.51497Z"
        fill={theme.colors['icon.primary']}
      />
    </svg>
  );
}

/* v8 ignore end */
