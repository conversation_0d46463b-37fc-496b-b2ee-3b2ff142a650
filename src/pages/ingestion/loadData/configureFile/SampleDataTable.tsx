import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { TableBuilder } from '@tigergraph/app-ui-lib/table';
import { TableBuilderColumn } from 'baseui/table-semantic';
import { LoadingIndicator } from '@/components/loading-indicator';
import { SampleData } from '@/pages/ingestion/loadData/configureFile/DataSourceFormat';
import { DataFormat } from '@tigergraph/tools-models/loading-job';
import ReactCodeMirror from '@uiw/react-codemirror';
import { cmOptions } from '@/pages/editor/query/builtinEndpoints/type';
import { useTheme } from '@/contexts/themeContext';
import { xcodeDark, xcodeLight } from '@uiw/codemirror-theme-xcode';

interface SampleDataTableProps {
  sampleData: SampleData | undefined;
  dataFormat: DataFormat;
  rowLimit?: number;
  isLoading?: boolean;
}

export default function SampleDataTable(props: SampleDataTableProps) {
  const [css, theme] = useStyletron();
  const { sampleData, dataFormat, rowLimit = 10, isLoading } = props;
  const { themeType } = useTheme();

  if (!sampleData) {
    return <></>;
  }

  return (
    <>
      {dataFormat === DataFormat.CSV || dataFormat === DataFormat.TSV ? (
        <TableBuilder
          data={sampleData.data.slice(0, rowLimit)}
          isLoading={isLoading}
          loadingMessage={() => <LoadingIndicator message="Loading the sample data" />}
          overrides={{
            Root: {
              style: {
                width: '100%',
                overflow: 'auto',
                minWidth: '200px',
              },
            },
            TableHeadCell: {
              style: {
                backgroundColor: theme.colors['background.primary'],
              },
            },
            TableBodyCell: {
              style: {
                backgroundColor: theme.colors['background.primary'],
              },
            },
          }}
        >
          {(sampleData.data?.[0] as string[])?.map((_, i) => (
            <TableBuilderColumn key={i} header={sampleData.header?.[i]}>
              {(row) => row[i]}
            </TableBuilderColumn>
          ))}
        </TableBuilder>
      ) : (
        <div
          className={css({
            border: `1px solid ${theme.colors.divider}`,
            borderRadius: '2px',
            height: '100%',
            overflow: 'auto',
          })}
        >
          <ReactCodeMirror
            value={sampleData.data.length > 0 ? JSON.stringify(sampleData.data, null, 2) : ''}
            basicSetup={cmOptions}
            theme={themeType === 'light' ? xcodeLight : xcodeDark}
            width={'100%'}
            height={'100%'}
            editable={false}
          />
        </div>
      )}
    </>
  );
}
