import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { QueryParam } from '@tigergraph/tools-models';
// import { Toggle } from '@tigergraph/app-ui-lib/toggle';
import SimpleTypeParamInput from '@/pages/editor/query/params/SimpleTypeParamInput';
import MapParamInput from '@/pages/editor/query/params/MapParamInput';
import ListParamInput from '@/pages/editor/query/params/ListParamInput';
import VertexParamInput from '@/pages/editor/query/params/VertexParamInput';
import { getParamTypeDisplayName, ParamErrors } from '@/utils/queryParam';

interface QueryParamFormProps {
  queryParams: QueryParam[];
  queryPayload: Record<string, any>;
  onQueryParamChange: (param: QueryParam, value: any) => void;
  graphName: string;
  paramErrors: ParamErrors;
}

export default function QueryParamForm({
  queryParams,
  queryPayload,
  onQueryParamChange,
  graphName,
  paramErrors,
}: QueryParamFormProps) {
  const [css, theme] = useStyletron();

  const handleParamChange = (param: QueryParam, value: any) => {
    onQueryParamChange(param, value);
  };

  return (
    <div className={css({ display: 'flex', flexDirection: 'column', gap: '16px' })}>
      {!queryParams.length && <div className={css({ color: theme.colors['text.secondary'] })}>No parameters.</div>}
      {queryParams.map((param) => {
        const { paramName, paramType } = param;
        const error = paramErrors[paramName];

        return (
          <div key={paramName}>
            <div
              className={css({
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '4px',
              })}
            >
              <div className={css({ fontWeight: 500 })}>
                {paramName}: {getParamTypeDisplayName(paramType)}
              </div>
            </div>

            {paramType.type === 'VERTEX' ? (
              <VertexParamInput
                param={param}
                value={queryPayload[paramName]}
                onChange={(value) => handleParamChange(param, value)}
                graphName={graphName}
                error={error as string}
              />
            ) : paramType.type === 'LIST' ? (
              <ListParamInput
                param={param}
                value={queryPayload[paramName]}
                onChange={(value) => handleParamChange(param, value)}
                graphName={graphName}
                error={error as string[]}
              />
            ) : paramType.type === 'MAP' ? (
              <MapParamInput
                param={param}
                value={queryPayload[paramName]}
                onChange={(value) => handleParamChange(param, value)}
                error={error as { key: string; value: string }[]}
              />
            ) : (
              <SimpleTypeParamInput
                param={param}
                value={queryPayload[paramName]}
                onChange={(value) => handleParamChange(param, value)}
                error={error as string}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}
