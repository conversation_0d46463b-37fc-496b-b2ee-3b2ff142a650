import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import VertexTypeSelect from './VertexTypeSelect';
import { QueryParam, QueryParamVertexType } from '@tigergraph/tools-models';
import { Input } from '@tigergraph/app-ui-lib/input';
import { ChangeEvent } from 'react';

interface VertexParamInputProps {
  param: QueryParam;
  value: { id: string; type: string };
  onChange: (value: { id: string; type: string }) => void;
  disabled?: boolean;
  graphName: string;
  hideLabel?: boolean;
  error?: string;
}

export default function VertexParamInput({
  param,
  value,
  onChange,
  disabled = false,
  graphName,
  hideLabel = false,
  error,
}: VertexParamInputProps) {
  const [css, theme] = useStyletron();
  const vertexType = (param.paramType as QueryParamVertexType).vertexType;
  const isWildcard = vertexType === '*';

  return (
    <div className={css({ display: 'flex', gap: '8px', flex: 1 })}>
      <div className={css({ flex: 1 })}>
        {!hideLabel && (
          <div
            className={css({
              marginBottom: '8px',
              color: theme.colors['dropdown.text'],
              fontWeight: 500,
              fontSize: '14px',
            })}
          >
            Vertex ID
          </div>
        )}
        <Input
          value={value?.id || ''}
          onChange={(e: ChangeEvent<HTMLInputElement>) => onChange({ ...value, id: e.target.value })}
          disabled={disabled}
          placeholder="Vertex ID"
        />
        {!!error && (
          <div className={css({ color: theme.colors['text.danger'], fontSize: '12px', marginTop: '4px' })}>{error}</div>
        )}
      </div>
      <div className={css({ flex: 1 })}>
        {!hideLabel && (
          <div
            className={css({
              marginBottom: '8px',
              color: theme.colors['dropdown.text'],
              fontWeight: 500,
              fontSize: '14px',
            })}
          >
            Vertex Type
          </div>
        )}
        <VertexTypeSelect
          value={value?.type || ''}
          onChange={(type) => onChange({ ...value, type })}
          disabled={disabled || !isWildcard}
          graphName={graphName}
        />
      </div>
    </div>
  );
}
