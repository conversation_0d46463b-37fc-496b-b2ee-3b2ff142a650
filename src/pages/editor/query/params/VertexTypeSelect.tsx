import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { useQuery } from 'react-query';
import { Select } from '@tigergraph/app-ui-lib/select';
import { useMemo } from 'react';
import { getGraphSchema } from '@tigergraph/tools-models';

interface VertexTypeSelectProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  graphName: string;
}

export default function VertexTypeSelect({ value, onChange, disabled = false, graphName }: VertexTypeSelectProps) {
  const [css, theme] = useStyletron();
  const { currentWorkspace } = useWorkspaceContext();

  // Fetch vertex types from the graph schema
  const { data: schemaData, isLoading } = useQuery(
    ['vertexTypes', graphName, currentWorkspace?.nginx_host],
    async () => {
      if (!currentWorkspace || !graphName) {
        return { vertexTypes: [] };
      }

      const response = await getGraphSchema(
        { graph: graphName },
        {
          baseURL: `https://${currentWorkspace.nginx_host}`,
          version: currentWorkspace.tg_version,
        }
      );

      const vertexTypes = response.data.results?.VertexTypes?.map((vt: any) => vt.Name) || [];
      return { vertexTypes };
    },
    {
      enabled: !!graphName && !!currentWorkspace && !disabled,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  const vertexTypes = useMemo(() => {
    return (schemaData?.vertexTypes || []).map((type: string) => ({
      id: type,
      label: type,
    }));
  }, [schemaData]);

  return (
    <Select
      options={vertexTypes}
      value={value ? [{ id: value, label: value }] : []}
      onChange={({ value }) => onChange((value[0]?.id as string) || '')}
      disabled={disabled}
      searchable={true}
      isLoading={isLoading}
      placeholder="Select vertex type"
      overrides={{
        Dropdown: {
          style: {
            maxHeight: '200px',
          },
        },
      }}
    />
  );
}
