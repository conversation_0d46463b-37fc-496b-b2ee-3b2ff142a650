import { GsqlQueryMeta, QuerySyntax } from '@tigergraph/tools-models';
import JSONbigint from 'json-bigint';

export const replaceQueryName = (code: string, name: string, newName: string): string => {
  // The regex pattern matches "QUERY (arbitrary whitespace) name (arbitrary whitespace) ("
  const regex = `(QUERY\\s+)(${name})(\\s*\\()`;
  return code.replace(new RegExp(regex, 'gi'), `$1${newName}$3`);
};

// TODO add this to tools-model
export const normalizeLintResults = (
  code: string,
  error: any,
  syntax: QuerySyntax
): {
  startLine: number;
  startColumn: number;
  endLine: number;
  endColumn: number;
  message: string;
} => {
  const lines = code.split('\n');
  const message = error.msg;
  let markStart: number = 0;
  let markStop: number = 0;

  // For cypher query, some errors are generated after the query has gone through the transformation stages
  // these types of errors will probably not have the correct line number, so we just mark the first line
  if (syntax === 'CYPHER' && error.errorcode !== 9999) {
    return {
      startLine: 0,
      startColumn: 0,
      endLine: 0,
      endColumn: 0,
      message: message,
    };
  }

  // Has startLine, startColumn, endLine, endColumn
  if ('startLine' in error && 'startColumn' in error && 'endLine' in error && 'endColumn' in error) {
    return {
      startLine: error.startLine - 1,
      startColumn: error.startColumn,
      endLine: error.endLine - 1,
      endColumn: error.endColumn,
      message: message,
    };
  }

  // Has start and stop index
  if ('startindex' in error && 'stopindex' in error) {
    markStart = error.startindex;
    markStop = error.stopindex + 1;
  }

  if (!markStart) {
    // If it's parsing error, mark until token ends
    if ('line' in error) {
      const line = error.line - 1; // parsing error line number is always actual line number + 1
      const pos = error.charpositioninline;
      let endPos = pos;

      if (line < lines.length) {
        for (; endPos < lines[line].length; endPos++) {
          if ([' ', '\t', '\n'].includes(lines[line].charAt(endPos))) {
            break;
          }
        }
      }

      return {
        startLine: line,
        startColumn: pos,
        endLine: line,
        endColumn: endPos,
        message: message,
      };
    } else {
      // Nothing there, mark the whole code
      return {
        startLine: 0,
        startColumn: 0,
        endLine: lines.length - 1,
        endColumn: lines[lines.length - 1].length,
        message: message,
      };
    }
  } else {
    // Contains both start position and end position
    const prefixStart = code.substring(0, markStart);
    const prefixStop = code.substring(0, markStop);
    return {
      startLine: prefixStart.split('\n').length - 1,
      startColumn: markStart - prefixStart.lastIndexOf('\n') - 1,
      endLine: prefixStop.split('\n').length - 1,
      endColumn: markStop - prefixStop.lastIndexOf('\n') - 1,
      message: message,
    };
  }
};

export interface HTTPConfig {
  url: string;
  headers: Record<string, string>;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: object;
}
export function genCurlText(requestConfig: HTTPConfig) {
  const { url, headers, data, method = 'GET' } = requestConfig;
  let curlCommand = `curl -X ${method} '${url}' \\\n`;

  if (headers) {
    const length = Object.keys(headers).length;
    for (const [index, [key, value]] of Object.entries(headers).entries()) {
      if (index === length - 1) {
        curlCommand += ` -H '${key}: ${value}'`;
      } else {
        curlCommand += ` -H '${key}: ${value}' \\\n`;
      }
    }
  }

  if (data && method !== 'GET') {
    const dataString = JSONbigint.stringify(data);
    curlCommand += `\\\n --data-raw '${dataString}'`;
  }

  return curlCommand;
}

export function genJSText(requestConfig: HTTPConfig) {
  const { url, headers, data, method = 'GET' } = requestConfig;
  let jsCommand = `fetch('${url}', {\n  method: '${method}'`;

  if (headers) {
    const headersString = JSONbigint.stringify(headers, null, 4);
    jsCommand += `,\n  headers: ${headersString}`;
  }

  if (data && method !== 'GET') {
    const dataString = JSONbigint.stringify(data, null, 2);
    jsCommand += `,\n  body: ${dataString}`;
  }

  jsCommand += '\n});';

  return jsCommand;
}

export function genPyText(requestConfig: HTTPConfig) {
  const { url, headers, data, method = 'GET' } = requestConfig;

  let pythonCode = 'import requests\n\n';
  pythonCode += `url = '${url}'\n`;
  pythonCode += 'headers = {\n';
  for (const key in headers) {
    pythonCode += `    '${key}': '${headers[key]}',\n`;
  }
  pythonCode += '}\n';

  if (data && method !== 'GET') {
    pythonCode += `data = ${JSONbigint.stringify(data)}\n`;
  }

  switch (method) {
    case 'GET':
      pythonCode += 'response = requests.get(url, headers=headers)\n';
      break;
    case 'POST':
      pythonCode += 'response = requests.post(url, headers=headers, json=data)\n';
      break;
    case 'PUT':
      pythonCode += 'response = requests.put(url, headers=headers, json=data)\n';
      break;
    case 'DELETE':
      pythonCode += 'response = requests.delete(url, headers=headers)\n';
      break;
    case 'PATCH':
      pythonCode += 'response = requests.patch(url, headers=headers, json=data)\n';
      break;
  }

  pythonCode += 'print(response.text)\n';
  return pythonCode;
}

export function getInterpretQueryCode(query: GsqlQueryMeta): string {
  const pattern = new RegExp(/\bCREATE\b([\s\S]*?)\bQUERY\b[\s\S]*?\(/i);
  const isGSQL = !query.syntax || query.syntax.includes('GSQL');

  return query.code.replace(pattern, isGSQL ? 'INTERPRET QUERY (' : 'INTERPRET OPENCYPHER QUERY (');
}

export function isDistributedQuery(queryContent: string): boolean {
  const pattern = '^\\s*create\\s+(or\\s+replace\\s+)?(?:batch\\s+)?(?:distributed\\s+)query\\s+([^\\s]+)\\s*\\(';

  return !!queryContent.match(new RegExp(pattern, 'i'));
}
