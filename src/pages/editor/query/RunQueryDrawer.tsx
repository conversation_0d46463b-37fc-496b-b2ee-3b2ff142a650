import { useWorkspaceContext } from '@/contexts/workspaceContext';
import RunAPIDrawer from '@/pages/editor/query/RunAPIDrawer';
import { ApiType } from '@/pages/editor/query/builtinEndpoints/type';
import { GsqlQueryMeta, QueryMetaLogic, QueryParam } from '@tigergraph/tools-models';
import { useMemo } from 'react';

export interface RunQueryDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  graphName: string;
  query: GsqlQueryMeta;
}

export default function RunQueryDrawer(props: RunQueryDrawerProps) {
  const { isOpen, onClose, graphName, query } = props;
  const { currentWorkspace } = useWorkspaceContext();

  const queryParams: QueryParam[] = useMemo(() => {
    const params = query.endpoint?.query?.[graphName]?.[query.name]?.['GET/POST']?.parameters || {};
    return QueryMetaLogic.convertGSQLParameters(params);
  }, [query, graphName]);

  return (
    <RunAPIDrawer
      isOpen={isOpen}
      onClose={onClose}
      wp={currentWorkspace!}
      graphName={graphName}
      path={`/restpp/query/${graphName}/${query.name}`}
      method={query.installed ? ApiType.POST : ApiType.GET}
      bodyParameters={queryParams}
      type="query"
      name={query.name}
      query={query}
    />
  );
}
