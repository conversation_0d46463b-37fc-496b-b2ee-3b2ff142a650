import IconButton from '@/components/IconButton';
import { showToast } from '@/components/styledToasterContainer';
import { FileTabStyleObject } from '@/pages/editor/file/styleObject';
import { SnippetsTabs } from '@/pages/editor/query/SnippetsTabs';
import { genCurlText, genJSText, genPyText, HTTPConfig } from '@/pages/editor/query/util';
import useEditorTheme from '@/pages/editor/useEditorTheme';
import { CopyIcon } from '@/pages/home/<USER>';
import { ApiType } from '@/pages/editor/query/builtinEndpoints/type';
import { buildReqBodyFromParamPayload } from '@/utils/queryParam';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { QueryParam } from '@tigergraph/tools-models';
import ReactCodeMirror from '@uiw/react-codemirror';
import { Tab } from 'baseui/tabs-motion';
import { useEffect, useMemo, useState } from 'react';
import useCopyClipboard from 'react-use-clipboard';

interface SnippetsCopierProps {
  parameters: QueryParam[];
  payload: Record<string, any>;
  url: string;
  secret: string;
  method: ApiType;
}

type LanguageType = 'cURL' | 'JavaScript' | 'Python';

export default function SnippetsCopier({ parameters, payload, url, secret, method }: SnippetsCopierProps) {
  const [css, theme] = useStyletron();
  const editorTheme = useEditorTheme({ background: theme.colors['input.background.disabled'] });
  const [activeKey, setActiveKey] = useState<LanguageType>('cURL');

  const snippets = useMemo(() => {
    let data = buildReqBodyFromParamPayload(parameters, payload);

    const config: HTTPConfig = {
      url,
      method,
      headers: {
        Authorization: `Bearer ${secret}`,
        Accept: 'application/json, text/plain, */*',
        ContentType: 'application/json',
      },
      data,
    };
    const cURL = genCurlText(config);
    const JavaScript = genJSText(config);
    const Python = genPyText(config);
    return { cURL, JavaScript, Python };
  }, [parameters, payload, url, method, secret]);

  const [curlCopied, setCurlCopied] = useCopyClipboard(snippets[activeKey], {
    successDuration: 1000,
  });
  useEffect(() => {
    if (curlCopied) {
      showToast({
        kind: 'positive',
        message: 'Code snippet copied successfully.',
      });
    }
  }, [curlCopied]);

  return (
    <div className={css({ position: 'relative' })}>
      <SnippetsTabs
        activeKey={activeKey}
        onChange={({ activeKey }) => {
          setActiveKey(activeKey as LanguageType);
        }}
      >
        {Object.entries(snippets).map(([name, value]) => (
          <Tab key={name} overrides={FileTabStyleObject(theme)} title={name}>
            <div
              className={css({
                border: `1px solid ${theme.colors['border.tertiary']}`,
                borderTop: 'none',
              })}
            >
              <ReactCodeMirror value={value} editable={false} theme={editorTheme} height="200px" />
            </div>
          </Tab>
        ))}
      </SnippetsTabs>
      <div className={css({ position: 'absolute', top: 0, right: 0 })}>
        <IconButton onClick={setCurlCopied}>
          <CopyIcon />
        </IconButton>
      </div>
    </div>
  );
}
