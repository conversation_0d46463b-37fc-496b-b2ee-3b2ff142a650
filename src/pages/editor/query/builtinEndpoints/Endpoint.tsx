import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ApiType } from '@/pages/editor/query/builtinEndpoints/type';
import { expand } from 'inline-style-expand-shorthand';
import { useTheme } from '@/contexts/themeContext';
import { GsqlParameter } from '@tigergraph/tools-models';
import HttpMethodBadge from '@/components/HttpMethodBadge';

export interface EndpointProps {
  apiName: string;
  apiInfo: Record<string, GsqlParameter>;
  graphName: string;
  onClick?: () => void;
}

export function Endpoint({ apiName, apiInfo, graphName, onClick }: EndpointProps) {
  const [css, theme] = useStyletron();

  const apiType: ApiType = apiName.split(' ')[0] as ApiType;
  const apiPath = apiName.split(' ')[1];

  const { themeType } = useTheme();

  const backgroundColorMap = {
    [ApiType.GET]: themeType === 'light' ? 'rgba(37, 131, 222, 0.10)' : 'rgba(37, 131, 222, 0.10)',
    [ApiType.POST]: themeType === 'light' ? 'rgba(39, 186, 63, 0.10)' : 'rgba(39, 186, 63, 0.10)',
    [ApiType.PUT]: themeType === 'light' ? 'rgba(248, 173, 104, 0.15)' : 'rgba(248, 173, 104, 0.15)',
    [ApiType.DELETE]: themeType === 'light' ? 'rgba(214, 69, 69, 0.10)' : 'rgba(214, 69, 69, 0.10)',
  };

  const colorMap = {
    [ApiType.GET]: theme.colors['background.informative.subtle'],
    [ApiType.POST]: theme.colors['background.success.bold'],
    [ApiType.PUT]: theme.colors['background.brand.bold'],
    [ApiType.DELETE]: theme.colors['background.danger.subtle'],
  };

  return (
    <div
      className={css({
        backgroundColor: backgroundColorMap[apiType],
        marginBottom: '10px',
        padding: '8px',
        borderRadius: '2px',
        ...expand({
          border: `1px solid ${colorMap[apiType]}`,
        }),
        cursor: 'pointer',
      })}
      onClick={onClick}
    >
      <div
        key={apiName}
        className={css({
          display: 'flex',
          justifyContent: 'space-between',
          height: '24px',
          alignItems: 'center',
        })}
      >
        <div
          className={css({
            display: 'flex',
            alignItems: 'center',
            maxWidth: '95%',
            gap: '8px',
          })}
        >
          <HttpMethodBadge apiType={apiType} />
          <div
            className={css({
              maxWidth: '100%',
              overflow: 'auto',
            })}
          >
            {apiName.split(' ')[1]}
          </div>
        </div>
      </div>
    </div>
  );
}
