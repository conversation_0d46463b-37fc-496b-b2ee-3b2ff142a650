import { FileTabStyleObject } from '@/pages/editor/file/styleObject';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { xcodeLight } from '@uiw/codemirror-theme-xcode';
import ReactCodeMirror from '@uiw/react-codemirror';
import { mergeOverrides } from 'baseui';
import { Tab, Tabs, TabsProps } from 'baseui/tabs-motion';

export function SnippetsTabs({ overrides, ...props }: TabsProps) {
  const [css, theme] = useStyletron();

  return (
    <Tabs
      overrides={mergeOverrides(
        {
          Root: {
            style: {
              height: '100%',
              transform: 'none', // to fix codemirror autocomplete tooltip position, see https://github.com/codemirror/dev/issues/324
            },
          },
          TabHighlight: {
            style: {
              height: '2px',
              backgroundColor: theme.colors['background.primary'],
            },
          },
          TabBorder: {
            style: {
              height: '1px',
              backgroundColor: `${theme.colors.divider}`,
              marginTop: '-1px',
            },
          },
          TabList: {
            style: {
              paddingTop: '4px',
              height: '28px',
              paddingBottom: '0px',
              marginBottom: '0px',
              width: 'calc(100% - 180px)',
              overflowX: 'auto',
            },
          },
        },
        overrides as any
      )}
      {...props}
    />
  );
}

export function SnippetsTab({ name, value }: { name: string; value: string }) {
  const [css, theme] = useStyletron();

  return (
    <Tab key={name} overrides={FileTabStyleObject(theme)} title={name}>
      <div
        className={css({
          height: '100%',
        })}
      >
        <ReactCodeMirror value={value} editable={false} theme={xcodeLight} width={'100%'} height={'200px'} />
      </div>
    </Tab>
  );
}
