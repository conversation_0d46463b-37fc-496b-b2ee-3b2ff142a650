import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import JSONbigint from 'json-bigint';
import { Button } from '@tigergraph/app-ui-lib/button';
import useEditorTheme from '@/pages/editor/useEditorTheme';
import { QueryParam } from '@tigergraph/tools-models';
import { CopyIcon } from '@/pages/home/<USER>';
import ReactCodeMirror from '@uiw/react-codemirror';
import { StreamLanguage } from '@codemirror/language';
import { json } from '@codemirror/legacy-modes/mode/javascript';
import { buildReqBodyFromParamPayload } from '@/utils/queryParam';
import { useMemo } from 'react';

export interface BodyTabProps {
  parameters: QueryParam[];
  payload: Record<any, any>;
  setCopyContent: (content: string) => void;
}

export default function BodyTab({ parameters, payload, setCopyContent }: BodyTabProps) {
  const [css, theme] = useStyletron();
  const editorTheme = useEditorTheme({ background: theme.colors['input.background'] });

  const body = useMemo(() => {
    return JSONbigint.stringify(buildReqBodyFromParamPayload(parameters, payload), null, 2);
  }, [parameters, payload]);

  return (
    <div className="flex flex-col gap-[8px]">
      <div className="flex justify-between items-center">
        <div
          className={css({
            fontSize: '16x',
            lineHeight: '24px',
            color: theme.colors['input.text'],
            fontWeight: 500,
          })}
        >
          Payload
        </div>
        <Button
          size="compact"
          kind="text"
          shape="square"
          onClick={() => {
            setCopyContent(body);
          }}
        >
          <CopyIcon />
        </Button>
      </div>
      <div className={css({ border: `1px solid ${theme.colors['input.border']}`, borderRadius: '2px' })}>
        <ReactCodeMirror
          value={body}
          readOnly
          height="300px"
          extensions={[StreamLanguage.define(json)]}
          theme={editorTheme}
        />
      </div>
    </div>
  );
}
