import { useState } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { GsqlQueryMeta, QueryParam } from '@tigergraph/tools-models';
import { Input } from '@tigergraph/app-ui-lib/input';
import { CopyIcon } from '@/pages/home/<USER>';
import TooltipLabel from '@/components/TooltipLabel';
import SnippetsCopier from '@/pages/editor/query/SnippetsCopier';
import HttpMethodBadge from '@/components/HttpMethodBadge';
import { ApiType } from '@/pages/editor/query/builtinEndpoints/type';
import { CreateToken } from '../CreateToken';
import { WorkspaceT } from '@/pages/workgroup/type';

export interface CodeSnippetTabProps {
  type: 'query' | 'restpp endpoints';
  query?: GsqlQueryMeta;
  method: ApiType;
  url: string;
  parameters: QueryParam[];
  payload: Record<any, any>;
  setCopyContent: (content: string) => void;
  wp: WorkspaceT;
}

export default function CodeSnippetTab({
  type,
  query,
  method,
  url,
  payload,
  parameters,
  setCopyContent,
  wp,
}: CodeSnippetTabProps) {
  const [css, theme] = useStyletron();
  const [isCreateSecretOpen, setIsCreateSecretOpen] = useState(false);
  const [token, setToken] = useState('');

  if (!(type === 'restpp endpoints' || query?.installed)) {
    return (
      <div className={css({ color: theme.colors['text.secondary'] })}>
        Code snippets are only available for installed queries.
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col gap-[12px]">
        <div className="flex items-center gap-[8px] ">
          <HttpMethodBadge apiType={method} />
          <div className={css({ ...theme.typography.Label, color: theme.colors['input.text'], fontWeight: 500 })}>
            Request URL
          </div>
          <div className="flex flex-1 items-center gap-[8px]">
            <div className="flex-1">
              <Input readOnly value={url} />
            </div>
            <Button
              size="compact"
              kind="text"
              shape="square"
              onClick={() => {
                setCopyContent(url);
              }}
            >
              <CopyIcon />
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <TooltipLabel
            label="Datebase Token"
            tooltip="Generate a database token and paste it here so that you can copy the code snippets below to run the query in your own code."
          />

          <Button kind="secondary" size="compact" onClick={() => setIsCreateSecretOpen(true)}>
            Generate Database Token
          </Button>
        </div>

        <Input
          placeholder="Enter your database secret here"
          value={token}
          onChange={(e) => setToken(e.currentTarget.value)}
        />

        <div>
          <TooltipLabel label="Code Snippets" tooltip="" />
          <SnippetsCopier parameters={parameters} payload={payload} url={url} secret={token} method={method} />
        </div>
      </div>
      <CreateToken isOpen={isCreateSecretOpen} onClose={() => setIsCreateSecretOpen(false)} workspace={wp} />
    </>
  );
}
