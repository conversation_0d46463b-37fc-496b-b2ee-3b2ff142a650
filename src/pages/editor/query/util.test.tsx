import { insertOrReplaceToQuery } from '@/pages/editor/file/util';
import { normalizeLintResults, replaceQueryName } from '@/pages/editor/query/util';

test('replaceQueryName', () => {
  const code = 'CREATE QUERY newQuery() {}';
  const newName = 'NewQuery';
  const result = replaceQueryName(code, 'newQuery', newName);
  expect(result).toBe('CREATE QUERY NewQuery() {}');
});

test('insertOrReplaceToQuery', () => {
  const query = 'CREATE QUERY newQuery() {}';
  const result = insertOrReplaceToQuery(query);
  expect(result).toBe('CREATE OR REPLACE QUERY newQuery() {}');
});

test('normalizeLintResults', () => {
  const sampleCode = `CREATE QUERY test() {
    SELECT v FROM Person:v;
    PRINT v;
  }`;

  // Test Cypher query errors
  const cypherError = {
    msg: 'Cypher error',
    errorcode: 1234,
  };
  expect(normalizeLintResults(sampleCode, cypherError, 'CYPHER')).toEqual({
    startLine: 0,
    startColumn: 0,
    endLine: 0,
    endColumn: 0,
    message: 'Cypher error',
  });

  // Test errors with explicit start/end positions
  const explicitPositionError = {
    msg: 'Explicit position error',
    startLine: 2,
    startColumn: 5,
    endLine: 2,
    endColumn: 10,
  };
  expect(normalizeLintResults(sampleCode, explicitPositionError, 'GSQL')).toEqual({
    startLine: 1,
    startColumn: 5,
    endLine: 1,
    endColumn: 10,
    message: 'Explicit position error',
  });

  // Test errors with start/stop indices
  const indexError = {
    msg: 'Index error',
    startindex: 7,
    stopindex: 11,
  };
  expect(normalizeLintResults(sampleCode, indexError, 'GSQL')).toEqual({
    startLine: 0,
    startColumn: 7,
    endLine: 0,
    endColumn: 12,
    message: 'Index error',
  });

  // Test parsing errors with line numbers
  const parsingError = {
    msg: 'Parsing error',
    line: 2,
    charpositioninline: 4,
  };
  expect(normalizeLintResults(sampleCode, parsingError, 'GSQL')).toEqual({
    startLine: 1,
    startColumn: 4,
    endLine: 1,
    endColumn: 10, // Should mark until next whitespace after 'SELECT'
    message: 'Parsing error',
  });

  // Test fallback case with no position information
  const fallbackError = {
    msg: 'Generic error',
  };
  expect(normalizeLintResults(sampleCode, fallbackError, 'GSQL')).toEqual({
    startLine: 0,
    startColumn: 0,
    endLine: 3,
    endColumn: 3,
    message: 'Generic error',
  });
});
