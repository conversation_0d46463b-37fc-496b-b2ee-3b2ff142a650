import { useEffect, useRef, useState, useMemo } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useSchema, useSchemaStyle } from '@/utils/graphEditor/useMetaHook';
import { DBGraphStyleJson, GLOBAL_GRAPH_NAME } from '@tigergraph/tools-models';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import commandExecutor, {
  APICommand,
  Command,
  CommandResult,
  Event,
  GSQLCommand,
  QueryCommand,
} from '@/pages/editor/result/CommandExecutor';
import { Schema } from '@tigergraph/tools-ui/esm/graph/type';
import { useQueryClient } from 'react-query';
import { Result } from '@/lib/type';
import { isStatusActive, WorkGroupT } from '@/pages/workgroup/type';
import ResultChunk from '@/pages/editor/result/ResultChunk';

interface ResultTabProps {
  command: Command;
  onCommandChanged: (cmd: Command) => void;
  onFinished: (cmd: Command, result: CommandResult) => void;
}

export function ResultTab({ command: cmd, onCommandChanged, onFinished }: ResultTabProps) {
  const [css, theme] = useStyletron();
  const { currentGraph, currentWorkspace, setCurrentGraph } = useWorkspaceContext();
  const [schemaCopy, setSchemaCopy] = useState<Schema | undefined>();
  const [schemaStyleCopy, setSchemaStyleCopy] = useState<DBGraphStyleJson | undefined>();
  const [globalSchemaStyleCopy, setGlobalSchemaStyleCopy] = useState<DBGraphStyleJson | undefined>();
  const [graphName] = useState<string>(currentGraph || '');
  const { data: schema } = useSchema(currentGraph, undefined, true);
  const { data: schemaStyle } = useSchemaStyle(currentGraph, undefined);
  const { data: globalSchemaStyle } = useSchemaStyle(GLOBAL_GRAPH_NAME, undefined);
  const [cmdResult, setCmdResult] = useState<CommandResult | null>(null);

  useEffect(() => {
    if (schema && (!schemaCopy || schemaCopy.GraphName === schema?.GraphName)) {
      setSchemaCopy(schema);
      setSchemaStyleCopy(schemaStyle);
      setGlobalSchemaStyleCopy(globalSchemaStyle);
    }
  }, [globalSchemaStyle, schema, schemaCopy, schemaStyle]);

  useEffect(() => {
    if (!currentWorkspace) {
      return;
    }
    const cachedResult = commandExecutor.getCmdResult(cmd.id);
    if (cachedResult) {
      setCmdResult(cachedResult);
    } else if (cmd.type === 'GSQL') {
      // The graph context will change after executing the GSQL code(When it contains use graph statement).
      // We need to update cmd.graph when currentGraph changes. (in most case, the user will first run `use graph`, then run query)
      commandExecutor.executeGSQL(cmd as GSQLCommand, currentWorkspace.nginx_host, currentGraph, (graph) => {
        setCurrentGraph(graph);
        onCommandChanged({
          ...cmd,
          graph,
        });
      });
    } else if (cmd.type === 'API') {
      commandExecutor.executeAPI(cmd as APICommand);
    } else {
      commandExecutor.executeQuery(cmd as QueryCommand);
    }

    const handleError = (event: Event) => {
      if (event.target !== cmd.id) {
        return;
      }
      // @ts-ignore
      setCmdResult({ ...event.payload! });
      onFinished(cmd, event.payload!);
    };
    const handleProgress = (event: Event) => {
      if (event.target !== cmd.id) {
        return;
      }
      // @ts-ignore
      setCmdResult({ ...event.payload! });
    };
    const handleFinish = (event: Event) => {
      if (event.target !== cmd.id) {
        return;
      }
      onFinished(cmd, event.payload!);
    };

    commandExecutor.on('error', handleError);
    commandExecutor.on('progress', handleProgress);
    commandExecutor.on('finish', handleFinish);

    return () => {
      commandExecutor.off('error', handleError);
      commandExecutor.off('progress', handleProgress);
      commandExecutor.off('finish', handleFinish);
    };
  }, [cmd, currentGraph, currentWorkspace, onFinished, setCurrentGraph, onCommandChanged]);

  const textTabRef = useRef<HTMLDivElement>(null);

  const queryClient = useQueryClient();
  const groups = queryClient.getQueryData(['groups']) as Result<WorkGroupT[]>;
  const isWorkspaceActive = !!groups?.Result?.find((g) => {
    return g.workspaces.find((w) => w.workspace_id === cmd.workspace_id && isStatusActive(w.status));
  });

  const resultChunks = useMemo(
    () => {
      const filteredCmdResult = cmdResult?.chunks.filter((chunk) => !!chunk.content || !!chunk.json) || [];
      return filteredCmdResult.map((chunk, idx) => (
        <div
          className={css({
            marginBottom: idx !== filteredCmdResult.length - 1 ? '16px' : '0px',
          })}
          key={chunk.id}
        >
          <ResultChunk
            chunk={chunk}
            profile={cmdResult?.profile}
            cmd={cmd}
            schema={schemaCopy}
            schemaStyle={schemaStyleCopy}
            globalSchemaStyle={globalSchemaStyleCopy}
            graphName={graphName}
            isWorkspaceActive={isWorkspaceActive}
          />
        </div>
      ));
    },
    // css function reference will change every react render
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [cmdResult, cmd, schemaCopy, schemaStyleCopy, globalSchemaStyleCopy, graphName, isWorkspaceActive]
  );

  return <>{resultChunks}</>;
}
