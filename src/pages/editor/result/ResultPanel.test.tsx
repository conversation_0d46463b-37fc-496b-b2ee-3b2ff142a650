// ResultPanel.test.tsx
import { render, screen, fireEvent } from '@/test-utils';
import ResultPanel from '@/pages/editor/result/ResultPanel';
import { vi } from 'vitest';
import { testQuery } from '@/mocks/response/query';

describe.skip('ResultPanel', () => {
  const defaultProps = {
    commandList: [
      {
        id: '1',
        type: 'GSQL' as const,
        GSQLCode: 'SELECT * FROM table',
        workspace_id: '',
        graph: 'graph',
        memoryLimit: 0,
        timeLimit: 0,
      },
      {
        id: '2',
        type: 'Query' as const,
        query: testQuery,
        workspace_id: '',
        graph: 'graph',
        memoryLimit: 0,
        timeLimit: 0,
      },
      {
        id: '3',
        type: 'GSQL' as const,
        GSQLCode: 'SELECT * FROM table2',
        workspace_id: '',
        graph: 'graph',
        memoryLimit: 0,
        timeLimit: 0,
      },
    ],
    setCommandList: vi.fn(),
    height: 500,
    activeCmdId: '1',
    setActiveCmdId: vi.fn(),
    onClickDeleteTab: vi.fn(),
    isCommandRunning: false,
    currentRunningCommand: null,
    handleFinish: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders command tabs correctly', async () => {
    render(<ResultPanel {...defaultProps} />);
    expect(await screen.findByText('SELECT * FROM table')).toBeInTheDocument();
    expect(await screen.findByText('Run query TestQuery')).toBeInTheDocument();
  });

  test('calls setActiveCmdId when switching tabs', async () => {
    render(<ResultPanel {...defaultProps} />);
    fireEvent.click(await screen.findByText('Run query TestQuery'));
    expect(defaultProps.setActiveCmdId).toHaveBeenCalledWith('2');
  });

  test('shows spinner when command is running', async () => {
    render(
      <ResultPanel {...defaultProps} isCommandRunning={true} currentRunningCommand={defaultProps.commandList[0]} />
    );
    expect(await screen.findByTitle('Running')).toBeInTheDocument();
  });

  test('calls onClickDeleteTab when closing a tab', async () => {
    render(<ResultPanel {...defaultProps} />);
    const closeButtons = await screen.findAllByAltText('close');
    fireEvent.click(closeButtons[0]);
    expect(defaultProps.onClickDeleteTab).toHaveBeenCalledWith('1');
  });

  test('navigates to next command', async () => {
    render(<ResultPanel {...defaultProps} />);
    const nextButton = await screen.findByTitle('next-command');
    fireEvent.click(nextButton);
    expect(defaultProps.setActiveCmdId).toHaveBeenCalledWith('2');
  });

  test('navigates to prev command', async () => {
    render(<ResultPanel {...defaultProps} />);
    const prevButton = await screen.findByTitle('prev-command');
    fireEvent.click(prevButton);
    expect(defaultProps.setActiveCmdId).toHaveBeenCalledWith(0);
  });
});
