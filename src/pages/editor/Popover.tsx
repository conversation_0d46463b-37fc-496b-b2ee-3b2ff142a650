import { Popover as BasePopover } from '@tigergraph/app-ui-lib/popover';
import { mergeOverrides } from 'baseui';
import { PopoverOverrides, PopoverProps } from 'baseui/popover';

const popoverOverrides: PopoverOverrides = {
  Body: {
    style: ({ $theme }: { $theme: any }) => ({
      backgroundColor: $theme.colors['background.alternative'],
      color: $theme.colors['dropdown.text'],
      border: `1px solid ${$theme.colors['dropdown.border']}`,
      borderRadius: '2px',
    }),
  },
  Inner: {
    style: ({ $theme }: { $theme: any }) => ({
      backgroundColor: $theme.colors['background.alternative'],
    }),
  },
  Arrow: {
    style: ({ $theme }: { $theme: any }) => ({
      backgroundColor: $theme.colors['background.alternative'],
    }),
  },
};

export default function Popover({ overrides, ...props }: PopoverProps) {
  return <BasePopover overrides={mergeOverrides(popoverOverrides as any, overrides as any)} {...props} />;
}
