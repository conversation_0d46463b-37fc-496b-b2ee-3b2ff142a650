import { axiosCluster } from '@/lib/network';
import { Return } from '@/lib/type';
import { WorkspaceT } from '@/pages/workgroup/type';
import axios from 'axios';
import { useQuery } from 'react-query';

export interface AlgorithmItem {
  name: string;
  algorithmName?: string;
  description?: string;
  schemaConstraints?: string;
  subs?: AlgorithmItem[];
  algorithms?: AlgorithmItem[];
}

export const getGraphAlgos = () => {
  return axiosCluster.get<Return<AlgorithmItem[]>>('/api/graph-algorithm');
};

export const useGraphAlgorithms = (wp: WorkspaceT | undefined, enable: boolean = true) => {
  return useQuery(
    ['algorithms', wp?.workspace_id],
    async () => {
      const resp = await getGraphAlgos();
      const queries = resp.data.results || [];
      return queries;
    },
    {
      enabled: !!wp && enable,
    }
  );
};

interface GitHubSearchItem {
  name: string;
  path: string;
  html_url: string;
  url: string;
  sha: string;
}

interface GitHubSearchResponse {
  total_count: number;
  incomplete_results: boolean;
  items: GitHubSearchItem[];
}

interface GitHubContentResponse {
  name: string;
  path: string;
  sha: string;
  size: number;
  url: string;
  html_url: string;
  git_url: string;
  download_url: string;
  type: string;
  content: string;
  encoding: string;
}

const searchGitHubFiles = async (algorithmName: string): Promise<GitHubSearchItem | null> => {
  try {
    // GitHub search API to find files in the repository
    // Limit search to the specific branch
    const searchQuery = `repo:tigergraph/gsql-graph-algorithms ${algorithmName} in:path extension:gsql path:/algorithms`;
    const searchResponse = await axios.get<GitHubSearchResponse>(
      `https://api.github.com/search/code?q=${encodeURIComponent(searchQuery)}`,
      {
        headers: {
          Accept: 'application/vnd.github.v3+json',
          // Add GitHub token if available
          ...(import.meta.env.VITE_GITHUB_TOKEN && {
            Authorization: `token ${import.meta.env.VITE_GITHUB_TOKEN}`,
          }),
        },
      }
    );

    // Return the first matching file
    return searchResponse.data.items[0] || null;
  } catch (error) {
    console.error('Error searching GitHub files:', error);
    throw error;
  }
};

// Function to fetch file content from GitHub
const fetchGitHubFileContent = async (fileUrl: string): Promise<string> => {
  try {
    const contentResponse = await axios.get<GitHubContentResponse>(fileUrl, {
      headers: {
        Accept: 'application/vnd.github.v3+json',
        // Add GitHub token if available
        ...(import.meta.env.VITE_GITHUB_TOKEN && {
          Authorization: `token ${import.meta.env.VITE_GITHUB_TOKEN}`,
        }),
      },
    });

    // GitHub returns content as base64 encoded
    const content = atob(contentResponse.data.content.replace(/\n/g, ''));
    return content;
  } catch (error) {
    console.error('Error fetching GitHub file content:', error);
    throw error;
  }
};

const fetchAlgorithmCode = async (algorithmName: string): Promise<string> => {
  // Step 1: Search for the algorithm file
  const searchResult = await searchGitHubFiles(algorithmName);

  if (!searchResult) {
    throw new Error(`Algorithm "${algorithmName}" not found in repository`);
  }

  // Step 2: Fetch the file content
  return await fetchGitHubFileContent(searchResult.url);
};

export const useAlgorithmCode = (algorithmName: string) => {
  return useQuery(
    ['algorithmCode', algorithmName],
    async () => {
      return await fetchAlgorithmCode(algorithmName);
    },
    {
      enabled: !!algorithmName,
    }
  );
};
