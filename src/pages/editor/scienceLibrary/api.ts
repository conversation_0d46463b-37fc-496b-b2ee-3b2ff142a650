import { axiosCluster } from '@/lib/network';
import { Return } from '@/lib/type';
import { InstallQueryTemplatesResult } from '@tigergraph/tools-models';

export const installAlgorithm = async (graphName: string, algoName: string) => {
  const url = `/api/graph-algorithm/${graphName}/install`;
  const res = await axiosCluster.post(url, {}, { params: { queryName: algoName } });
  return res.data as Return<InstallQueryTemplatesResult>;
};
