import { useMemo, useState } from 'react';
import { Expandable, ListItem, ListItemLabel } from '@/components/Expandable';
import { ExternalLink, ExternalNode } from '@tigergraph/tools-models';
import { convertSchemaToGraph } from '@tigergraph/tools-ui/esm/graph/data';
import { DatabaseIcon, EdgeIcon, VertexIcon } from '@/pages/home/<USER>';
import { WorkspaceT } from '@/pages/workgroup/type';
import { emptySchema, useSchema } from '@/utils/useSchema';
import { LoadingIndicator } from '@/components/loading-indicator';

export interface SchemaListProps {
  wp: WorkspaceT;
  graphName: string;
  expanded: boolean;
  searchText?: string;
  onSchemaItemSelect: (item: ExternalNode | ExternalLink, graphName: string) => void;
}

export function SchemaList({ wp, graphName, expanded, searchText = '', onSchemaItemSelect }: SchemaListProps) {
  const [localExpanded, setLocalExpanded] = useState(false);

  // Fetch schema when expanded
  const { data: schema = emptySchema, isLoading } = useSchema(wp, graphName, localExpanded);

  const vertices = schema.VertexTypes;
  const edges = schema.EdgeTypes;
  const schemaGraph = useMemo(() => {
    return convertSchemaToGraph(schema);
  }, [schema]);

  // Filter schema items based on search text
  const filteredVertices = vertices.filter(({ Name }) => Name.toLowerCase().includes(searchText.toLowerCase()));
  const filteredEdges = edges.filter(({ Name }) => Name.toLowerCase().includes(searchText.toLowerCase()));

  const handleClickSchemaItem = (name: string) => {
    const item =
      schemaGraph.nodes.find((node) => node.type === name) || schemaGraph.links.find((link) => link.type === name);
    if (item) {
      onSchemaItemSelect(item, schema.GraphName);
    }
  };

  return (
    <Expandable
      label={<ListItemLabel icon={<DatabaseIcon />} label={'Schema'} />}
      defaultExpanded={localExpanded}
      onExpandChange={(isExpanded) => setLocalExpanded(isExpanded)}
    >
      {isLoading ? (
        <LoadingIndicator />
      ) : (
        <>
          {filteredVertices.map(({ Name }) => (
            <ListItem key={Name} onClick={() => handleClickSchemaItem(Name)}>
              <ListItemLabel icon={<VertexIcon />} label={Name} />
            </ListItem>
          ))}
          {filteredEdges.map(({ Name }) => (
            <ListItem key={Name} onClick={() => handleClickSchemaItem(Name)}>
              <ListItemLabel icon={<EdgeIcon />} label={Name} />
            </ListItem>
          ))}
        </>
      )}
    </Expandable>
  );
}
