import { useState } from 'react';
import { Expandable, ListItemLabel } from '@/components/Expandable';
import { SchemaList } from './SchemaList';
import { WorkspaceT } from '@/pages/workgroup/type';
import { ExternalLink, ExternalNode } from '@tigergraph/tools-models';
import QueryList from '@/pages/editor/graphs/QueryList';
import { CreateTempFileFn } from '@/pages/editor/file/hooks';
import { GraphIcon } from '@/pages/home/<USER>';

export interface GraphStoreProps {
  wp: WorkspaceT;
  graphName: string;
  searchText?: string;
  onSchemaItemSelect: (item: ExternalNode | ExternalLink, graphName: string) => void;
  createTempFile: CreateTempFileFn;
}

export default function GraphStore({ wp, graphName, searchText, onSchemaItemSelect, createTempFile }: GraphStoreProps) {
  const [expanded, setExpanded] = useState(false);

  return (
    <>
      <Expandable
        label={<ListItemLabel icon={<GraphIcon />} label={graphName} />}
        defaultExpanded={expanded}
        onExpandChange={(isExpanded) => setExpanded(isExpanded)}
      >
        <SchemaList
          wp={wp}
          graphName={graphName}
          expanded={expanded}
          searchText={searchText}
          onSchemaItemSelect={onSchemaItemSelect}
        />
        <QueryList
          wp={wp}
          graphName={graphName}
          expanded={expanded}
          searchText={searchText}
          createTempFile={createTempFile}
        />
      </Expandable>
    </>
  );
}
