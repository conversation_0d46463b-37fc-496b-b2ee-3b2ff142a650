import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Mo<PERSON>, ModalHeader, ModalBody, ModalFooter } from '@tigergraph/app-ui-lib/modal';
import ConfirmButtons from '@/components/ConfirmButtons';
import { expand } from 'inline-style-expand-shorthand';

export interface DeleteQueryModalProps {
  isOpen: boolean;
  queryName: string;
  onClose: () => void;
  onConfirm: () => void;
}

export default function DeleteQueryModal(props: DeleteQueryModalProps) {
  const [css] = useStyletron();
  const { isOpen, onClose, onConfirm, queryName } = props;

  const handleClose = () => {
    onClose();
  };

  return (
    <div
      className={css({
        display: 'flex',
        width: '100%',
      })}
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          onConfirm();
        }
      }}
    >
      <Modal
        onClose={handleClose}
        isOpen={isOpen}
        overrides={{
          Root: {
            style: {
              zIndex: 2,
            },
          },
        }}
      >
        <ModalHeader
          className={css({
            fontFamily: 'Urbanist',
            fontSize: '16px',
            fontWeight: 600,
            ...expand({
              padding: '0',
            }),
          })}
        >
          Delete Query
        </ModalHeader>
        <ModalBody>
          <div
            className={css({
              marginBottom: '8px',
              fontSize: '14px',
              fontWeight: 400,
            })}
          >
            Are you sure you want to delete query <span className={css({ fontWeight: 600 })}>{queryName}</span> ?
          </div>
        </ModalBody>
        <ModalFooter>
          <ConfirmButtons confirmLabel={'Confirm'} cancelLabel={'Cancel'} onConfirm={onConfirm} onCancel={onClose} />
        </ModalFooter>
      </Modal>
    </div>
  );
}
