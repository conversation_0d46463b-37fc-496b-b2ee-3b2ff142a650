import { codeCheckReq } from '@/pages/editor/query/codeCheck';
import { WorkspaceT } from '@/pages/workgroup/type';
import { constructReqCommonConfig } from '@/utils/utils';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { GsqlQueryMeta } from '@tigergraph/tools-models';
import toast from 'react-hot-toast';

export const lintQueryCode = async (wp: WorkspaceT, graphName: string, query: GsqlQueryMeta) => {
  const codeCheckPromise = codeCheckReq({ code: query.code, graph: graphName }, constructReqCommonConfig(wp));
  toast.promise(
    codeCheckPromise,
    {
      loading: 'Checking code errors...',
      success: 'Code checked successfully',
      error: 'Failed to check code errors',
    },
    {
      success: {
        style: {
          display: 'none',
        },
      },
    }
  );
  const codeCheckRes = await codeCheckPromise;
  if (codeCheckRes.results?.errors.length) {
    showToast({
      kind: 'negative',
      // @ts-ignore
      message: `The query ${query.name} has error: ${codeCheckRes.results.errors[0].msg || ''}. Please fix it first.`,
    });
    return false;
  }

  return true;
};
