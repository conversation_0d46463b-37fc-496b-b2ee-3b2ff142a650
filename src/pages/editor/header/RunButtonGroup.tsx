import { Button } from '@tigergraph/app-ui-lib/button';
import { StatefulPopover } from 'baseui/popover';
import { ChevronDown } from 'baseui/icon';
import { styled, useStyletron } from '@tigergraph/app-ui-lib/Theme';
import RunCmdButton from '@/pages/editor/header/RunCmdButton';
import { expand } from 'inline-style-expand-shorthand';

interface RunButtonGroupProps {
  runAllDisabled: boolean;
  runSelectionDisabled: boolean;
  selectedCode: string;
  onClickRun: () => void;
  onClickRunSelection: () => void;
}

const Keyboard = styled('div', ({ $theme }) => ({
  color: $theme.colors['icon.primary'],
  textAlign: 'center',
  fontFamily: 'Roboto',
  fontSize: '10px',
  fontWeight: 400,
  lineHeight: '20px',
  height: '20px',
  padding: '0 2px',
  border: `1px solid ${$theme.colors.divider}`,
  borderRadius: '2px',
  minWidth: '20px',
}));

export function RunButtonGroup({
  runAllDisabled,
  runSelectionDisabled,
  selectedCode,
  onClickRun,
  onClickRunSelection,
}: RunButtonGroupProps) {
  const [css, theme] = useStyletron();
  const isMac = /Mac|iPad/i.test(navigator.platform);

  return (
    <>
      <RunCmdButton
        size={'compact'}
        disabled={runAllDisabled && runSelectionDisabled}
        onClick={() => {
          if (!runSelectionDisabled) {
            onClickRunSelection();
          } else {
            onClickRun();
          }
        }}
      >
        Execute {isMac ? '⌘ + ↩' : 'ctrl + ↩'}
      </RunCmdButton>
      <StatefulPopover
        triggerType={'click'}
        placement={'bottomRight'}
        overrides={{
          Body: {
            style: {
              borderRadius: '2px',
              border: `1px solid ${theme.colors.divider}`,
              backgroundColor: theme.colors['background.primary'],
            },
          },
          Inner: {
            style: {
              padding: '8px 16px 8px 8px',
              backgroundColor: theme.colors['background.primary'],
            },
          },
        }}
        content={({ close }) => {
          return (
            <button
              className={css({
                textAlign: 'left',
                cursor: !runAllDisabled ? 'pointer' : 'not-allowed',
                ':hover': {
                  backgroundColor: theme.colors['button.background.default.hover'],
                },
              })}
              disabled={runAllDisabled}
              onClick={() => {
                onClickRun();
                close();
              }}
            >
              <div
                className={css({
                  fontSize: '14px',
                  fontWeight: 600,
                })}
              >
                Execute All
              </div>
              <div
                className={css({
                  fontSize: '12px',
                  display: 'flex',
                  gap: '4px',
                  marginTop: '4px',
                  alignItems: 'center',
                })}
              >
                {isMac ? (
                  <>
                    <Keyboard>⌘</Keyboard>
                    <span>+</span>
                    <Keyboard>⇧</Keyboard>
                    <span>+</span>
                    <Keyboard>↩</Keyboard>
                  </>
                ) : (
                  <>
                    <Keyboard>ctrl</Keyboard>
                    <span>+</span>
                    <Keyboard>shift</Keyboard>
                    <span>+</span>
                    <Keyboard>↩</Keyboard>
                  </>
                )}
              </div>
            </button>
          );
        }}
      >
        <Button
          size={'compact'}
          disabled={runAllDisabled}
          overrides={{
            BaseButton: {
              style: {
                borderBottomLeftRadius: '0px',
                borderTopLeftRadius: '0px',
                ...expand({
                  borderLeft: !runAllDisabled ? `1px solid ${theme.colors['border.inverse']}` : '0px',
                }),
                height: '32px',
                width: '32px',
                paddingLeft: '0px',
                paddingRight: '0px',
              },
            },
          }}
        >
          <ChevronDown size={24} />
        </Button>
      </StatefulPopover>
    </>
  );
}
