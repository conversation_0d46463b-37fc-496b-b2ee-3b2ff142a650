import { ReactNode, useCallback, useEffect } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { pageTabsOverrides, Tab, Tabs } from '@/components/Tab';
import { WorkspaceSelector } from '@/pages/editor/WorkspaceSelector';
import { expand } from 'inline-style-expand-shorthand';
import { RunButtonGroup } from '@/pages/editor/header/RunButtonGroup';
import { ActiveFile } from '@/utils/graphEditor/data';
import { Command } from '@/pages/editor/result/CommandExecutor';
import { FaBook } from 'react-icons/fa';
import { WorkspaceT } from '@/pages/workgroup/type';

interface GSQLEditorHeaderProps {
  children: ReactNode;
  isCommandRunning: boolean;
  activeTab: string;
  runCmd: (wp: WorkspaceT, graphName: string, cmd: string | Command) => Command | null;
  currentWorkspace: WorkspaceT | undefined;
  activeFiles: ActiveFile[];
  currentFileId: string;
  selectedCode: string;
  currentGraph: string;
}

export default function GSQLEditorHeader({
  children,
  isCommandRunning,
  activeTab,
  runCmd,
  currentWorkspace,
  activeFiles,
  currentFileId,
  selectedCode,
  currentGraph,
}: GSQLEditorHeaderProps) {
  const [css, theme] = useStyletron();

  const activeFile = activeFiles.find((file) => file.id === currentFileId);

  // code in the code editor
  const code = activeFile?.content || '';

  const handleRunAll = useCallback(() => {
    runCmd(currentWorkspace!, currentGraph, code);
  }, [code, currentGraph, currentWorkspace, runCmd]);

  const handleRunSelection = useCallback(() => {
    runCmd(currentWorkspace!, currentGraph, selectedCode);
  }, [currentGraph, currentWorkspace, runCmd, selectedCode]);

  const runAllDisabled = !code.trim() || isCommandRunning || !currentWorkspace || !currentWorkspace.canAccess;
  const runSelectionDisabled = runAllDisabled || !selectedCode.trim();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const isCommandOrCtrl = event.metaKey || event.ctrlKey;
      if (isCommandOrCtrl && event.shiftKey && event.key === 'Enter') {
        if (!runAllDisabled) {
          handleRunAll();
        }
        return;
      }

      if (isCommandOrCtrl && event.key === 'Enter') {
        if (!runSelectionDisabled) {
          handleRunSelection();
        } else if (!runAllDisabled) {
          handleRunAll();
        }
      }
    };
    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    activeTab,
    currentWorkspace,
    handleRunAll,
    handleRunSelection,
    runAllDisabled,
    runSelectionDisabled,
    selectedCode,
  ]);

  return (
    <Tabs activeKey={'Query Editor'} overrides={pageTabsOverrides}>
      <Tab
        title={
          <div
            className={css({
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
            })}
          >
            <span>Query Editor</span>
            <a
              href="https://github.com/tigergraph/ecosys/blob/master/demos/guru_scripts/docker/tutorial/4.x/README.md"
              target="_blank"
              rel="noopener noreferrer"
              className={css({
                display: 'flex',
                alignItems: 'center',
                height: '24px',
                gap: '8px',
                padding: '0 8px',
                borderRadius: '2px',
                border: `1px solid ${theme.colors['button.border']}`,
                backgroundColor: theme.colors['button.background.default'],
                ':hover': {
                  backgroundColor: theme.colors['button.background.default.hover'],
                },
              })}
            >
              <FaBook size={14} color={theme.colors['icon.primary']} />
              <span
                className={css({
                  fontSize: '12px',
                  fontWeight: 500,
                  color: theme.colors['button.text'],
                })}
              >
                GSQL Tutorial
              </span>
            </a>
            <a
              href="https://github.com/tigergraph/ecosys/blob/master/demos/guru_scripts/docker/tutorial/4.x/Cypher.md"
              target="_blank"
              rel="noopener noreferrer"
              className={css({
                display: 'flex',
                alignItems: 'center',
                height: '24px',
                gap: '8px',
                padding: '0 8px',
                borderRadius: '2px',
                border: `1px solid ${theme.colors['button.border']}`,
                backgroundColor: theme.colors['button.background.default'],
                ':hover': {
                  backgroundColor: theme.colors['button.background.default.hover'],
                },
              })}
            >
              <FaBook size={14} color={theme.colors['icon.primary']} />
              <span
                className={css({
                  fontSize: '12px',
                  fontWeight: 500,
                  color: theme.colors['button.text'],
                })}
              >
                Cypher Tutorial
              </span>
            </a>
          </div>
        }
        key="Query Editor"
        overrides={{
          TabPanel: {
            style: {
              ...expand({
                padding: '0',
              }),
            },
          },
          Tab: {
            style: {
              ':hover': {
                background: 'transparent',
              },
              cursor: 'default',
            },
          },
        }}
      >
        <div
          className={css({
            display: 'flex',
            position: 'absolute',
            right: '0',
            top: '12px',
            paddingRight: '16px',
          })}
        >
          <WorkspaceSelector />
          <RunButtonGroup
            runAllDisabled={runAllDisabled}
            runSelectionDisabled={runSelectionDisabled}
            selectedCode={selectedCode}
            onClickRun={handleRunAll}
            onClickRunSelection={handleRunSelection}
          />
        </div>
        <div
          className={css({
            height: 'calc(100vh - 57px)',
          })}
        >
          {children}
        </div>
      </Tab>
    </Tabs>
  );
}
