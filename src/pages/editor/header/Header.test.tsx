// Header.test.tsx
import { render, screen, fireEvent } from '@/test-utils';
import GSQLEditorHeader from '@/pages/editor/header/Header';
import { vi } from 'vitest';
import { currentWorkspace } from '@/mocks/response/currentWorkspace';
import { queryFile } from '@/mocks/response/queryFile';
import { FilePermission } from '@/utils/graphEditor/data';

describe('GSQLEditorHeader', () => {
  const defaultProps = {
    children: <div>Test Content</div>,
    isCommandRunning: false,
    activeTab: 'files',
    runCmd: vi.fn(),
    selectedQuery: queryFile,
    activeFiles: [
      {
        id: '1',
        content: 'test code',
        name: 'test.gsql',
        is_folder: false,
        permission: FilePermission.Owner,
        parent_id: '',
        files: [],
        type: 'UserFile' as const,
        updated_at: '2024-12-02T05:44:08.451818Z',
        created_at: '2024-12-02T05:44:08.451818Z',
      },
    ],
    currentFileId: '1',
    selectedCode: 'selected code',
    currentWorkspace,
    currentGraph: 'test-graph',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders Query Editor title and tutorial link', async () => {
    render(<GSQLEditorHeader {...defaultProps} />);
    expect(await screen.findByText('Query Editor')).toBeInTheDocument();
    expect(await screen.findByText('GSQL Tutorial')).toBeInTheDocument();
  });

  test('renders run buttons when activeTab is Files', async () => {
    render(<GSQLEditorHeader {...defaultProps} />);
    expect(await screen.findByText('Execute', { exact: false })).toBeInTheDocument();
  });

  test('handles Cmd/Ctrl + Enter keyboard shortcut for run selection', async () => {
    render(<GSQLEditorHeader {...defaultProps} />);

    fireEvent.keyDown(window, {
      key: 'Enter',
      metaKey: true,
      ctrlKey: true,
    });

    expect(defaultProps.runCmd).toHaveBeenCalled();
  });

  test('handles Cmd/Ctrl + Shift + Enter keyboard shortcut for run all', async () => {
    render(<GSQLEditorHeader {...defaultProps} />);

    fireEvent.keyDown(window, {
      key: 'Enter',
      ctrlKey: true,
      shiftKey: true,
      metaKey: true,
    });

    expect(defaultProps.runCmd).toHaveBeenCalled();
  });

  test('disables run buttons when command is running', async () => {
    render(<GSQLEditorHeader {...defaultProps} isCommandRunning={true} />);

    const runButton = await screen.findByText('Execute', { exact: false });

    expect(runButton).toBeDisabled();
  });

  test('disables run buttons when workspace cannot be accessed', async () => {
    render(<GSQLEditorHeader {...defaultProps} currentWorkspace={{ ...currentWorkspace, canAccess: false }} />);

    const runButton = await screen.findByText('Execute', { exact: false });

    expect(runButton).toBeDisabled();
  });
});
