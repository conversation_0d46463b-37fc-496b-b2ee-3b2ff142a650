import './editor.css';
import './index.scss';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ResizeType, useResize } from '@/pages/editor/useResize';
import { FileTabs, FileTabsRef } from '@/pages/editor/file/FileTabs';
import { DefaultFileListWidth, DefaultResultPanelHeight } from '@/utils/graphEditor';
import { HideEditorIcon, HideFileIcon, HideResultIcon, HideSchemaIcon } from '@/pages/home/<USER>';
import { useBlocker, useSearchParams } from 'react-router-dom';
import SchemaDesigner from '@/components/schemaDesigner';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { GraphResultRef } from '@/pages/editor/result/GraphResult';
import { Layer } from 'baseui/layer';
import ResultPanel from '@/pages/editor/result/ResultPanel';
import SchemaDesignerDeactiveDialog from '@/components/schemaDesigner/SchemaDesignerDeactiveDialog';
import { expand } from 'inline-style-expand-shorthand';
import LayoutButtonGroup, { ToggleButtonProp } from '@/pages/editor/LayoutButtonGroup';
import Header from '@/pages/editor/header/Header';
import useSize from 'ahooks/lib/useSize';
// import { Tab, Tabs } from '@/components/Tab';
import { useOrgFiles } from '@/pages/editor/file/hooks';
import { FileList } from '@/pages/editor/file/FileList';
import { Tab, Tabs } from '@tigergraph/app-ui-lib/tab';
import { Command, CommandResult, GSQLCommand } from '@/pages/editor/result/CommandExecutor';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { useSchema, useSchemaStyle } from '@/utils/graphEditor/useMetaHook';
import { UnsavedChangesModal } from '@/pages/editor/UnsavedChangeDialog';
import { FaSave } from 'react-icons/fa';
import IconButton from '@/components/IconButton';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { PLACEMENT, TRIGGER_TYPE } from 'baseui/popover';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { isMac } from '@/utils/os';
import GraphStoreList from '@/pages/editor/graphs/GraphStoreList';
import { ExternalLink, ExternalNode } from '@tigergraph/tools-models';
import { useQueryClient } from 'react-query';
import GraphAlgorithms from '@/pages/editor/scienceLibrary/GraphAlgorithms';

type HandleCreateFileFn = (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;

export default function Editor() {
  const [css, theme] = useStyletron();
  const queryClient = useQueryClient();

  const { refetchSimpleAuth, currentGraph, currentWorkspace, graphNames, setCurrentGraph } = useWorkspaceContext();
  const { refetch: refetchSchema } = useSchema(currentGraph, undefined, false);
  const { refetch: refetchSchemaStyle } = useSchemaStyle(currentGraph);

  const {
    commandList,
    setCommandList,
    activeCmdId,
    setActiveCmdId,
    activeFiles,
    setActiveFiles,
    currentFileId,
    setCurrentFileId,
    folderExpanded,
    onToggleExpand,
    unsavedFiles,
    setSelectedQueryId,
    selectedQueryId,
    hideSchema,
    setHideSchema,
    isCommandRunning,
    setIsCommandRunning,
    currentRunningCommand,
    setCurrentRunningCommand,
    memoryLimit,
    timeLimit,
    selectedCode,
    runCmd,
  } = useEditorContext();

  const [searchParams, setSearchParams] = useSearchParams();
  const activeTab = searchParams.get('tab') || 'graphs';
  const handleActiveTabChange = useCallback(
    ({ activeKey }: { activeKey: React.Key }) => {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('tab', `${activeKey}`);
      setSearchParams(newSearchParams, {
        replace: true,
      });
    },
    [setSearchParams, searchParams]
  );

  const {
    files,
    fetchingFiles,
    newFileId,
    setNewFileId,
    handleSelectFile,
    onChangeFile,
    updateFileStore,
    handleCreateFile,
    createTempFile,
    refetch: refresh,
  } = useOrgFiles();

  const resultContainerRef = useRef<HTMLDivElement>(null);
  const leftSidebarRef = useRef<HTMLDivElement>(null);
  const [hideEditor, setHideEditor] = useState(false);
  const [hideResultPanel, setHideResultPanel] = useState(false);
  const [hideFileList, setHideFileList] = useState(false);

  const maxHeight = window.innerHeight - 56;
  const {
    initResize: initResultResize,
    size: resultSize,
    setSize: setResultSize,
  } = useResize({ ref: resultContainerRef, type: ResizeType.Vertical });
  const schemaDesignerContainerRef = useRef<HTMLDivElement>(null);
  const editorContainerRef = useRef<HTMLDivElement>(null);
  const editorSize = useSize(editorContainerRef);

  const {
    initResize: initFileListResize,
    size: fileListSize,
    setSize: setFileListSize,
  } = useResize({ ref: leftSidebarRef, type: ResizeType.Horizontal });

  const DefaultSchemaDesignerWidth = window.innerWidth * 0.33;
  const {
    initResize: initDesignerResize,
    size: designerSize,
    setSize: setDesignerSize,
  } = useResize({
    ref: schemaDesignerContainerRef,
    type: ResizeType.HorizontalReverse,
    minWidth: 300,
    initWidth: hideSchema ? 0 : DefaultSchemaDesignerWidth,
  });

  const noAccessToWorkspace = !currentWorkspace?.canAccess;
  // when no access to workspace, just hide schema editor by set size to 0
  useEffect(() => {
    if (noAccessToWorkspace) {
      setDesignerSize({
        x: 0,
      });
    }
  }, [noAccessToWorkspace, setDesignerSize]);

  useEffect(() => {
    setHideResultPanel(resultSize.y === 0);
    if (resultSize.y !== maxHeight && resultSize.y && hideFileList && hideEditor) {
      setHideFileList(false);
      setHideEditor(false);
    }
  }, [hideEditor, hideFileList, maxHeight, resultSize]);

  useEffect(() => {
    setHideFileList(fileListSize.x === 0 || resultSize.y! >= maxHeight || editorSize?.width === 0);
    setHideEditor(editorSize?.width! <= fileListSize.x! || resultSize.y! >= maxHeight || editorSize?.width === 0);
  }, [editorSize?.width, fileListSize, maxHeight, resultSize.y]);

  useEffect(() => {
    setHideSchema(designerSize.x === 0);
  }, [designerSize, setHideSchema]);

  const [showSchemaChangeModal, setShowSchemaChangeModal] = useState(false);

  const graphResultRef = useRef<GraphResultRef>(null);

  const handleClickDeleteTab = (id: string) => {
    if (commandList.length === 1) {
      setActiveCmdId(0);
    } else if (activeCmdId === id) {
      const index = commandList.findIndex((item) => item.id === id);
      const commandListLength = commandList.length;
      if (index === commandListLength - 1) {
        setActiveCmdId(commandList[index - 1].id);
      } else {
        setActiveCmdId(commandList[index + 1].id);
      }
    }
    setCommandList((prev) => {
      return prev.filter((item) => item.id !== id);
    });
  };

  const fileTabsRef = useRef<FileTabsRef>(null);

  const handleCmdFinish = useCallback(
    (cmd: Command, result: CommandResult) => {
      if (cmd.id !== currentRunningCommand?.id) {
        return;
      }

      setIsCommandRunning(false);
      setCurrentRunningCommand(null);

      if (result.error) {
        return;
      }

      if (cmd.type === 'GSQL') {
        const _cmd = cmd as GSQLCommand;
        if (
          /(install query [-\d\w,]+)|(drop query [\d\w,]+)|(create\s+(or\s+replace\s+)?(batch\s+)?(distributed\s+)?query)/i.test(
            _cmd.GSQLCode
          )
        ) {
          queryClient.invalidateQueries(['queries', currentRunningCommand!.workspace_id, cmd.graph]);
        }

        if (/(drop\s+all)|(create\s+graph)|(drop\s+graph)|(import\s+graph)/i.test(_cmd.GSQLCode)) {
          refetchSimpleAuth();
        }

        if (
          /(create\s+vertex)|(create\s+edge)|(drop\s+vertex)|(drop\s+edge)|(add\s+vertex)|(add\s+edge)|(alter\s+vertex)|(alter\s+edge)|(run\s+schema_change\s+job)|(run\s+global\s+schema_change\s+job)/i.test(
            _cmd.GSQLCode
          )
        ) {
          refetchSchema();
          refetchSchemaStyle();
        }
      }
    },
    [
      currentRunningCommand,
      setIsCommandRunning,
      setCurrentRunningCommand,
      queryClient,
      refetchSimpleAuth,
      refetchSchema,
      refetchSchemaStyle,
    ]
  );

  const handleEditQuery = useCallback(
    (name: string, content: string, graphName: string) => {
      const activeFile = activeFiles.find((f) => f.is_temp && f.query_name === name && f.graphName === graphName);
      if (activeFile) {
        setCurrentFileId(activeFile.id);
      } else {
        createTempFile(true, name, content, graphName);
      }
    },
    [activeFiles, createTempFile, setCurrentFileId]
  );

  const handleCloseSchemaDesigner = () => {
    if (graphResultRef.current?.isSchemaChanged() && currentWorkspace?.is_rw) {
      setShowSchemaChangeModal(true);
    } else {
      setDesignerSize({ x: 0 });
      if (hideEditor && editorSize && designerSize.x) {
        setFileListSize({ x: editorSize.width + designerSize.x });
      }
    }
  };

  const confirmCloseSchemaDesigner = () => {
    setDesignerSize({ x: 0 });
    setShowSchemaChangeModal(false);
  };

  const hasUnsavedSchema = useCallback(
    () => !!graphResultRef.current?.isSchemaChanged() && !!currentWorkspace?.is_rw,
    [currentWorkspace?.is_rw, graphResultRef]
  );
  const blocker = useBlocker(
    ({ currentLocation, nextLocation }) =>
      (!!unsavedFiles.length || hasUnsavedSchema()) && currentLocation.pathname !== nextLocation.pathname
  );

  const [isSavingAll, setIsSavingAll] = useState(false);
  const saveAllFiles = useCallback(async () => {
    if (isSavingAll) {
      return;
    }

    setIsSavingAll(true);
    try {
      await fileTabsRef.current?.saveAllFiles();
      showToast({ kind: 'positive', message: 'All files saved successfully' });
    } finally {
      setIsSavingAll(false);
    }
  }, [fileTabsRef, isSavingAll]);

  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const afterConfirm = useRef<(() => void) | null>(null);
  useEffect(() => {
    const handleCheckUnsavedFiles = (event: CustomEvent) => {
      if (!!unsavedFiles.length || hasUnsavedSchema()) {
        setShowConfirmDialog(true);
        afterConfirm.current = event.detail.onConfirm;
      } else {
        event.detail.onConfirm();
      }
    };

    window.addEventListener('check-unsaved-files' as any, handleCheckUnsavedFiles);
    return () => {
      window.removeEventListener('check-unsaved-files' as any, handleCheckUnsavedFiles);
    };
  }, [currentWorkspace?.is_rw, isCommandRunning, unsavedFiles.length, hasUnsavedSchema]);

  const layoutButtonProps: ToggleButtonProp[] = [
    {
      label: 'file list',
      opened: !hideFileList,
      tooltipText: `${hideFileList ? 'Show' : 'Hide'} File List Panel`,
      icon: <HideFileIcon />,
      onClick: () => {
        // setHideFileList((prev) => !prev);
        if (hideFileList) {
          if (hideEditor && editorSize) {
            const x = editorSize.width || DefaultFileListWidth;
            setFileListSize({ x: x });
            if (editorSize.width! === 0) {
              setDesignerSize({ x: designerSize.x! - x });
            }
          } else {
            setFileListSize({ x: DefaultFileListWidth });
            if (editorSize?.width! <= DefaultFileListWidth) {
              setDesignerSize({ x: designerSize.x! - DefaultFileListWidth });
            }
          }
          if (resultSize.y === maxHeight) {
            setResultSize({ y: DefaultResultPanelHeight });
          }
        } else {
          if (!hideEditor) {
            setFileListSize({ x: 0 });
          }
          if (hideEditor && !hideResultPanel) {
            setResultSize({ y: maxHeight });
          }
          if (hideEditor && hideResultPanel) {
            setDesignerSize({ x: designerSize.x! + editorSize!.width! });
          }
        }
      },
      styleOverride: {
        ...expand({}),
      },
    },
    {
      label: 'editor',
      opened: !hideEditor,
      tooltipText: `${hideResultPanel ? 'Show' : 'Hide'} Editor Panel`,
      icon: <HideEditorIcon />,
      onClick: () => {
        // setHideEditor((prev) => !prev);
        if (hideEditor) {
          if (hideFileList) {
            setFileListSize({ x: 0 });
            if (editorSize?.width === 0) {
              setDesignerSize({ x: designerSize.x! - DefaultFileListWidth });
            }
          } else {
            setFileListSize({ x: DefaultFileListWidth });
            if (editorSize?.width! <= DefaultFileListWidth) {
              setDesignerSize({ x: designerSize.x! - DefaultFileListWidth });
            }
          }
          if (resultSize.y === maxHeight) {
            setResultSize({ y: DefaultResultPanelHeight });
          }
        } else {
          if (!hideFileList && editorSize) {
            setFileListSize({ x: editorSize.width });
          }
          if (hideFileList && !hideResultPanel) {
            setResultSize({ y: maxHeight });
          }
          if (hideFileList && hideResultPanel) {
            setDesignerSize({ x: designerSize.x! + editorSize!.width! });
          }
        }
      },
      styleOverride: {
        ...expand({}),
      },
    },
    {
      label: 'schema',
      opened: !hideSchema,
      tooltipText: `${hideSchema ? 'Show' : 'Hide'} Schema Panel`,
      icon: <HideSchemaIcon />,
      onClick: () => {
        if (hideSchema) {
          setDesignerSize({ x: DefaultSchemaDesignerWidth });
        } else {
          handleCloseSchemaDesigner();
        }
      },
      styleOverride: {
        ...expand({}),
      },
      disabled: noAccessToWorkspace,
    },
    {
      label: 'result',
      opened: !hideResultPanel,
      tooltipText: `${hideResultPanel ? 'Show' : 'Hide'} Message Logs`,
      icon: <HideResultIcon />,
      onClick: () => {
        setHideResultPanel((prev) => !prev);
        if (hideResultPanel) {
          if (hideFileList && hideEditor) {
            setResultSize({ y: maxHeight });
          } else {
            setResultSize({ y: DefaultResultPanelHeight });
          }
          if (editorSize?.width === 0) {
            setDesignerSize({ x: designerSize.x! - DefaultFileListWidth });
          }
        } else {
          setResultSize({ y: 0 });
          if (hideFileList && hideEditor) {
            setDesignerSize({ x: designerSize.x! + editorSize!.width! });
          }
        }
      },
      styleOverride: {
        ...expand({}),
      },
    },
  ];

  const onSchemaItemSelect = useCallback(
    (item: ExternalNode | ExternalLink, graphName: string) => {
      if (hideSchema) {
        setDesignerSize({ x: DefaultSchemaDesignerWidth });
      }
      if (currentGraph !== graphName) {
        setCurrentGraph(graphName);
      }
      setTimeout(
        () => {
          graphResultRef.current?.selectGraphElement(item);
        },
        currentGraph === graphName && !hideSchema ? 0 : 2000
      );
    },
    [hideSchema, currentGraph, setDesignerSize, DefaultSchemaDesignerWidth, setCurrentGraph]
  );

  return (
    <Header
      isCommandRunning={isCommandRunning}
      runCmd={runCmd}
      activeTab={activeTab}
      currentWorkspace={currentWorkspace}
      activeFiles={activeFiles}
      currentFileId={currentFileId}
      currentGraph={currentGraph}
      selectedCode={selectedCode}
    >
      <div
        className={css({
          display: 'flex',
          height: '100%',
          width: '100%',
          overflow: 'hidden',
          position: 'relative',
        })}
      >
        <div
          className={css({
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            zIndex: 0,
            position: 'relative',
            width: `calc(100% - ${designerSize.x!}px)`,
          })}
        >
          <div
            ref={editorContainerRef}
            className={css({
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'relative',
              height: `calc(100% - ${resultSize.y!}px)`,
              width: '100%',
            })}
          >
            <div
              ref={leftSidebarRef}
              className={css({
                height: `100%`,
                width: `${fileListSize.x}px`,
                backgroundColor: `${theme.colors['background.tertiary.a']}`,
                maxWidth: '100%',
                position: 'relative',
              })}
            >
              <Tabs
                activeKey={activeTab}
                onChange={handleActiveTabChange}
                overrides={{
                  TabList: {
                    style: {
                      borderRight: `1px solid ${theme.colors['border.tertiary']}`,
                    },
                  },
                  Root: {
                    style: {
                      height: '100%',
                    },
                  },
                }}
              >
                {noAccessToWorkspace ? null : (
                  <Tab
                    title="Graphs"
                    key="graphs"
                    overrides={{
                      TabPanel: {
                        style: {
                          ...expand({ padding: '0' }),
                          height: 'calc(100% - 45px)',
                          overflowY: 'auto',
                        },
                      },
                    }}
                  >
                    <GraphStoreList
                      graphNames={graphNames}
                      currentWorkspace={currentWorkspace!}
                      onSchemaItemSelect={onSchemaItemSelect}
                      createTempFile={createTempFile}
                    />
                    <GraphAlgorithms createTempFile={createTempFile} />
                  </Tab>
                )}
                <Tab
                  title={
                    <div className={[css({ display: 'flex', alignItems: 'center' }), 'file-tabs-title'].join(' ')}>
                      <span className={css({ marginRight: '4px' })}>Files</span>
                      {!!unsavedFiles.length &&
                        (!isSavingAll ? (
                          <StatefulPopover
                            triggerType={TRIGGER_TYPE.hover}
                            content={
                              <>
                                Save All
                                <span className={css({ marginLeft: '12px' })}>
                                  {isMac() ? '⌘ + ⇧ + S' : 'ctrl + shift + S'}
                                </span>
                              </>
                            }
                            ignoreBoundary={true}
                            placement={PLACEMENT.bottom}
                          >
                            <IconButton onClick={saveAllFiles} $style={{ height: '14px' }}>
                              <FaSave size={14} color={theme.colors['icon.primary']} />
                            </IconButton>
                          </StatefulPopover>
                        ) : (
                          <Spinner $color={theme.colors.gray1000} $size={'14px'} $borderWidth={'2px'} />
                        ))}
                    </div>
                  }
                  key="files"
                  overrides={{
                    TabPanel: {
                      style: {
                        ...expand({ padding: '0' }),
                        height: 'calc(100% - 45px)',
                      },
                    },
                  }}
                >
                  <FileList
                    isFetching={fetchingFiles}
                    files={files}
                    activeFiles={activeFiles}
                    folderExpanded={folderExpanded}
                    currentFileId={currentFileId}
                    newFileId={newFileId}
                    setNewFileId={setNewFileId}
                    onSelectFile={handleSelectFile}
                    onChangeFile={onChangeFile}
                    onRefresh={refresh}
                    onCreateFile={handleCreateFile}
                    onToggleExpand={onToggleExpand}
                  />
                </Tab>
              </Tabs>
              <div className={'drag-line-container vertical'} onMouseDown={initFileListResize}>
                <div className={'line'}></div>
              </div>
            </div>
            <div
              className={css({
                flex: 1,
                minWidth: 0,
                visibility: hideEditor ? 'hidden' : 'visible',
                height: '100%',
              })}
            >
              <FileTabs
                ref={fileTabsRef}
                onChangeFile={updateFileStore}
                onCreateFile={handleCreateFile}
                onSaveAll={saveAllFiles}
                updateFileStore={updateFileStore}
              />
            </div>
          </div>
          <div
            ref={resultContainerRef}
            className={css({
              position: 'relative',
              overflow: 'auto',
              maxHeight: '100%',
            })}
          >
            <div className={'drag-line-container horizontal'} onMouseDown={initResultResize}>
              <div className={'line'}></div>
            </div>
            <ResultPanel
              isCommandRunning={isCommandRunning}
              currentRunningCommand={currentRunningCommand}
              commandList={commandList}
              setCommandList={setCommandList}
              activeCmdId={activeCmdId}
              height={resultSize.y!}
              setActiveCmdId={setActiveCmdId}
              onClickDeleteTab={handleClickDeleteTab}
              handleFinish={handleCmdFinish}
            />
          </div>
        </div>
        <div
          ref={schemaDesignerContainerRef}
          className={css({
            borderLeft: `1px solid ${theme.colors.divider}`,
            height: '100%',
            width: `${designerSize.x!}px`,
            flex: 1,
            display: 'flex',
            position: 'relative',
            overflow: 'hidden',
          })}
        >
          <div className={'drag-line-container vertical-right'} onMouseDown={initDesignerResize}>
            <div className={'line'}></div>
          </div>
          <SchemaDesigner
            ref={graphResultRef}
            width="100%"
            height="100%"
            enableHeader={false}
            backgroundColor={theme.colors['background.secondary']}
            enableFullScreen={false}
            showHideButton={false}
            showCreateEdgeTooltip={!hideSchema && currentWorkspace?.is_rw}
            hideSchema={hideSchema}
          />
          <SchemaDesignerDeactiveDialog
            graphResultRef={graphResultRef}
            showPrompt={!hideSchema && !!currentWorkspace?.is_rw}
            showSchemaChangeModal={showSchemaChangeModal}
            onConfirmCloseSchemaDesigner={confirmCloseSchemaDesigner}
            onCancelCloseSchemaDesigner={() => setShowSchemaChangeModal(false)}
          />
        </div>
      </div>
      <Layer>
        <div
          className={css({
            position: 'absolute',
            right: '0px',
            bottom: '50%',
            transform: 'translateY(50%)',
            display: 'flex',
            flexDirection: 'column',

            borderRadius: '4px 0px 0px 4px',
            border: `1px solid ${theme.colors['border.tertiary']}`,
            background: `${theme.colors.backgroundPrimary}`,
            padding: '8px',
            boxShadow: '-2px -2px 10px 0px rgba(0, 0, 0, 0.05), 2px 2px 10px 0px rgba(0, 0, 0, 0.05)',
          })}
        >
          <LayoutButtonGroup buttonProps={layoutButtonProps} />
        </div>
      </Layer>
      <UnsavedChangesModal
        isOpen={blocker.state === 'blocked' || showConfirmDialog}
        unsavedFiles={unsavedFiles}
        hasUnsavedSchema={hasUnsavedSchema()}
        onDiscard={() => {
          if (blocker.state === 'blocked') {
            blocker.proceed!();
            return;
          }

          setShowConfirmDialog(false);
          afterConfirm.current?.();
        }}
        onSaveAll={async () => {
          if (unsavedFiles.length) {
            await saveAllFiles();
          }
          if (hasUnsavedSchema()) {
            await graphResultRef.current?.handleSaveSchema();
          }

          if (blocker.state === 'blocked') {
            blocker.proceed!();
            return;
          }

          setShowConfirmDialog(false);
          afterConfirm.current?.();
        }}
        onCancel={() => {
          if (blocker.state === 'blocked') {
            blocker.reset!();
            return;
          }
          if (showConfirmDialog) {
            setShowConfirmDialog(false);
            afterConfirm.current = null;
          }
        }}
      />
    </Header>
  );
}
