import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import React, { ReactNode, useState, forwardRef, ForwardedRef } from 'react';
import { BiChevronRight } from 'react-icons/bi';

export interface ExpandableProps {
  /**
   * The label content to display next to the expand/collapse icon
   */
  label: ReactNode;

  /**
   * The content to show when expanded
   */
  children: ReactNode;

  /**
   * Whether the component is initially expanded
   */
  defaultExpanded?: boolean;

  /**
   * Optional className for the container
   */
  className?: string;

  /**
   * Optional styles for the container
   */
  style?: React.CSSProperties;

  /**
   * Optional callback when expand state changes
   */
  onExpandChange?: (expanded: boolean) => void;

  /**
   * Optional icon size
   */
  iconSize?: number;

  /**
   * Optional padding for the header
   */
  headerPadding?: string;

  /**
   * Optional padding for the content
   */
  contentPadding?: string;
}

export function Expandable({
  label,
  children,
  defaultExpanded = false,
  className = '',
  style = {},
  onExpandChange,
  iconSize = 16,
  contentPadding = '20px',
}: ExpandableProps) {
  const [css, theme] = useStyletron();
  const [expanded, setExpanded] = useState(defaultExpanded);

  const handleToggle = () => {
    const newExpanded = !expanded;
    setExpanded(newExpanded);
    if (onExpandChange) {
      onExpandChange(newExpanded);
    }
  };

  return (
    <div className={className} style={style}>
      <div
        className={css({
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          padding: '4px 8px',
          ':hover': {
            backgroundColor: theme.colors['list.background.hover'],
          },
          borderRadius: '4px',
          ...theme.typography['Label'],
        })}
        onClick={handleToggle}
        role="button"
        aria-expanded={expanded}
      >
        <BiChevronRight
          size={iconSize}
          className={css({
            transform: expanded ? 'rotate(90deg)' : 'none',
            transition: 'transform 0.2s',
            flexShrink: 0,
            color: theme.colors['icon.primary'],
            marginRight: '4px',
          })}
        />
        {label}
      </div>

      {expanded && <div className={css({ paddingLeft: contentPadding })}>{children}</div>}
    </div>
  );
}

export interface ListItemProps {
  /**
   * The content of the list item
   */
  children: ReactNode;

  /**
   * Optional click handler
   */
  onClick?: () => void;

  /**
   * Optional className
   */
  className?: string;

  /**
   * Optional style
   */
  style?: React.CSSProperties;
}

export const ListItem = forwardRef<HTMLDivElement, ListItemProps>(
  ({ children, className, style = {}, ...props }: ListItemProps, ref: ForwardedRef<HTMLDivElement>) => {
    const [css, theme] = useStyletron();
    return (
      <div
        ref={ref}
        className={`${css({
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          padding: '4px 8px',
          gap: '8px',
          ':hover': {
            backgroundColor: theme.colors['list.background.hover'],
          },
          borderRadius: '4px',
          color: theme.colors['text.primary'],
          ...theme.typography['Label'],
        })} ${className || ''}`}
        style={style}
        {...props}
      >
        {children}
      </div>
    );
  }
);

ListItem.displayName = 'ListItem';

export const ListItemLabel = ({ icon, label }: { icon?: ReactNode; label: string }) => {
  const [css, theme] = useStyletron();
  return (
    <div className={css({ display: 'flex', alignItems: 'center', gap: '8px', width: '100%' })}>
      {icon && <div className="min-w-[16px]">{icon}</div>}
      <span
        className={css({
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          lineHeight: '16px',
          color: theme.colors['text.primary'],
          fontWeight: 400,
          padding: '2px 0',
        })}
      >
        {label}
      </span>
    </div>
  );
};
