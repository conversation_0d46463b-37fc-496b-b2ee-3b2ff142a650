import { columnOverrides } from '@/components/table';
import { TableContainer } from '@/lib/styled';
import { InlineBlock } from '@/pages/admin/user/styled';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Pagination } from '@tigergraph/app-ui-lib/pagination';
import { TableBuilder } from '@tigergraph/app-ui-lib/table';
import { Body1 } from '@tigergraph/app-ui-lib/typography';
import { TableBuilderColumn } from 'baseui/table-semantic';
import { format } from 'date-fns';
import { ArrowUpIcon, ArrowDownIcon, FilterIcon } from 'lucide-react';
import React, { useState } from 'react';
import { parseDate } from '@/lib/date';

import { LoadingIndicator } from '@/components/loading-indicator';
import { ErrorDisplay } from '@/components/error';
import { FilterBuilder } from '@/admin/components/FilterBuilder';
import { OrgDetail } from '@/admin/components/OrgDetail';
import { AdminOrg, AdminOrgSort, FilterCondition, useAdminOrgs } from '@/admin/hooks/useAdminOrgs';
import IconButton from '@/components/IconButton';

const PAGE_SIZE = 20;

type SortDirection = 'asc' | 'desc';

interface SortState {
  field: AdminOrgSort;
  direction: SortDirection;
}

export default function OrganizationList() {
  const [css, theme] = useStyletron();
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [sort, setSort] = useState<SortState>({ field: 'created_at', direction: 'desc' });
  const [selectedOrg, setSelectedOrg] = useState<AdminOrg>();

  const { data, isLoading, error } = useAdminOrgs({
    filters: filters.filter((f) => f.value !== ''),
    page,
    pageSize: PAGE_SIZE,
    sort: sort.field,
    sortDirection: sort.direction,
  });

  const handleSort = (field: AdminOrgSort) => {
    setSort((prev) => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  const renderSortHeader = (label: string, field: AdminOrgSort) => {
    return (
      <div className="flex items-center gap-1 cursor-pointer select-none" onClick={() => handleSort(field)}>
        {label}
        {sort.field === field && (sort.direction === 'asc' ? <ArrowUpIcon size={14} /> : <ArrowDownIcon size={14} />)}
      </div>
    );
  };

  const totalPages = data?.total ? Math.ceil(data.total / PAGE_SIZE) : 0;

  return (
    <div className="space-y-6">
      {error ? <ErrorDisplay error={error as Error} /> : null}

      <FilterBuilder
        filters={filters}
        onChange={(newFilters) => {
          setFilters(newFilters);
          setPage(1);
        }}
        hideIncludeDeleted
        type="org"
      />

      <TableContainer $style={{ height: 'auto' }}>
        <TableBuilder
          data={data?.organizations || []}
          overrides={{
            TableBodyRow: {
              style: ({ $rowIndex, $row }) => {
                // Highlight if delinquent_since is not empty
                if ($row?.delinquent_since) {
                  return {
                    backgroundColor: '#fff7e6', // light orange/yellow
                  };
                }
                return {};
              },
            },
          }}
        >
          <TableBuilderColumn header={renderSortHeader('Organization ID', 'org_id')}>
            {(row: AdminOrg) => (
              <div className="text-blue-600 cursor-pointer hover:underline" onClick={() => setSelectedOrg(row)}>
                {row.org_id}
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header={renderSortHeader('Organization Name', 'org_name')}>
            {(row: AdminOrg) => (
              <div className="group flex items-center gap-1">
                <span>{row.org_name}</span>
                <IconButton
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    setFilters([{ field: 'org_name', operator: 'eq', value: row.org_name }]);
                    setPage(1);
                  }}
                >
                  <FilterIcon size={14} />
                </IconButton>
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header={renderSortHeader('Display Name', 'org_display_name')}>
            {(row: AdminOrg) => (
              <div className="group flex items-center gap-1">
                <span>{row.org_display_name}</span>
                <IconButton
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    setFilters([{ field: 'org_display_name', operator: 'eq', value: row.org_display_name }]);
                    setPage(1);
                  }}
                >
                  <FilterIcon size={14} />
                </IconButton>
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header={renderSortHeader('Logo', 'logo_url')} overrides={columnOverrides}>
            {(row: AdminOrg) => (
              <div className="group flex items-center gap-2">
                {row.logo_url ? (
                  <div className="flex items-center gap-2">
                    <img
                      src={row.logo_url}
                      alt={`${row.org_name} logo`}
                      className="w-8 h-8 object-contain border border-gray-200 rounded"
                    />
                    <IconButton
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e: React.MouseEvent) => {
                        e.stopPropagation();
                        setFilters([{ field: 'logo_url', operator: 'eq', value: row.logo_url }]);
                        setPage(1);
                      }}
                    >
                      <FilterIcon size={14} />
                    </IconButton>
                  </div>
                ) : (
                  <span>-</span>
                )}
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header={renderSortHeader('Creator', 'creator')}>
            {(row: AdminOrg) => (
              <div className="group flex items-center gap-1">
                <span>{row.creator}</span>
                <IconButton
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    setFilters([{ field: 'creator', operator: 'eq', value: row.creator }]);
                    setPage(1);
                  }}
                >
                  <FilterIcon size={14} />
                </IconButton>
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header={renderSortHeader('Created At', 'created_at')} overrides={columnOverrides}>
            {(row: AdminOrg) => (
              <div className="group flex items-center gap-1">
                <span>{format(parseDate(row.create_time), 'yyyy-MM-dd HH:mm:ss')}</span>
                <IconButton
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    setFilters([
                      {
                        field: 'created_at',
                        operator: 'gte',
                        value: new Date(row.create_time).toISOString(),
                      },
                    ]);
                    setPage(1);
                  }}
                >
                  <FilterIcon size={14} />
                </IconButton>
              </div>
            )}
          </TableBuilderColumn>

          <TableBuilderColumn header="Delinquent Since">
            {(row: AdminOrg) => (
              <span>{row.delinquent_since ? format(parseDate(row.delinquent_since), 'yyyy-MM-dd HH:mm:ss') : '-'}</span>
            )}
          </TableBuilderColumn>
        </TableBuilder>

        {isLoading && <LoadingIndicator />}

        <InlineBlock
          className={css({
            alignItems: 'center',
            justifyContent: 'flex-end',
            gap: '16px',
            marginTop: '24px',
          })}
        >
          <Body1 color={theme.colors.gray800}>
            {`${(page - 1) * PAGE_SIZE + 1} - ${Math.min(page * PAGE_SIZE, data?.total || 0)} of ${
              data?.total || 0
            } items`}
          </Body1>
          <Pagination totalPage={totalPages} pageNumber={page} setPageNumber={setPage} />
        </InlineBlock>
      </TableContainer>

      {selectedOrg && <OrgDetail org={selectedOrg} isOpen={!!selectedOrg} onClose={() => setSelectedOrg(undefined)} />}
    </div>
  );
}
