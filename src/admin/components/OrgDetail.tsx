import { Modal } from '@tigergraph/app-ui-lib/modal';
import { AdminOrg } from '@/admin/hooks/useAdminOrgs';
import { format } from 'date-fns';
import { parseDate } from '@/lib/date';
import { useAdminOrgDetail } from '@/admin/hooks/useAdminOrgDetail';
import { LoadingIndicator } from '@/components/loading-indicator';
import { ErrorDisplay } from '@/components/error';
import { useState } from 'react';
import EditOrgDrawer from '@/admin/pages/org/editDrawer';
import { Button } from '@tigergraph/app-ui-lib/button';
import { MdEdit } from 'react-icons/md';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { DatabaseList } from './DatabaseList';
import { WorkgroupList } from './WorkgroupList';
import { UserList } from './UserList';
import { WorkspaceList } from './WorkspaceList';

interface OrgDetailProps {
  org: AdminOrg;
  isOpen: boolean;
  onClose: () => void;
}

export function OrgDetail({ org, isOpen, onClose }: OrgDetailProps) {
  const {
    data: detail,
    isLoading,
    error,
  } = useAdminOrgDetail({
    orgID: org.org_id,
    enabled: isOpen,
  });
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);
  const [isDatabaseListOpen, setIsDatabaseListOpen] = useState(false);
  const [isWorkgroupListOpen, setIsWorkgroupListOpen] = useState(false);
  const [isUserListOpen, setIsUserListOpen] = useState(false);
  const [isWorkspaceListOpen, setIsWorkspaceListOpen] = useState(false);
  const navigate = useNavigate();

  const handleWorkspaceCountClick = () => {
    setIsWorkspaceListOpen(true);
  };

  const renderField = (
    label: string,
    value: string | number | boolean | null | undefined,
    extraContent?: React.ReactNode,
    onClick?: () => void
  ) => (
    <div className="grid grid-cols-[280px_1fr] gap-6 py-1 border-b border-gray-100">
      <div className="text-gray-600 text-sm whitespace-nowrap">{label}</div>
      <div className="break-all text-sm pr-4 flex items-center gap-2">
        <span className={onClick ? 'text-blue-600 cursor-pointer hover:underline' : undefined} onClick={onClick}>
          {value?.toString() || '-'}
        </span>
        {extraContent}
      </div>
    </div>
  );

  const renderSection = (
    title: string,
    fields: { label: string; value: any; extraContent?: React.ReactNode; onClick?: () => void }[]
  ) => (
    <div className="space-y-2">
      <div className="text-sm font-medium text-gray-700 mb-2">{title}</div>
      <div className="space-y-1">
        {fields.map((field) => renderField(field.label, field.value, field.extraContent, field.onClick))}
      </div>
    </div>
  );

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} size="auto">
        <div className="p-6">
          <div className="mb-6">
            <h2>Organization Details</h2>
          </div>
          {error ? (
            <ErrorDisplay error={error as Error} />
          ) : isLoading ? (
            <LoadingIndicator />
          ) : (
            <div className="grid grid-cols-2 gap-x-8">
              {/* Left Column */}
              <div className="space-y-6">
                {renderSection('Basic Information', [
                  { label: 'Organization ID', value: org.org_id },
                  { label: 'Organization Name', value: org.org_name },
                  { label: 'Display Name', value: org.org_display_name },
                  { label: 'Creator', value: org.creator },
                  { label: 'Created At', value: format(parseDate(org.create_time), 'yyyy-MM-dd HH:mm:ss') },
                ])}

                {renderSection('Statistics', [
                  { label: 'Workspace Count', value: detail?.workspace_count, onClick: handleWorkspaceCountClick },
                  {
                    label: 'Workgroup Count',
                    value: detail?.workgroup_count,
                    onClick: () => setIsWorkgroupListOpen(true),
                  },
                  {
                    label: 'Database Count',
                    value: detail?.tg_database_count,
                    onClick: () => setIsDatabaseListOpen(true),
                  },
                  { label: 'User Count', value: detail?.user_count, onClick: () => setIsUserListOpen(true) },
                  { label: 'Credits', value: detail?.credits },
                ])}

                {/* Payment Methods */}
                {detail?.payment_methods?.credit_cards && detail.payment_methods.credit_cards.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-gray-700 mb-2">Payment Methods</div>
                    <div className="space-y-2">
                      {detail.payment_methods.credit_cards.map((card) => (
                        <div key={card.card_id} className="flex items-center gap-2">
                          <span className="text-sm">
                            {card.brand} **** {card.last_4_digits} ({card.name_on_card})
                          </span>
                          {card.card_id === detail.payment_methods.default_card && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">Default</span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Logo */}
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-700 mb-2">Logo</div>
                  <div>
                    {org.logo_url ? (
                      <div className="space-y-2">
                        <img
                          src={org.logo_url}
                          alt={`${org.org_name} logo`}
                          className="max-w-[200px] max-h-[200px] object-contain border border-gray-200 rounded-lg"
                        />
                        <div className="text-sm text-gray-500 break-all">{org.logo_url}</div>
                      </div>
                    ) : (
                      <div className="text-gray-500">-</div>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-700 mb-2">Quota Information</div>
                  <div className="space-y-1">
                    {renderField(
                      'Tier',
                      detail?.quota?.tier,
                      <Button
                        onClick={() => setIsEditDrawerOpen(true)}
                        size="compact"
                        kind="secondary"
                        startEnhancer={<MdEdit size={14} />}
                      >
                        Edit
                      </Button>
                    )}
                    {renderField('Max Workspace Type', detail?.quota?.max_workspace_type)}
                    {renderField('Max Workspace Memory', detail?.quota?.max_workspace_memory)}
                    {renderField('Read/Write Workspace Count Limit', detail?.quota?.rw_workspace_count_limit)}
                    {renderField('Read-Only Workspace Count Limit', detail?.quota?.ro_workspace_count_limit)}
                    {renderField('Memory Limit', detail?.quota?.memory_limit)}
                    {renderField('Workspace Auto Backup Count Limit', detail?.quota?.workspace_auto_backup_count_limit)}
                    {renderField(
                      'Workspace Auto Backup Retention In Days',
                      detail?.quota?.workspace_auto_backup_retention_in_days
                    )}
                    {renderField(
                      'Workspace Manual Backup Count Limit',
                      detail?.quota?.workspace_manual_backup_count_limit
                    )}
                    {renderField('Read/Write Workspace Count Usage', detail?.quota?.rw_workspace_count_usage)}
                    {renderField('Read-Only Workspace Count Usage', detail?.quota?.ro_workspace_count_usage)}
                    {renderField('Memory Usage', detail?.quota?.memory_usage_in_bytes)}
                  </div>
                </div>

                {/* Feature Flags */}
                {renderSection(
                  'Feature Flags',
                  detail?.feature_flag?.map((flag) => ({
                    label: flag.feature_name,
                    value: flag.enabled,
                  })) || []
                )}
              </div>
            </div>
          )}
        </div>
      </Modal>

      <EditOrgDrawer isOpen={isEditDrawerOpen} onClose={() => setIsEditDrawerOpen(false)} orgID={org.org_id} />

      {detail && (
        <>
          <DatabaseList detail={detail} isOpen={isDatabaseListOpen} onClose={() => setIsDatabaseListOpen(false)} />

          <WorkgroupList detail={detail} isOpen={isWorkgroupListOpen} onClose={() => setIsWorkgroupListOpen(false)} />

          <UserList detail={detail} isOpen={isUserListOpen} onClose={() => setIsUserListOpen(false)} />

          <WorkspaceList detail={detail} isOpen={isWorkspaceListOpen} onClose={() => setIsWorkspaceListOpen(false)} />
        </>
      )}
    </>
  );
}
