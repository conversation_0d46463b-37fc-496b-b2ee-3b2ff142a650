export const REQUEST_CHANNEL_PREFIX = 'event-source-request-id';

export const GLOBAL_GRAPH_NAME = '1';

export enum PageKey {
  SchemaDesigner = '/schema-designer',
  LoadingBuilder = '/loading-builder',
  GraphExplorer = '/graph-explorer',
  QueryBuilder = '/query-builder',
  QueryEditor = '/query-editor',
  Actions = '/actions',
  LoadingExecutor = '/loading-executor'
}

/** Represent GSQL role's privilege's key. */
export enum GSQLPrivilege {
  ReadSchema = 'READ_SCHEMA',
  WriteSchema = 'WRITE_SCHEMA',
  ReadLoadingJob = 'READ_LOADINGJOB',
  ExecuteLoadingJob = 'EXECUTE_LOADINGJOB',
  WriteLoadingJob = 'WRITE_LOADINGJOB',
  WriteDataSource = 'WRITE_DATASOURCE',
  ReadRole = 'READ_ROLE',
  WriteRole = 'WRITE_ROLE',
  ReadUser = 'READ_USER',
  WriteUser = 'WRITE_USER',
  ReadProxyGroup = 'READ_PROXYGROUP',
  WriteProxyGroup = 'WRITE_PROXYGROUP',
  ReadFile = 'READ_FILE',
  WriteFile = 'WRITE_FILE',
  DropGraph = 'DROP_GRAPH',
  ExportGraph = 'EXPORT_GRAPH',
  ClearGraphStore = 'CLEAR_GRAPHSTORE',
  DropAll = 'DROP_ALL',
  AccessTag = 'ACCESS_TAG',
  ReadData = 'READ_DATA',
  CreateData = 'CREATE_DATA',
  UpdateData = 'UPDATE_DATA',
  DeleteData = 'DELETE_DATA',
  AppAccessData = 'APP_ACCESS_DATA',
  AppAccessLog = 'APP_ACCESS_LOG',

  // FOR QUERY
  ReadQuery = 'READ_QUERY',
  // removed from 4.1.0
  WriteQuery = 'WRITE_QUERY',

  // new from 4.1.0
  Owner = 'OWNER',
  CreateQuery = 'CREATE_QUERY',
  UpdateQuery = 'UPDATE_QUERY',
  DropQuery = 'DROP_QUERY',
  InstallQuery = 'INSTALL_QUERY',
  ExecuteQuery = 'EXECUTE_QUERY',
}

export const ACCESS_PRIVILEGES = {
  [PageKey.SchemaDesigner]: [
    GSQLPrivilege.ReadSchema
  ],
  [PageKey.LoadingBuilder]: [
    GSQLPrivilege.ReadSchema,
    GSQLPrivilege.ReadLoadingJob
  ],
  [PageKey.LoadingExecutor]: [
    GSQLPrivilege.ReadSchema,
    GSQLPrivilege.ReadLoadingJob
  ],
  [PageKey.GraphExplorer]: [
    GSQLPrivilege.ReadSchema
  ],
  [PageKey.QueryBuilder]: [
    GSQLPrivilege.ReadSchema
  ],
  [PageKey.QueryEditor]: [
    GSQLPrivilege.ReadQuery
  ],
  [PageKey.Actions]: []
};

export const PAGE_NAME = {
  [PageKey.SchemaDesigner]: 'Design Schema',
  [PageKey.LoadingBuilder]: 'Map Data To Graph',
  [PageKey.GraphExplorer]: 'Explore Graph',
  [PageKey.QueryEditor]: 'Write Queries',
  [PageKey.Actions]: 'Actions'
};

export const PAGE_INDEX = {
  [PageKey.SchemaDesigner]: 0,
  [PageKey.LoadingBuilder]: 1,
  [PageKey.GraphExplorer]: 2,
  [PageKey.QueryEditor]: 3,
  [PageKey.Actions]: 4,
};
