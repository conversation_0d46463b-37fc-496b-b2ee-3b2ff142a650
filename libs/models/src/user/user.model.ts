import { GSQLPrivilege, GLOBAL_GRAPH_NAME } from '../utils';
import { cloneDeep } from 'lodash';

export interface UserRole {
  [graphName: string]: string[];
}

export interface GraphCreators {
  [graphName: string]: string;
}

export interface UserPrivilege {
  [graphName: string]: {
    privileges: GSQLPrivilege[];
    // fix this type
    childPermissions?: Record<string, { privileges: GSQLPrivilege[] }>;
  };
}

export interface UserSecret {
  alias: string;
  value: string;
}

/**
 * Represent a user's JSON.
 *
 * @export
 */
export interface UserInfo {
  name: string;
  isSuperUser: boolean;
  creators: GraphCreators;
  roles: UserRole;
  privileges: UserPrivilege;
  groups?: UserInfo[]; // The proxy groups that the user belongs to.
  secrets?: {
    [graphName: string]: UserSecret[]
  } | UserSecret[];
}

/**
 * GSQL user.
 *
 * @export
 */
export class User {
  private username: string;
  private superuser: boolean;
  private currentGraph: string;
  private creators: GraphCreators;
  private roles: UserRole;
  private privileges: UserPrivilege;
  private secrets: Map<string, UserSecret[]>;
  private globalSecrets: UserSecret[];

  constructor(info: UserInfo) {
    this.username = info.name;
    this.superuser = !!info.isSuperUser;
    this.creators = info.creators || {};
    this.roles = info.roles || {};
    this.privileges = info.privileges || {};
    // Before TG 4.1.0, info.secrets is an object, where the key is the graph name and the value is an array of secrets.
    // After TG 4.1.0, secrets are not graph-specific, info.secrets is an array.
    if (Array.isArray(info.secrets)) {
      this.globalSecrets = info.secrets;
    } else {
      this.secrets = this.parseToSecret(info.secrets);
    }
  }

  /** Return the username. */
  getUsername(): string {
    return this.username;
  }

  /**
   * Set the current graph the user is using.
   *
   * @memberof User
   */
  setCurrentGraph(graphName: string) {
    this.currentGraph = graphName;
  }

  /**
   * Return the current graph the user is using.
   *
   * @type {string}
   * @memberof User
   */
  getCurrentGraph(): string {
    return this.currentGraph;
  }

  /**
   * Return all of the graph creators.
   *
   * @readonly
   * @type {GraphCreators}
   * @memberof User
   */
  getCreators(): GraphCreators {
    return this.creators;
  }

  /** Return the creator for a given graph. */
  getCreatorForGraph(graphName: string): string {
    if (graphName) {
      return this.creators[graphName] || '';
    }
    return '';
  }

  /**
   * Return all of the user's roles.
   *
   * @readonly
   * @type {UserRole}
   * @memberof User
   */
  getRoles(): UserRole {
    return this.roles;
  }

  /** Return the user's roles for a given graph. */
  getRolesForGraph(graphName: string): string[] {
    let roles: string[] = [];
    if (graphName) {
      roles = cloneDeep(this.roles[graphName] || []);
    }
    return roles;
  }

  getPrivileges(): UserPrivilege {
    return this.privileges;
  }

  getPrivilegesForGraph(graphName: string): GSQLPrivilege[] {
    return this.privileges[graphName]?.privileges || [];
  }

  getChildPermissionsForGraph(graphName: string): Record<string, { privileges: GSQLPrivilege[] }> {
    return this.privileges[graphName]?.childPermissions || {};
  }

  /**
   * Return the user's highest level role for a given graph.
   *
   * @param {string} [graphName=this.currentGraph]
   * @returns {GSQLBuiltinRole}
   * @memberof User
   */
  getUserRole(graphName: string = this.currentGraph): string {
    return this.roles[graphName][0];
  }

  getUserRoles(graphName: string = this.currentGraph): string[] {
    return this.roles[graphName];
  }

  hasPrivilege(privilege: GSQLPrivilege, graphName: string = this.currentGraph): boolean {
    return (this.privileges[graphName] && this.privileges[graphName].privileges.includes(privilege)) ||
      (this.privileges[GLOBAL_GRAPH_NAME]) && this.privileges[GLOBAL_GRAPH_NAME].privileges.includes(privilege);
  }

  /** Return the user's secrets for a given graph. */
  getSecrets(graphName: string): UserSecret[] {
    let secrets: UserSecret[] = [];
    if (graphName) {
      secrets = cloneDeep(this.secrets.get(graphName) || []);
    }
    return secrets;
  }

  /** Return the global secrets. */
  getGlobalSecrets(): UserSecret[] {
    return cloneDeep(this.globalSecrets || []);
  }

  /** Return whether the user is Super User or not. */
  isSuperUser(): boolean {
    return this.superuser;
  }

  /**
   * Return all graph the user has access to.
   *
   * @returns {string[]}
   * @memberof User
   */
  getGraphList(): string[] {
    return Object.keys(this.roles);
  }

  /** Update user's information. */
  updateInfo(info: UserInfo, graphName: string) {
    this.superuser = !!info.isSuperUser;

    if (info.roles[graphName]) {
      this.roles[graphName] = info.roles[graphName];
    } else {
      delete this.roles[graphName];
    }

    if (info.privileges[graphName]) {
      this.privileges[graphName] = info.privileges[graphName];
    } else {
      delete this.privileges[graphName];
    }

    if (Array.isArray(info.secrets)) {
      this.globalSecrets = info.secrets;
    } else if (info.secrets[graphName]) {
      this.secrets.set(graphName, info.secrets[graphName]);
    } else {
      this.secrets.delete(graphName);
    }
  }

  /** Parse secret JSON. */
  private parseToSecret(secrets: { [graphName: string]: UserSecret[] }): Map<string, UserSecret[]> {
    const result = new Map<string, UserSecret[]>();
    if (secrets) {
      Object.keys(secrets).forEach(graphName =>
        result.set(graphName, secrets[graphName]));
    }

    return result;
  }
}
