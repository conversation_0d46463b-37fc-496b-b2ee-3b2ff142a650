/******************************************************************************
 * Copyright (c) 2017, TigerGraph Inc.
 * All rights reserved.
 * Project: GVIS
 * Brief: Define link object for graph chart.
 *
 * Created on: Oct 6th, 2016
 *      Author: <PERSON><PERSON>
 ******************************************************************************/
import { ItemsDataLink } from './items.data.link';
import { GraphChartDataNode, createGraphChartDataNode } from './graphchart.data.node';
import { GraphStyle } from './graphchart.style';
import { Utils } from './utils';

export interface ExternalLink {
  source: {
    id: string;
    type: string;
  };

  target: {
    id: string;
    type: string;
  };

  type: string;

  directed?: boolean;

  attrs?: {
    [key: string]: string | number | boolean | Object;
  };
  discriminator?: string;
  styles?: {
    [key: string]: string | number | Object;
  };
  others?: any;
  labels?: any;
}

/**
 * AddLinkOption interface.
 * It is used to determine how handle the nodes and link for a added link.
 * `defaults` | `extend` | `overwrite`
 * If a node or link exists:
 * `defaults`: keep using old values for any existed field, use new values for non existed field
 * `extend`: Merge new values with old values, prefer using new values for duplicated fields.
 * `overwrite`: descard all old values. Use new values instead.
 */
export interface AddLinkOption {
  link?: string;
  node?: string;
}

/**
 * Graph chart link object.
 * @preferred
 */
export class GraphChartDataLink extends ItemsDataLink {
  /**
   * source node object;
   * @type {GraphChartDataNode}
   */
  public source: GraphChartDataNode;

  /**
   * target node object;
   * @type {GraphChartDataNode}
   */
  public target: GraphChartDataNode;

  /**
   * external link type
   * @type {string}
   */
  public exType: string;

  /**
   * Indicate directed or undirected type link;
   * @type {boolean}
   */
  public directed: boolean;

  /**
   * Indicate discriminator for link;
   * @type {string}
   */
    public discriminator: string | undefined;

  /**
   * Style object for graph link object.
   */
  public styles: GraphStyle.GraphChartLinkStyles;

  /**
   * label object to determine which label needs to be shown in visualization.
   */
  public labels: {
    [attrName: string]: boolean;
  };

  private pointsX: number[] = [];
  public get x(): number[] {
    return this.pointsX;
  }

  private pointsY: number[] = [];
  public get y(): number[] {
    return this.pointsY;
  }

  /**
   * This field is used for storing middle reuslt from several algorithms which need to be shown in visualization or affect layout.
   * @type {any}
   */
  public others: any;

  public isLink = true;

  /**
   * An internal link object.
   * @param {string}             internalID internalID by registerExternalLink;
   * @param {ExternalLink}       exLink     external link data object
   * @param {GraphChartDataNode} source     internal source node object
   * @param {GraphChartDataNode} target     internal target node object
   */
  constructor(internalID: string, exLink: ExternalLink, source: GraphChartDataNode, target: GraphChartDataNode) {
    super(internalID, source.id, target.id);

    this.source = source;
    this.target = target;
    this.exType = exLink.type;
    this.directed = exLink.directed === undefined ? true : exLink.directed;
    this.discriminator = exLink.discriminator;

    this.attrs = Utils.cloneDeep(exLink.attrs) || Object.create(null);
    this.styles = new GraphStyle.GraphChartLinkStyles(exLink.styles);
    this.others = Utils.cloneDeep(exLink.others) || Object.create(null);
    this.labels = Object.create(null);
  }

  /**
   * Merge external node. Prefer external node values.
   * @param  {ExternalNode} exNode [description]
   * @return {this}                [description]
   */
  public extendLink(exLink: ExternalLink): this {
    let newLink = Utils.cloneDeep(exLink);

    for (let key in newLink.attrs) {
      if (Object.prototype.hasOwnProperty.call(newLink.attrs, key)) {
        if (newLink.attrs[key] === '') {
          delete newLink.attrs[key];
        }
      }
    }

    Utils.extend(this.attrs, newLink.attrs);

    let tmpStyle = new GraphStyle.GraphChartLinkStyles(newLink.styles);
    Utils.extend(this.styles, tmpStyle);

    Utils.extend(this.others, newLink.others);
    Utils.extend(this.labels, newLink.labels);

    if (exLink.directed === false) {
      this.directed = false;
    }

    return this;
  }

  /**
   * Merge external Link. Prefer old Link values.
   * @param  {ExternalLink} exLink [description]
   * @return {this}                [description]
   */
  public defaultsLink(exLink: ExternalLink): this {
    let newLink = Utils.cloneDeep(exLink);

    Utils.defaultsDeep(this.attrs, newLink.attrs);

    let tmpStyle = new GraphStyle.GraphChartLinkStyles(newLink.styles);
    Utils.defaultsDeep(this.styles, tmpStyle);

    Utils.defaultsDeep(this.others, newLink.others);
    Utils.defaultsDeep(this.labels, newLink.labels);

    if (exLink.directed === false) {
      this.directed = false;
    }

    return this;
  }

  /**
   * Assign external Link values to old Links.
   * @param  {ExternalLink} exLink [description]
   * @return {this}                [description]
   */
  public overwriteLink(exLink: ExternalLink): this {
    let newLink = Utils.cloneDeep(exLink);

    this.attrs = newLink.attrs;

    let tmpStyle = new GraphStyle.GraphChartLinkStyles(newLink.styles);
    this.styles = tmpStyle;

    this.others = newLink.others;
    this.labels = newLink.labels;

    if (exLink.directed === false) {
      this.directed = false;
    }

    return this;
  }
}

export function getLinkID(link: ExternalLink): string {
  const source = `${link.source.type}#${link.source.id}`;
  const target = `${link.target.type}#${link.target.id}`;
  let id = `${link.type}#${source}#${target}${link.discriminator ? '#' + link.discriminator : ''}`;
  if (!link.directed) {
    id =
      source > target
        ? `${link.type}#${source}#${target}${link.discriminator ? '#' + link.discriminator : ''}`
        : `${link.type}#${target}#${source}${link.discriminator ? '#' + link.discriminator : ''}`;
  }
  return id;
}

export function createGraphChartDataLink(exLink: ExternalLink): GraphChartDataLink {
  return new GraphChartDataLink(
    getLinkID(exLink),
    exLink,
    createGraphChartDataNode(exLink.source),
    createGraphChartDataNode(exLink.target),
  );
}
