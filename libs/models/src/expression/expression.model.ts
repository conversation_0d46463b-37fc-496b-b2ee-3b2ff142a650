import { GraphChartDataNode, GraphChartDataLink } from '../gvis/insights';

import { DataType } from '../data';

import { Atom, AtomJson } from './atom.model';
import { EvaluateResult } from './evaluate-result.interface';
import { ExpressionValidator, ExpressionValidateResult } from './expression-validator';
import { ArithmeticOperator, FunctionType } from './operators.enum';

/**
 * JSON format for expression.
 *
 * @export
 * @interface ExpressionJson
 */
export interface ExpressionJson {
  type: string;
  operands: Array<AtomJson | ExpressionJson>;
  operator?: ArithmeticOperator | FunctionType;
}

/**
 * Parameter and return types of function.
 *
 * @export
 * @const FunctionMetaInfo
 */
export const FunctionMetaInfo = new Map<
  FunctionType,
  {
    returnType: DataType,
    paramType: DataType[]
  }
>([
  [
    FunctionType.ToFloat,
    {
      returnType: DataType.Real,
      paramType: [DataType.String]
    }
  ],
  [
    FunctionType.ToInt,
    {
      returnType: DataType.Int,
      paramType: [DataType.String]
    }
  ],
  [
    FunctionType.Concat,
    {
      returnType: DataType.String,
      paramType: [DataType.String, DataType.String]
    }
  ],
  [
    FunctionType.TokenLen,
    {
      returnType: DataType.Int,
      paramType: [DataType.String]
    }
  ],
  [
    FunctionType.Sqrt,
    {
      returnType: DataType.Real,
      paramType: [DataType.Real]
    }
  ],
  [
    FunctionType.Sum,
    {
      returnType: DataType.Real,
      paramType: [DataType.Real]
    }
  ],
  [
    FunctionType.Min,
    {
      returnType: DataType.Real,
      paramType: [DataType.Real]
    }
  ],
  [
    FunctionType.Max,
    {
      returnType: DataType.Real,
      paramType: [DataType.Real]
    }
  ],
  [
    FunctionType.Avg,
    {
      returnType: DataType.Real,
      paramType: [DataType.Real]
    }
  ],
  [
    FunctionType.Count,
    {
      returnType: DataType.Int,
      paramType: []
    }
  ],
  [
    FunctionType.DistinctCount,
    {
      returnType: DataType.Int,
      paramType: [DataType.Unknown]
    }
  ],
  [
    FunctionType.Collect,
    {
      returnType: DataType.Collection,
      paramType: [DataType.Unknown]
    }
  ],
  [
    FunctionType.DistinctCollect,
    {
      returnType: DataType.Collection,
      paramType: [DataType.Unknown]
    }
  ]
]);

/**
 * Base class for expression.
 *
 * @export
 * @abstract
 * @class BaseExpression
 */
export abstract class BaseExpression {
  operands: Array<Atom | BaseExpression>;
  operator?: ArithmeticOperator | FunctionType;
  operandsTypes: any[][];

  constructor(
    operands: Array<Atom | BaseExpression>,
    operator?: ArithmeticOperator | FunctionType
  ) {
    this.operands = operands;
    this.operator = operator;
  }

  getReferredVertexOrEdgeTypes(): string[] {
    const types: string[] = [];
    if (this.operands) {
      this.operands.forEach(operand => types.push(...operand.getReferredVertexOrEdgeTypes()));
    }
    return types;
  }

  evaluate(nodeOrLink?: GraphChartDataNode | GraphChartDataLink): EvaluateResult;
  evaluate(): EvaluateResult {
    return {
      applicable: false
    };
  }


  abstract getType(): DataType;
  abstract toString(): string;
  abstract toJson(): ExpressionJson;
  abstract semanticCheck(): ExpressionValidateResult;
}

/**
 * Class for handling undefined expression.
 *
 * @export
 * @class NullExpression
 * @extends {BaseExpression}
 */
export class NullExpression extends BaseExpression {
  constructor() {
    super(undefined, undefined);
  }

  getType() {
    return DataType.Unknown;
  }

  toString() {
    return '';
  }

  toJson() {
    return {
      type: 'NullExpression',
      operands: []
    };
  }

  semanticCheck(): ExpressionValidateResult {
    return {
      success: false,
      errors: ['Please choose expression type.']
    };
  }
}

/**
 * Class for function expression.
 *
 * @export
 * @class FuncExpression
 * @extends {BaseExpression}
 */
export class FunctionExpression extends BaseExpression {
  operator: FunctionType;
  operandsTypes = [
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      'BoolConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'StringParam',
      'IntAccum',
      'RealAccum',
      'StringAccum',
      'BoolAccum',
      'LoadingVariable',
      'AttrVariable',
      'AttrVariableWithAlias',
      'AliasReference',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power,
    ]
  ];

  constructor(operands: Array<Atom | BaseExpression>, operator: FunctionType) {
    super(operands, operator);
  }

  getType(): DataType {
    return FunctionMetaInfo.get(this.operator).returnType;
  }

  toString(): string {
    // For COUNT(*), directly return it as the expression.
    if (this.operator === FunctionType.Count) {
      return this.operator;
    } else if (this.operator === FunctionType.DistinctCount) {
      return `COUNT(DISTINCT ${this.operands.map(operand => operand.toString()).join(',')})`;
    } else if (this.operator === FunctionType.DistinctCollect) {
      return `COLLECT(DISTINCT ${this.operands.map(operand => operand.toString()).join(',')})`;
    }
    return `${this.operator}` +
      `(${this.operands.map(operand => operand.toString()).join(',')})`;
  }

  toJson(): ExpressionJson {
    return {
      type: 'FunctionExpression',
      operator: this.operator,
      operands: this.operands.map(operand => operand.toJson())
    };
  }

  // Here we assume that operands are not undefined.
  semanticCheck(): ExpressionValidateResult {
    if (!Object.values(FunctionType).includes(this.operator)) {
      return {
        success: false,
        errors: [
          'This expression contains an unsupported function type. ' +
          'Did you import a solution from a higher version of GraphStudio?'
        ]
      };
    }

    for (let i = 0; i < this.operands.length; i++) {
      if (!this.operands[i].semanticCheck().success) {
        return {
          success: false,
          errors: []
        };
      }
    }

    let passSemanticCheck = true;
    const semanticErrors = [];
    const operandsArray = this.operands;
    const functionReturnObject = FunctionMetaInfo.get(this.operator);
    const lengthOfParamType = functionReturnObject.paramType.length;

    if (operandsArray.length !== lengthOfParamType) {
      // if number of operands doesn't fit the number of expected parameters
      return {
        success: false,
        errors: [
          `Expect ${lengthOfParamType}` +
          ' argument(s), but get ' +
          `${this.operands.length}.`
        ]
      };
    }
    functionReturnObject.paramType.forEach((type, index) => {
      // Check type compatible one by one in range of meta specified param number.
      const operandType = operandsArray[index].getType();

      if (type !== DataType.Unknown && operandType !== type &&
        !(
          ExpressionValidator.isReal(type) &&
          ExpressionValidator.isInt(operandType)
        )
      ) {
        // Integer type can be treated as real type even the type name are different.
        passSemanticCheck = false;
        semanticErrors.push(
          `Expression${index + 1} should be ${type}, ` +
          `not ${operandsArray[index].getType()}.`
        );
      }
    });

    return {
      success: passSemanticCheck,
      errors: semanticErrors
    };
  }
}

/**
 * Class for arithmetic expression.
 *
 * @export
 * @class ArithExpression
 * @extends {BaseExpression}
 */
export class ArithmeticExpression extends BaseExpression {
  operator: ArithmeticOperator;
  operandsTypes = [
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      'BoolConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'StringParam',
      'IntAccum',
      'RealAccum',
      'StringAccum',
      'BoolAccum',
      'LoadingVariable',
      'AttrVariable',
      'AttrVariableWithAlias',
      'Function',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ],
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      'BoolConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'StringParam',
      'IntAccum',
      'RealAccum',
      'StringAccum',
      'BoolAccum',
      'LoadingVariable',
      'AttrVariable',
      'AttrVariableWithAlias',
      'Function',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ]
  ];

  constructor(operands: Array<Atom | BaseExpression>, operator: ArithmeticOperator) {
    super(operands, operator);
  }

  getType(): DataType {
    if (this.operands[0].getType() === this.operands[1].getType()) {
      return this.operands[0].getType();
    } else {
      return DataType.Real;
    }
  }

  toString(): string {
    return `(${this.operands[0]} ${this.operator} ${this.operands[1]})`;
  }

  toJson(): ExpressionJson {
    return {
      type: 'ArithmeticExpression',
      operator: this.operator,
      operands: [
        this.operands[0].toJson(),
        this.operands[1].toJson()
      ]
    };
  }

  // Here we assume that operands are not undefined.
  semanticCheck(): ExpressionValidateResult {
    if (!Object.values(ArithmeticOperator).includes(this.operator)) {
      return {
        success: false,
        errors: [
          'This expression contains an unsupported arithmetic expression type. ' +
          'Did you import a solution from a higher version of GraphStudio?'
        ]
      };
    }
    if (
      !this.operands[0].semanticCheck().success ||
      !this.operands[1].semanticCheck().success
    ) {
      return {
        success: false,
        errors: []
      };
    }

    const leftOperandType = this.operands[0].getType();
    const rightOperandType = this.operands[1].getType();

    if (
      (
        !ExpressionValidator.isInt(leftOperandType) ||
        !ExpressionValidator.isInt(rightOperandType)
      ) &&
      this.operator === ArithmeticOperator.Mod
    ) {
      return {
        success: false,
        errors: [`Both operands must be integers for operator "${this.operator.toString()}".`]
      };
    }
    if (
      ExpressionValidator.isNumeric(leftOperandType) &&
      ExpressionValidator.isNumeric(rightOperandType)
    ) {
      return {
        success: true,
        errors: []
      };
    }
    if (
      ExpressionValidator.isString(leftOperandType) &&
      ExpressionValidator.isString(rightOperandType)
      && this.operator === ArithmeticOperator.Plus
      // + operator can be used with two string, performs as concat.
    ) {
      return {
        success: true,
        errors: []
      };
    }

    return {
      success: false,
      errors: [
        `${leftOperandType} and ${rightOperandType} are incompatible ` +
        `for operator "${this.operator.toString()}".`
      ]
    };
  }

  evaluate(nodeOrLink: GraphChartDataNode | GraphChartDataLink): EvaluateResult {
    const leftOperandEvalResult = this.operands[0].evaluate(nodeOrLink);
    const rightOperandEvalResult = this.operands[1].evaluate(nodeOrLink);
    const falseResult = {
      applicable: false
    };
    if (!leftOperandEvalResult.applicable || !rightOperandEvalResult.applicable) {
      return falseResult;
    }

    let evalResult: string | boolean | number;
    switch (this.operator.toString()) {
      case '+': {
        evalResult = typeof leftOperandEvalResult.value === 'number' ?
          <number>leftOperandEvalResult.value + <number>rightOperandEvalResult.value :
          <string>leftOperandEvalResult.value + <string>rightOperandEvalResult.value;
        break;
      }
      case '-': {
        evalResult = <number>leftOperandEvalResult.value - <number>rightOperandEvalResult.value;
        break;
      }
      case '*': {
        evalResult = <number>leftOperandEvalResult.value * <number>rightOperandEvalResult.value;
        break;
      }
      case '/': {
        evalResult = <number>leftOperandEvalResult.value / <number>rightOperandEvalResult.value;
        break;
      }
      case '%': {
        evalResult = <number>leftOperandEvalResult.value % <number>rightOperandEvalResult.value;
        break;
      }
      case '^': {
        evalResult = Math.pow(
          <number>leftOperandEvalResult.value,
          <number>rightOperandEvalResult.value
        );
        break;
      }
    }

    if (Number.isNaN(<number>evalResult) || evalResult === Infinity) {
      return falseResult;
    } else {
      return {
        applicable: true,
        value: evalResult
      };
    }
  }
}

/**
 * Class for arithmetic expression in WHERE clause.
 * Since '+' can not be used with 2 strings in WHERE clause of a loading job,
 * we need to check more for WHERE clause.
 *
 * @export
 * @class ArithmeticExpressionInWhereClause
 * @extends {ArithmeticExpression}
 */
export class ArithmeticExpressionInWhereClause extends ArithmeticExpression {
  toJson(): ExpressionJson {
    return {
      type: 'ArithmeticExpressionInWhereClause',
      operator: this.operator,
      operands: [
        this.operands[0].toJson(),
        this.operands[1].toJson()
      ]
    };
  }

  semanticCheck(): ExpressionValidateResult {
    const passSemanticCheck = super.semanticCheck();
    if (!passSemanticCheck.success) {
      return passSemanticCheck;
    }

    if (this.operator === ArithmeticOperator.Plus) {
      passSemanticCheck.success = ExpressionValidator.isNumeric(
        this.operands[0].getType()) &&
        ExpressionValidator.isNumeric(this.operands[1].getType()
        );
      if (!passSemanticCheck.success) {
        passSemanticCheck.errors.push(
          'Only numeric type operands can be used ' +
          'with + operator in WHERE clause.'
        );
      }
    }

    return passSemanticCheck;
  }
}

/**
 * Class for arithmetic expression in an aggregation function.
 * The arithmetic expression in the aggregation function
 * shall not include Function as an option.
 * @export
 * @class ArithmeticExpressionInAggregationFunction
 * @extends {ArithmeticExpression}
 */
export class ArithmeticExpressionInAggregationFunction extends ArithmeticExpression {
  operandsTypes = [
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      'BoolConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'StringParam',
      'IntAccum',
      'RealAccum',
      'StringAccum',
      'BoolAccum',
      'LoadingVariable',
      'AttrVariable',
      'AttrVariableWithAlias',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ],
    [
      'NullExpression',
      'IntConstant',
      'RealConstant',
      'StringConstant',
      'BoolConstant',
      'IntParam',
      'RealParam',
      'BoolParam',
      'StringParam',
      'IntAccum',
      'RealAccum',
      'StringAccum',
      'BoolAccum',
      'LoadingVariable',
      'AttrVariable',
      'AttrVariableWithAlias',
      ArithmeticOperator.Plus,
      ArithmeticOperator.Minus,
      ArithmeticOperator.Multiply,
      ArithmeticOperator.Divide,
      ArithmeticOperator.Mod,
      ArithmeticOperator.Power
    ]
  ];

  toJson(): ExpressionJson {
    return {
      type: 'ArithmeticExpressionInAggregationFunction',
      operator: this.operator,
      operands: [
        this.operands[0].toJson(),
        this.operands[1].toJson()
      ]
    };
  }
}
