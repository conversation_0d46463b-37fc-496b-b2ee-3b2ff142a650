import { QueryParam } from './query-param.interface';

export type QuerySyntax = 'GSQL' | 'CYPHER'

/**
 * Meta information of one query.
 * - queryName: query name
 * - installed: whether query is installed or not
 * - params: the parameters of the query
 *
 * @export
 * @interface QueryMeta
 */
export interface QueryMeta {
  queryName: string;
  syntax?: QuerySyntax;
  installed: boolean;
  enabled: boolean;
  callerQueries: string[];
  installing: boolean;
  params: QueryParam[];
  originalCode: string;
  draftCode: string;
  graphUpdate: boolean;
  isHidden: boolean;
  isACLSpecified: boolean;
}
