import { QuerySyntax } from "./query-meta.interface";

/**
 * Meta information exported from gsql server.
 *
 * @export
 * @interface GsqlQueryMeta
 */
export interface GsqlQueryMeta {
  optimizedLevel: number;
  installed: boolean;
  enabled: boolean;
  callerQueries: string[];
  installing: boolean;
  graphUpdate: boolean;
  isHidden: boolean;
  isACLSpecified: boolean;
  endpoint: {
    query?: {
      [graphName: string]: {
        [queryName: string]: {
          'GET/POST': {
            parameters: {
              [paramName: string]: {
                type: string;
                index: number;
                max_count?: number;
                is_id?: 'true';
                id_type?: string;
                defaultValue?: any;
                [key: string]: any;
              }
            },
            [key: string]: any;
          };
        }
      }
    }
  };
  syntax?: QuerySyntax;
  name: string;
  draft: string;
  code: string;
  [key: string]: any;
}
