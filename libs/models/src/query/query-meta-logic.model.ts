import { GsqlQueryMeta } from './gsql-query-meta.interface';
import { QueryMeta } from './query-meta.interface';
import {
  QueryParamType,
  QueryParamVertexType,
  QueryParamListType
} from './query-param-type.interface';

export class QueryMetaLogic {
  static loadFromGSQL(graphName: string, gsqlQueryMeta: GsqlQueryMeta): QueryMeta {
    // Init the query meta object
    const queryMeta: QueryMeta = {
      queryName: gsqlQueryMeta.name,
      installed: gsqlQueryMeta.installed,
      enabled: gsqlQueryMeta.enabled,
      callerQueries: gsqlQueryMeta.callerQueries,
      installing: gsqlQueryMeta.installing,
      params: [],
      originalCode: gsqlQueryMeta.code,
      draftCode: gsqlQueryMeta.draft,
      graphUpdate: gsqlQueryMeta.graphUpdate,
      isHidden: gsqlQueryMeta.isHidden,
      isACLSpecified: gsqlQueryMeta.isACLSpecified,
      syntax: gsqlQueryMeta.syntax || 'GSQL',
    };

    // get the parameters
    const parameters = gsqlQueryMeta.endpoint.query ?
      gsqlQueryMeta.endpoint.query[graphName][gsqlQueryMeta.name]['GET/POST'].parameters :
      {};

    const orderedParams = Object.keys(parameters)
      // query itself is ignored, vertex type is ignored
      .filter(paramName => paramName !== 'query' && !paramName.endsWith('.type'))
      .sort((p1, p2) => parameters[p1].index - parameters[p2].index);

    orderedParams.forEach(paramName => {

      // const param type based on param meta
      const paramMeta = parameters[paramName];
      const queryParamType: QueryParamType = {
        type: paramMeta.type
      };
      // fix INT64 and UINT64
      if (queryParamType.type === 'INT64') {
        queryParamType.type = 'INT';
      }
      if (queryParamType.type === 'UINT64') {
        queryParamType.type = 'UINT';
      }
      // handle VERTEX type attribute
      if (paramMeta.is_id !== undefined) {
        queryParamType.type = 'VERTEX';
        // if paramName.type exists in parameters, means the vertex is any type vertex
        if ((paramName + '.type') in parameters) {
          (<QueryParamVertexType>queryParamType).vertexType = '*';
        } else {
          // otherwise the vertex type is given in id_type
          (<QueryParamVertexType>queryParamType).vertexType = paramMeta.id_type;
        }
      }

      // Push the param meta into the query meta.
      // If max_count exists in the param meta, it is a list param, otherwise it is normal param
      const paramType = paramMeta.max_count ?
        <QueryParamListType> {
          type: 'LIST',
          elementType: queryParamType
        } :
        queryParamType;

      // If the parameter has default value, set default value.
      if (parameters[paramName].defaultValue) {
        const defaultValue = parameters[paramName].defaultValue;
        queryMeta.params.push({
          paramName: paramName,
          paramType: paramType,
          paramDefaultValue: this.handleParamDefaultValue(paramType, defaultValue)
        });
      } else {
        queryMeta.params.push({
          paramName: paramName,
          paramType: paramType
        });
      }
    });

    return queryMeta;
  }

  static handleParamDefaultValue(paramType: QueryParamType, paramDefaultValue: any) {
    return paramType.type === 'STRING' ? this.unescapeStr(paramDefaultValue) : paramDefaultValue;
  }

  static unescapeStr(str: string): string {
    return str.replace(/\\(.)/g, function (match, char) {
      if (char == '\\')
        return '\\';
      if (char == 'n')
        return '\n';
      if (char == 't')
        return '\t';
      if (char == '')
        return '';
      return char;
    });
  }
}
