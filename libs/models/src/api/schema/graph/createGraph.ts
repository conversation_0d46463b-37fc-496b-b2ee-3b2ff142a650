import { getAxiosInstance } from '../../request';
import { getVersionIsGTE410 } from '../../version';

export type CreateGraphParams = {
  GraphName: string;
  VertexTypes: string[];
  EdgeTypes: string[];
};

export const createGraph = async (params: CreateGraphParams) => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().post('/api/gsql-server/gsql/v1/schema/graphs', {
      VertexTypes: params.VertexTypes,
      EdgeTypes: params.EdgeTypes,
    }, {
      params: {
        graphName: params.GraphName,
      }
    });
    return res;
  } else {
    const res = getAxiosInstance().post('/api/gsql-server/gsql/schema', params);
    return res;
  }
};
