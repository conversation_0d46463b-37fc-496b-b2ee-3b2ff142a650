import { getAxiosInstance } from '../../request';
import { getVersionIsGTE410 } from '../../version';

export type GetGraphSchemaParams = {
  graph: string;
};

export const getGraphSchema = async (params?: GetGraphSchemaParams) => {
  if (await getVersionIsGTE410()) {
    const graph = params?.graph || 'global';
    const res = await getAxiosInstance().get(`/api/gsql-server/gsql/v1/schema/graphs/${graph}`);
    return res;
  } else {
    const res = await getAxiosInstance().get('/api/gsql-server/gsql/schema', {
      params,
    });
    return res;
  }
};
