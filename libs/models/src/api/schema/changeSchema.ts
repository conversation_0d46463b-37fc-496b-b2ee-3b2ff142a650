import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type ChangeSchemaParams = {
  graph: string;
  schemaChange: any;
};

export const changeSchema = async (params: ChangeSchemaParams) => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/schema/change',
      params.schemaChange,
      {
        params: {
          graph: params.graph,
        },
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().put(
      '/api/gsql-server/gsql/schema/change',
      params.schemaChange,
      {
        params: {
          graph: params.graph,
        },
      }
    );
    return res;
  }
};
