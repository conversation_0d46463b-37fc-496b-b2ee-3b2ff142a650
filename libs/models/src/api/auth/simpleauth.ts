import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

type SecretData = {
  alias: string;
  value: string;
};

export type SimpleAuthReturnType = Result<{
  failedAttempts: number;
  isSuperUser: boolean;
  lastSuccessLogin: string;
  name: string;
  nextValidLogin: string;
  showAlterPasswordWarning: boolean;
  privileges?: {
    [graphName: string]: string[];
  };
  roles?: {
    [graphName: string]: string[];
  };
  secrets?:
    | {
        [graphName: string]: SecretData[];
      }
    | SecretData[];
}>;

export const simpleAuth = async () => {
  if (await getVersionIsGTE410()) {
    // may need transform result
    const res = await getAxiosInstance().get<SimpleAuthReturnType>(
      '/api/gsql-server/gsql/v1/auth/simple'
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<SimpleAuthReturnType>(
      '/api/gsql-server/gsql/simpleauth'
    );

    return res;
  }
};
