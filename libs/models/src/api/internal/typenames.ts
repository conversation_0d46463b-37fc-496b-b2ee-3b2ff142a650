import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export const getTypeNames = async () => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/internal/info?type=type-names'
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/typenames'
    );

    return res;
  }
};