import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export const isSchemaReady = async () => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/v1/internal/check/schema-ready'
    );

    return ResizeObserverSize;
  } else {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/isschemaready'
    );

    return res;
  }
};