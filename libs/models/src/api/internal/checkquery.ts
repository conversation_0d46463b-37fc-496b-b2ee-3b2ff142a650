import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type CheckQueryParam = {
  graph: string;
  code: string;
};

export const checkQuery = async (params: CheckQueryParam) => {
  const { code, graph } = params;
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/v1/internal/check/query',
      {
        code,
      },
      {
        params: {
          graph,
        }
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/codecheck',
      {
        code,
        graph,
      },
    );

    return res;
  }
};
