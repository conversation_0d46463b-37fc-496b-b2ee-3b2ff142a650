import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export const getAutoKeys = async () => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/v1/internal/info/auto-keys'
    );

    return ResizeObserverSize;
  } else {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/getinfos/autokeys'
    );

    return res;
  }
};