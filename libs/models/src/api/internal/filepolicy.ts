import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type FilePolicyParam = {
  path: string
}

export const checkFilePolicy = async (params: FilePolicyParam) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/internal/check/file-policy',
      {
        params,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/filepolicy',
      {
        params,
      }
    );

    return res;
  }
};