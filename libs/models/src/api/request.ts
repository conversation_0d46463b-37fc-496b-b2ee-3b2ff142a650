import { AxiosRequestConfig, AxiosInstance, isAxiosError } from 'axios';
import axios from './axios.min.js';
import { handlePasswordExpired, isPasswordExpired, isSessionRevoked, showSessionEndDialog } from '../error/http-error.js';

const config: AxiosRequestConfig = {
  // TODO will change here config
};
const axiosInstance: AxiosInstance = axios.create(config);

axiosInstance.interceptors.response.use(
  (response) => {
    if (response.data?.error) {
      throw new Error(response.data.message || 'Some errors have occurred.');
    }

    return response;
  },
  (err) => {
    if (err?.response?.status === 401) {
      if (isPasswordExpired(err.response)) {
        handlePasswordExpired();
      } else if (isSessionRevoked(err.response)) {
        showSessionEndDialog();
      } else if (!window.location.hash.includes('login')) {
        // don't do this when imported by cloud portal
        window.location.href = `${window.location.origin}/#/login?returnURL=${encodeURIComponent(
          window.location.pathname + window.location.search + window.location.hash
        )}`;
      }
    }

    if (isAxiosError(err)) {
      const message = err.response.data?.message || err.message;
      (err as any).error = message;
      err.message = message;
    }
    return Promise.reject(err);
  }
);

export const getAxiosInstance = () => {
  return axiosInstance;
};

export const setUpCloudEnvAxiosInstance = () => {
  axiosInstance.defaults.withCredentials = true;
  if (sessionStorage.getItem('BASEURL')) {
    axiosInstance.defaults.baseURL = sessionStorage.getItem('BASEURL');
  }
};
