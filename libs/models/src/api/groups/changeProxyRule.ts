import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type ChangeProxyGroupParams = {
  name: string;
  rule: string;
};

export const changeProxyRule = async (params: ChangeProxyGroupParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().patch<Result>(
      `/api/gsql-server/gsql/scim/v2/Groups/${params.name}`,
      params,
      {
        params: {
          gsqlFormat: true,
        },
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().put<Result>(
      '/api/gsql-server/gsql/proxy-groups',
      params
    );
    return res;
  }
};
