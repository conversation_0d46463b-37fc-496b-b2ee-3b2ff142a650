import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type DeleteProxyGroup = {
  name: string;
};

export const deleteProxyGroup = async (params: DeleteProxyGroup) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/scim/v2/Groups`,
      {
        groupNames: [params.name],
      },
      {
        params: {
          gsqlFormat: true,
          action: 'delete',
        },
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().delete<Result>(
      '/api/gsql-server/gsql/proxy-groups',
      {
        params,
      }
    );
    return res;
  }
};
