import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type DeleteOneDataSourceParam = {
  graph: string;
  name: string;
};

export const deleteOneDataSource = async (params: DeleteOneDataSourceParam) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/v1/data-sources?graph=${params.graph}&name=${params.name}`
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/data-sources/${params.name}?graph=${params.graph}`
    );

    return res;
  }
};
