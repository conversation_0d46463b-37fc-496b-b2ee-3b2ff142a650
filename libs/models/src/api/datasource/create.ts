import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type CreateOneDataSourceParam = {
  graph: string;
  name: string;
  config: any;
};

export const createOneDataSource = async (params: CreateOneDataSourceParam) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/v1/data-sources?graph=${params.graph}`,
      {
        params: {
          name: params.name,
          config: params.config,
        },
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/data-sources?graph=${params.graph}`,
      {
        params: {
          name: params.name,
          config: params.config,
        },
      }
    );

    return res;
  }
};
