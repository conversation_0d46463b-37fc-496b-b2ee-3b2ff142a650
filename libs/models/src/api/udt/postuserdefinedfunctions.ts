import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type PostUserDefinedFunctionsParams = {
  filename: string,
  filepath: string,
  errormessage?: string,
  content?: string
}

export const uploadUserDefinedFunctions = async (params: PostUserDefinedFunctionsParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/v1/udt/filels?${params.filename}&filepath=${params.filepath}`,
      {
        params: {
          content: params.content
        }
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/userdefinedfunction?${params.filename}&filepath=${params.filepath}`,
      {
        params: {
          content: params.content
        }
      }
    );

    return res;
  }
};