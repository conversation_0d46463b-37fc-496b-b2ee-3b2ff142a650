import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';


export type GetUserDefinedFunctionsParams = {
  filename: string,
  filepath: string
  errormessage?: string
}


export const getUserDefinedFunctions = async (params: GetUserDefinedFunctionsParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/v1/udt/filels?filename=${params.filename}&filepath=${params.filepath}`
    );

    return res.data.results;
  } else {
    const res = await getAxiosInstance().get<Result>(
      `/api/gsql-server/gsql/userdefinedfunction?filename=${params.filename}&filepath=${params.filepath}`
    );

    return res.data.results;
  }
};