import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export const getTokenFunctions = async () => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/udt/token-functions'
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/userdefinedtokenfunctions'
    );

    return res;
  }
};