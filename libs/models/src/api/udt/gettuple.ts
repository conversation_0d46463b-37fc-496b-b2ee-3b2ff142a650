import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type Tuple = {
  name: string;
  fields: {
    fieldname: string;
    filedtype: string;
    length: number;
  }[];
};

export type GetAllTuplesParams = {
  graph: string;
}

export type GetAllTuplesReturnType = {
  Tuples: Tuple[];
};

export const getUDTTuples = async (params: GetAllTuplesParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result<GetAllTuplesReturnType>>(
      '/api/gsql-server/gsql/v1/udt/tuples',
      {
        params,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result<GetAllTuplesReturnType>>(
      '/api/gsql-server/gsql/udt',
      {
        params,
      }
    );

    return res;
  }
};
