import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type ListBucketsParam = {
  datasource?: string;
};

export const getListBuckets = async (params: ListBucketsParam) => {
  const baseOldUrl = params.datasource
    ? `/api/gsql-server//gsql/lists3buckets?datasource=${params.datasource}`
    : `/api/gsql-server/gsql/lists3buckets`;
  const baseNewUrl = params.datasource
    ? `/api/gsql-server/gsql/v1/list-buckets/${params.datasource}`
    : `/api/gsql-server/gsql/v1/list-buckets`;

  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(baseNewUrl);

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(baseOldUrl);

    return res;
  }
};
