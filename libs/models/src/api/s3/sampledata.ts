import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type SampleDataParam = {
  size: number;
  config?: JSON;
  datasource?: string;
  path: string;
  type: string;
};

export const getSampleData = async (params: SampleDataParam) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/v1/sample-data',
      params,
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/sampledata',
      params,
    );

    return res;
  }
};
