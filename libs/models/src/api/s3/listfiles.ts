import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type ListFilesParam = {
  datasource: string;
  path?: string;
};

export const getListFiles = async (params: ListFilesParam) => {
  const baseOldUrl = `/api/gsql-server/gsql/listfiles?datasource=${
    params.datasource
  }${params.path ? `&path=${params.path}` : ''}`;
  const baseNewUrl = `/api/gsql-server/gsql/v1/list-files/${params.datasource}${
    params.path ? `?path="${params.path}"` : ''
  }`;

  if (getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(baseNewUrl);

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(baseOldUrl);

    return res;
  }
};
