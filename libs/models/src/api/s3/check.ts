import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type S3PayloadParam = {
  type: string;
  config: any;
};

export const checkS3 = async (params: S3PayloadParam) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/v1/ds-credentials',
      {
        params: {
          type: params?.type,
          config: params?.config,
        },
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/check/ds-credentials',
      {
        params: {
          type: params?.type,
          config: params?.config,
        },
      }
    );

    return res;
  }
};
