import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export const getHelp = async () => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/v1/help'
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<string>('/api/gsql-server/gsql/help');

    return res;
  }
};
