import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export const clearStore = async () => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/clear-store'
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/clear-store'
    );

    return res;
  }
};
