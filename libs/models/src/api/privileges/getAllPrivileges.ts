import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GetAllPrivilegesParams = {
  roles: string[]
}

export const getAllPrivileges = async (params?: GetAllPrivilegesParams) => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().get('/api/gsql-server/gsql/v1/privileges', {
      params: {
        roles: params?.roles.join(',')
      }
    })
    return res;
  } else {
    const res = getAxiosInstance().get('/api/gsql-server/gsql/privileges', {
      params: {
        roles: params?.roles.join(',')
      }
    })
    return res;
  }
};
