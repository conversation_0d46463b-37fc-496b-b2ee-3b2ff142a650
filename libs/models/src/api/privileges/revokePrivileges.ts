import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

const paramsSerializer = (params?: Record<string, string | string[]>) => {
  return Object.keys(params).reduce((res, key) => {
    const paramsValue = params[key];
    let paramValueString: string;
    if (Array.isArray(paramsValue)) {
      paramValueString = paramsValue.map(val => `${key}=${val}`).join('&');
    } else if (typeof paramsValue === 'string') {
      paramValueString = `${key}=${paramsValue}`;
    }
    return [res, paramValueString].filter(Boolean).join('&');
  }, '')
};

export type RevokePrivilegesParams = {
  privileges: any;
  vertexName?: string;
  edgeName?: string;
  attrName?: string;
  roleName: string;
  graph?: string;
  queryName?: string;
};

export const revokePrivileges = async ({ graph, roleName, privileges, vertexName, edgeName, attrName, queryName }: RevokePrivilegesParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/privileges/revoke',
      {
        vertexName: vertexName ? [vertexName] : undefined,
        edgeName: edgeName ? [edgeName] : undefined,
        attrNames: attrName ? [attrName] : undefined,
        queryName: queryName ? [queryName] : undefined,
        privileges,
        roles: [roleName],
      },
      {
        params: {
          graph,
        },
        paramsSerializer,
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().delete('/api/gsql-server/gsql/role', {
      params: {
        vertexName,
        edgeName,
        attrNames: attrName,
        privilege: privileges,
        role: roleName,
        graph: graph,
      },
      paramsSerializer,
    });
    return res;
  }
};
