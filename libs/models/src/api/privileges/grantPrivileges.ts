import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GrantPrivilegesParams = {
  privileges: any;
  vertexName?: string;
  edgeName?: string;
  attrName?: string;
  roleName: string;
  graph?: string;
  queryName?: string;
};

export const grantPrivileges = async ({ graph, roleName, privileges, vertexName, edgeName, attrName, queryName }: GrantPrivilegesParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/privileges/grant',
      {
        vertexName: vertexName ? [vertexName] : undefined,
        edgeName: edgeName ? [edgeName] : undefined,
        attrNames: attrName ? [attrName] : undefined,
        queryName: queryName ? [queryName] : undefined,
        privileges,
        roles: [roleName],
      },
      {
        params: {
          graph,
        }
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/role',
      {
        vertexName: vertexName ? [vertexName] : undefined,
        edgeName: edgeName ? [edgeName] : undefined,
        attrNames: attrName ? [attrName] : undefined,
        privileges,
        roles: [roleName],
      },
      {
        params: {
          graph,
        },
      },
    );
    return res;
  }
};
