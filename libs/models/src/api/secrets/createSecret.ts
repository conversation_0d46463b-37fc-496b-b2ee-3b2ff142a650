import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type CreateSecretParams = {
  graph?: string;
  alias: string;
};

export const createSecret = async (params?: CreateSecretParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/v1/secrets`,
      null,
      {
        params,
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/secrets',
      null,
      {
        params,
      }
    );

    return res;
  }
};
