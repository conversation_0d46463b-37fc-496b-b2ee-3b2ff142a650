import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type DeleteSecretParams = {
  graph?: string;
  alias: string;
};

export const deleteSecret = async (params?: DeleteSecretParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().delete<Result>(
      `/api/gsql-server/gsql/v1/secrets`,
      {
        data: {
          secrets: [params.alias]
        }
      },
    );
    return res;
  } else {
    const res = await getAxiosInstance().delete<Result>(
      '/api/gsql-server/gsql/secrets',
      {
        params,
      }
    );

    return res;
  }
};
