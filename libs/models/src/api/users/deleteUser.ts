import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type DeleteUserParams = {
  user: string;
};

export const deleteUser = async (params: DeleteUserParams) => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().delete(
      `/api/gsql-server/gsql/scim/v2/Users/<USER>
      {
        params: {
          gsqlFormat: true,
        }
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().delete('/api/gsql-server/gsql/users', {
      params: {
        name: params.user,
      },
    });
    return res;
  }
};
