import { getVersionIsGTE410 } from '../version';
import { getAxiosInstance } from '../request';
import { Result } from '../types';

export type CreateUserParams = {
  name: string;
  password: string;
};

export const createUser = async (params: CreateUserParams) => {
    if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>('/api/gsql-server/gsql/scim/v2/Users', {
      username: params.name,
      password: params.password,
    }, {
      params: {
        gsqlFormat: true,
      }
    });

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>('/api/gsql-server/gsql/users', params);

    return res;
  }
};
