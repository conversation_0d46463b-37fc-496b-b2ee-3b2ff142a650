import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GetUserListParams = {
  graph?: string;
};

export const getUserList = async (params: GetUserListParams) => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().get('/api/gsql-server/gsql/scim/v2/Users', {
      params: {
        ...params,
        gsqlFormat: true,
      },
    });
    return res;
  } else {
    const res = getAxiosInstance().get('/api/gsql-server/gsql/users', {
      params,
    });
    return res;
  }
};
