import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type ChangeUserParams = {
  name: string;
  password: string;
};

export const changeUser = async (params: ChangeUserParams) => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().put(
      `/api/gsql-server/gsql/scim/v2/Users/<USER>
      {
        password: params.password,
      },
      {
        params: {
          gsqlFormat: true,
        }
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().put('/api/gsql-server/gsql/users', params);
    return res;
  }
};
