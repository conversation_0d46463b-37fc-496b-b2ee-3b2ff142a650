import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type RunInterpretParams = {
  queryBody: string;
  params: any;
  headers?: any;
};

export const runInterpret = async ({
  queryBody,
  params,
  headers,
}: RunInterpretParams) => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/queries/interpret',
      queryBody,
      {
        params,
        headers: {
          ...headers,
          'Content-Type': 'text/plain',
        },
      },
    );
    return res;
  } else {
    const res = getAxiosInstance().post(
      '/api/gsql-server/interpreted_query',
      queryBody,
      {
        params,
        headers: {
          ...headers,
          'Content-Type': 'text/plain',
        },
      }
    );
    return res;
  }
};
