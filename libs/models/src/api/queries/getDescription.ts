import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export const getDescription = async () => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().get(
      '/api/gsql-server/gsql/v1/queries/description'
    );
    return res;
  } else {
    const res = getAxiosInstance().get('/api/gsql-server/gsql/description');
    return res;
  }
};
