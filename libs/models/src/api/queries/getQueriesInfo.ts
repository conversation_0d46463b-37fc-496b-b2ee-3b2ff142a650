import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GetQueriesInfoParams = {
  queryBody: string;
  queryParams?: Record<string, string | number>
};

export const getQueriesInfo = async ({ queryBody, queryParams }: GetQueriesInfoParams) => {
  if (await getVersionIsGTE410()) {
    const res = getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/queries/interpret/query/info',
      queryBody,
      {
        headers: {
          'Content-Type': 'text/plain',
        },
        params: {
          getParams: true,
          ...queryParams,
        },
      }
    );
    return res;
  } else {
    const res = getAxiosInstance().post(
      '/api/gsql-server/interpreted_query/info',
      queryBody,
      {
        headers: {
          'Content-Type': 'text/plain',
        },
        params: {
          getParams: true,
          ...queryParams,
        },
      }
    );
    return res;
  }
};
