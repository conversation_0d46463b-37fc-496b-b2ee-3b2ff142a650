import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type GrantRolesParams = {
  graph: string;
  roles: string[];
  usernames: string[];
};

export const grantRoles = async (params: GrantRolesParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/roles/grant',
      {
        roles: params.roles,
        users: params.usernames,
      },
      {
        params: {
          graph: params.graph,
        },
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/user',
      {
        roles: params.roles,
        usernames: params.usernames,
      },
      {
        params: {
          graph: params.graph,
        },
      }
    );
    return res;
  }
};
