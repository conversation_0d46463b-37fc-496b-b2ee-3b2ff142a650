import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type RevokeRolesParams = {
  graph: string;
  roles: string[];
  usernames: string[];
};

export const revokeRoles = async (params: RevokeRolesParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post(
      '/api/gsql-server/gsql/v1/roles/revoke',
      {
        roles: params.roles,
        users: params.usernames,
      },
      {
        params: {
          graph: params.graph,
        },
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().delete('/api/gsql-server/gsql/user', {
      params: {
        graph: params.graph,
        role: params.roles[0],
        usernames: params.usernames,
      },
    });
    return res;
  }
};
