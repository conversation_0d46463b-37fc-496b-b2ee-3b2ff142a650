import { getVersionIsGTE410 } from '../version';
import { getAxiosInstance } from '../request';
import { Result } from '../types';

export type DeleteRoleParam = {
  roleName: string;
  graph?: string;
};

export const deleteRole = async (params: DeleteRoleParam) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().delete<Result>(
      `/api/gsql-server/gsql/v1/roles/${params.roleName}`,
      {
        params: {
          graph: params.graph,
        },
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().delete<Result>(
      '/api/gsql-server/gsql/roles',
      {
        params: {
          role: params.roleName,
          graph: params.graph,
        },
      }
    );

    return res;
  }
};
