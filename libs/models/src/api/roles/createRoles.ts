import { getAxiosInstance } from '../request';
import { Result } from '../types';
import { getVersionIsGTE410 } from '../version';

export type CreateRoleParams = {
  graph?: string;
  roleName: string;
};

export const createRoles = async (params?: CreateRoleParams) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/gsql/v1/roles`,
      {
        roles: [params.roleName],
      },
      {
        params: {
          graph: params.graph,
        },
      }
    );
    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/roles',
      {
        roles: [params.roleName],
      },
      {
        params: {
          graph: params.graph,
        },
      }
    );

    return res;
  }
};
