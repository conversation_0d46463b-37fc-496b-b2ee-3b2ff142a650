import { getAxiosInstance } from './request';
import { AxiosResponse } from 'axios';
import { compare } from 'compare-versions';

export type IVersionResponse = {
  error: boolean;
  message: string;
  results: {
    tigergraph_version: string;
  };
};

let versionPromise: Promise<AxiosResponse<IVersionResponse>>

export const getVersion = async () => {
  let version: string
  if (versionPromise instanceof Promise) {
    // using cached version api
  } else {
    versionPromise = getAxiosInstance().get<IVersionResponse>('/api/version');
  }

  version = (await versionPromise).data.results.tigergraph_version;

  return version;
};

export const getVersionIsGTE410 = async () => {
  const version = await getVersion();

  return compare(version, '4.1.0', '>=');
};
