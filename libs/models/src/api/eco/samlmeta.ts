import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export const getSAMLMeta = async () => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/v1/saml/meta'
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<string>(
      '/api/gsql-server/gsql/saml/meta'
    );

    return res;
  }
};