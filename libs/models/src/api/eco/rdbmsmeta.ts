import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export type RDBMSMetaParam = {
  type: string;
  server: string;
  port: number | string;
  database: string;
  username: string;
  password: string;
};

export const getRDBMSMeta = async ({ username, password, ...params }: RDBMSMetaParam) => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().post<Result>(
      '/api/gsql-server/gsql/v1/rdbms/meta',
      {
        username,
        password,
      },
      {
        params
      }
    );

    return res;
  } else {
    const res = await getAxiosInstance().post<Result>(
      `/api/gsql-server/rdbms/meta`,
      {
        username,
        password,
      },
      {
        params,
      }
    );

    return res;
  }
};
