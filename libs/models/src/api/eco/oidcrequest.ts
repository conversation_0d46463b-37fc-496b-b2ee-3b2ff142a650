import { Result } from '../types';
import { getAxiosInstance } from '../request';
import { getVersionIsGTE410 } from '../version';

export const getOIDCRequest = async () => {
  if (await getVersionIsGTE410()) {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/v1/oidc/authnrequest'
    );

    return res;
  } else {
    const res = await getAxiosInstance().get<Result>(
      '/api/gsql-server/gsql/oidc/authnrequest'
    );

    return res;
  }
};