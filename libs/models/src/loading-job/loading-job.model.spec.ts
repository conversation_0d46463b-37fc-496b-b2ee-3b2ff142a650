import { GraphStudioError } from '../error';
import { Attribute, Edge, Graph, Vertex } from '../topology';

import { DBLoadingJob<PERSON>son, } from './db-loading-job.interface';
import {
  GSQLUDFJson, GSQLLoadingJobJson,
  GSQLLoadToEdgeJson, GSQLLoadToVertexJson
} from './gsql-loading-job.interface';
import { LoadingJobData, LoadingJobLogic, LoadingJobPrefix } from './loading-job.model';
import {
  LoadingMappingLogic, SourceType,
  OneColumnMappingTargetType,
  OneColumnMappingGraphEntityTarget,
  OneColumnMappingMappingWidgetTarget,
  TokenFunction,
  TokenFunctionSignature,
  builtinTokenFunctionNames,
  builtinTokenFunctionReturnType,
  builtinTokenFunctionParamNumber,
  builtinTokenFunctionDocument
} from './loading-mapping.model';
import { LoadToEdgeData, LoadToEdgeLogic } from './load-to-edge.model';
import { LoadToVertexData, LoadToVertexLogic } from './load-to-vertex.model';
import { DataSource, DataSourceType } from './data-source.interface';
import {
  TabularParsingOptions, JSONParsingOptions,
  DataSet, DataFormat, FileFormat,
  JSONDataType, JSONDataFieldSchema,
} from './data-set.interface';
import { combinedTokenFunctionList } from './loading-mapping.model'; 

describe('GSQLLoadingJobModel', () => {
  let dbLoadingJobJson: DBLoadingJobJson;
  let dbLoadingJobJson2: DBLoadingJobJson;
  let dbJSONLoadingJobJson: DBLoadingJobJson;
  let mockGsqlLoadingJobJson: GSQLLoadingJobJson;
  let mockGsqlLoadingJobJson2: GSQLLoadingJobJson;
  let mockJSONGsqlLoadingJobJson: GSQLLoadingJobJson;

  let theSchema: Graph;

  beforeEach(() => {
    combinedTokenFunctionList.splice(0, combinedTokenFunctionList.length,
      ...builtinTokenFunctionNames.map(tokenName => <TokenFunctionSignature>(
        {
          name: tokenName,
          returnType: builtinTokenFunctionReturnType.get(tokenName),
          paramNumber: builtinTokenFunctionParamNumber.get(tokenName) || 1,
          paramNumberFixed: builtinTokenFunctionParamNumber.get(tokenName) !== undefined,
          doc: builtinTokenFunctionDocument.get(tokenName),
        }
      )));
    dbLoadingJobJson = {
      loadingJobName: 'load_job',
      loadingStatementsStyle: [
        {
          sourcePosition: {
            x: 0.2, y: 0.5
          },
          targetPosition: {
            x: 0.8, y: 0.5
          },
          middlePositions: []
        },
        {
          sourcePosition: {
            x: 0.2, y: 0.5
          },
          targetPosition: {
            x: 0.8, y: 0.5
          },
          middlePositions: []
        },
        {
          sourcePosition: {
            x: 0.2, y: 0.5
          },
          targetPosition: {
            x: 0.8, y: 0.5
          },
          middlePositions: []
        }
      ],
      dataSourceJson: {
        type: 'file',
        uri: '/resources/data_set/gsql/persons.csv',
        options: {
          fileFormat: FileFormat.None,
          eol: '\n',
          separator: ',',
          header: 'true'
        },
        position: {
          x: 0.5,
          y: 0.2
        }
      },
      header: ['name', 'age', 'col1', 'col2'],
      sampleData: [
        ['src', '25', '', ''],
        ['src2', '26', '', '']
      ]
    };

    mockGsqlLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      FileNames: {
        f: ''
      },
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        },
        {
          Type: 'Edge',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'SrcColIndex',
              Value: 3
            }
          ],
          TargetName: 'person_movie',
          FromVertexType: 'person',
          ToVertexType: 'movie',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        },
        {
          Type: 'Vertex',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 2
            },
            {
              Type: 'SrcColIndex',
              Value: 3
            }
          ],
          TargetName: 'movie',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        }
      ]
    };

    dbLoadingJobJson2 = {
      loadingJobName: 'load_job',
      loadingStatementsStyle: [
        {
          sourcePosition: {
            x: 0.2, y: 0.5
          },
          targetPosition: {
            x: 0.8, y: 0.5
          },
          middlePositions: []
        },
        {
          sourcePosition: {
            x: 0.2, y: 0.5
          },
          targetPosition: {
            x: 0.8, y: 0.5
          },
          middlePositions: [
            {
              x: 0.4, y: 0.5
            },
            {
              x: 0.6, y: 0.5
            }
          ]
        }
      ],
      dataSourceJson: {
        type: 'file',
        uri: '/resources/data_set/gsql/persons.csv',
        options: {
          fileFormat: FileFormat.None,
          eol: '\n',
          separator: ',',
          header: 'true'
        },
        position: {
          x: 0.5,
          y: 0.2
        }
      },
      header: ['name', 'age', 'col1', 'col2'],
      sampleData: [
        ['src', '25', '', ''],
        ['src2', '26', '', '']
      ]
    };

    mockGsqlLoadingJobJson2 = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      FileNames: {
        f: ''
      },
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'FileVar',
            Value: 'f',
          }
        },
        {
          Type: 'Edge',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'UDF',
              Params: [
                {
                  Type: 'SrcColIndex',
                  Value: 1
                },
                {
                  Type: 'UDF',
                  Params: [
                    {
                      Type: 'SrcColIndex',
                      Value: 2
                    },
                    {
                      Type: 'Literal',
                      Value: 'a'
                    },
                    {
                      Type: 'Default'
                    }
                  ],
                  UdfName: 'gsql_concat'
                }
              ],
              UdfName: 'gsql_concat'
            },
            {
              Type: 'SrcColIndex',
              Value: 3
            }
          ],
          TargetName: 'person_movie',
          FromVertexType: 'person',
          ToVertexType: 'movie',
          DataSource: {
            Type: 'FileVar',
            Value: 'f',
          }
        }
      ]
    };

    mockJSONGsqlLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'json_load_job',
      FileNames: {
        MyDataSource: ''
      },
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {
            HEADER: 'false',
            SEPARATOR: ',',
            EOL: '\\n',
            JSON_FILE: 'true'
          },
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'FileVar',
            Value: 'MyDataSource',
          }
        },
        {
          Type: 'Edge',
          UsingClauses: {
            HEADER: 'false',
            SEPARATOR: ',',
            EOL: '\\n',
            JSON_FILE: 'true'
          },
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'UDF',
              Params: [
                {
                  Type: 'SrcColIndex',
                  Value: 1
                },
                {
                  Type: 'UDF',
                  Params: [
                    {
                      Type: 'SrcColIndex',
                      Value: 2
                    },
                    {
                      Type: 'Literal',
                      Value: 'a'
                    },
                    {
                      Type: 'Default'
                    }
                  ],
                  UdfName: 'gsql_concat'
                }
              ],
              UdfName: 'gsql_concat'
            },
            {
              Type: 'SrcColIndex',
              Value: 3
            }
          ],
          TargetName: 'person_movie',
          FromVertexType: 'person',
          ToVertexType: 'movie',
          DataSource: {
            Type: 'FileVar',
            Value: 'MyDataSource',
          }
        }
      ]
    };

    dbJSONLoadingJobJson = {
      loadingJobName: 'json_load_job',
      loadingStatementsStyle: [
        {
          sourcePosition: {
            x: 0.2, y: 0.5
          },
          targetPosition: {
            x: 0.8, y: 0.5
          },
          middlePositions: []
        },
        {
          sourcePosition: {
            x: 0.2, y: 0.5
          },
          targetPosition: {
            x: 0.8, y: 0.5
          },
          middlePositions: [
            {
              x: 0.4, y: 0.5
            },
            {
              x: 0.6, y: 0.5
            }
          ]
        }
      ],
      dataSetJson: {
        dataFormat: DataFormat.JSON,
        dataSchema: <JSONDataFieldSchema[]>[
          {
            level: 0,
            name: 'name',
            path: [
                'name'
            ],
            type: JSONDataType.String
          },
          {
            level: 0,
            name: 'age',
            path: [
                'age'
            ],
            type: JSONDataType.Number
          },
        ]
      },
      dataSourceJson: {
        type: 'file',
        uri: '/resources/data_set/gsql/persons.json',
        options: {
          fileFormat: FileFormat.None,
          eol: '\\n',
          separator: '',
          header: 'false',
          quote: ''
        },
        position: {
          x: 0.5,
          y: 0.2
        }
      }
    };

    theSchema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    vertex.primaryId = new Attribute().loadFromGSQLJson({
      AttributeName: 'id',
      AttributeType: {
        Name: 'INT'
      }
    });
    vertex.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'movie' }];
    const vertex2 = new Vertex();
    vertex2.name = 'movie';
    vertex2.primaryId = new Attribute().loadFromGSQLJson({
      AttributeName: 'id',
      AttributeType: {
        Name: 'STRING'
      }
    });
    vertex2.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'STRING'
      }
    }));
    theSchema.vertexTypes.push(vertex);
    theSchema.vertexTypes.push(vertex2);
    theSchema.edgeTypes.push(edge);
  });

  it('should parse fail because it is offline job.', () => {
    const gsqlLoadingJobJson: GSQLLoadingJobJson = {
      Type: 'Online',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {
            SEPARATOR: ',',
            HEADER: 'true'
          },
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'Online'
          }
        }
      ]
    };

    const validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe('Online post job is deprecated in Graph Studio.');
  });

  it('should parse fail because it contains user defined inline filters.', () => {
    const gsqlLoadingJobJson: GSQLLoadingJobJson = {
      Type: 'Offline',
      Filters: ['f1 = to_int($0) > 1'],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      FileNames: {
        f: ''
      },
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        }
      ]
    };

    const validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message)
    .toBe('User defined inline filters is not supported in Graph Studio.');
  });

  it('should parse fail because it contains user defined headers.', () => {
    const gsqlLoadingJobJson: GSQLLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {h1: ['name', 'age']},
      JobName: 'load_job',
      FileNames: {
        f: ''
      },
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        }
      ]
    };

    const validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe('User defined header is not supported in Graph Studio.');
  });

  it('should parse fail because load to temp table, delete vertex | edge is not supported.', () => {
    let gsqlLoadingJobJson: GSQLLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      FileNames: {
        f: ''
      },
      LoadingStatements: [
        {
          Type: 'TempTable',
          UsingClauses: {},
          ColumnNames: ['t1', 't2'],
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'temp1',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        }
      ]
    };
    let validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe('Load to temp table is not supported in Graph Studio.');

    gsqlLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      LoadingStatements: [
        {
          Type: 'DeleteVertex',
          UsingClauses: {},
          PrimaryId: {
            Type: 'SrcColIndex',
            Value: 0
          },
          VertexType: 'company'
        }
      ]
    };
    validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe('Delete vertex is not supported in Graph Studio.');

    gsqlLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      LoadingStatements: [
        {
          Type: 'DeleteEdge',
          UsingClauses: {},
          FromVertexPrimaryId: {
            Type: 'SrcColIndex',
            Value: 0
          },
          ToVertexPrimaryId: {
            Type: 'SrcColIndex',
            Value: 1
          },
          EdgeType: 'member_work_company',
          FromVertexType: 'members',
          ToVertexType: 'company'
        }
      ]
    };
    validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe('Delete edge is not supported in Graph Studio.');

    gsqlLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      FileNames: {
        f: ''
      },
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'FileVar',
            Value: 'f',
          }
        },
        {
          Type: 'WhatIsThis'
        }
      ]
    };
    validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe('"WhatIsThis" is invalid loading statement type.');

  });

  it('should parse fail because load to vertex | edge contains unsupported features.', () => {
    let gsqlLoadingJobJson: GSQLLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      FileNames: {
        f: ''
      },
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'File',
            Value: '/resources/data_set/gsql/persons.csv'
          }
        },
        {
          Type: 'Edge',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'SrcColIndex',
              Value: 3
            }
          ],
          TargetName: 'person_movie',
          FromVertexType: 'person',
          ToVertexType: 'movie',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        }
      ]
    };
    let validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe('Data source type "File" ' +
        'is not supported in Graph Studio.');

    gsqlLoadingJobJson = {
      Type: 'Offline',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      FileNames: {
        f: ''
      },
      LoadingStatements: [
        {
          Type: 'Vertex',
          UsingClauses: {},
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            }
          ],
          TargetName: 'person',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        },
        {
          Type: 'Edge',
          UsingClauses: {},
          WhereClause: '$1 != "123"',
          Mappings: [
            {
              Type: 'SrcColIndex',
              Value: 0
            },
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'SrcColIndex',
              Value: 3
            }
          ],
          TargetName: 'person_movie',
          FromVertexType: 'person',
          ToVertexType: 'movie',
          DataSource: {
            Type: 'FileVar',
            Value: 'f'
          }
        }
      ]
    };
    validationCheck = LoadingJobLogic.validationCheck(gsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe(`Loading statement number saved in engine and backend side don't match.`);
  });

  it('should fail loading job validation check because GSQL loading statement number and ' +
  'DB loading statement number not match.', () => {
    const mockGsqlLoadingJobJson3: GSQLLoadingJobJson = JSON.parse(JSON.stringify(mockGsqlLoadingJobJson));
    mockGsqlLoadingJobJson3.LoadingStatements.splice(0, 1);
    const validationCheck = LoadingJobLogic
      .validationCheck(mockGsqlLoadingJobJson2, dbLoadingJobJson);
    expect(validationCheck.success).toBeFalsy();
    expect(validationCheck.message).toBe('Loading statement number saved in engine and backend side don\'t match.');
  });

  it('should pass loading job validation check.', () => {
    const validationCheck = LoadingJobLogic
      .validationCheck(mockGsqlLoadingJobJson, dbLoadingJobJson);
    expect(validationCheck.success).toBeTruthy();
  });

  it('should load CSV loading job into in-memory loading job successfully', () => {
    const loadingJob: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    // Job name.
    expect(loadingJob.jobName).toBe(mockGsqlLoadingJobJson.JobName);

    // Data source.
    expect(loadingJob.dataSource.type).toBe(DataSourceType.File);

    // Data set.
    expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.CSV);
    const dataSchema = loadingJob.dataSet.dataSchema;
    expect(dataSchema.length).toBe(4);
    expect(dataSchema[0].name).toBe('name');
    expect(dataSchema[1].name).toBe('age');
    const sampleData = loadingJob.dataSet.sampleData;
    expect(sampleData[0][0]).toBe('src');
    expect(sampleData[0][1]).toBe('25');
    expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');

    // Loading statement.
    expect(loadingJob.loadingStatements.length).toBe(3);
    expect('vertexName' in loadingJob.loadingStatements[0]).toBeTruthy();
    expect('edgeName' in loadingJob.loadingStatements[1]).toBeTruthy();
    expect('vertexName' in loadingJob.loadingStatements[2]).toBeTruthy();
  });

  it('should load JSON loading job into in-memory loading job successfully', () => {
    const expectedJSONUsingClauses = {
      EOL: '\\n',
      JSON_FILE: 'true'
    };

    const loadingJob: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockJSONGsqlLoadingJobJson, dbJSONLoadingJobJson);

    // Job name.
    expect(loadingJob.jobName).toBe(mockJSONGsqlLoadingJobJson.JobName);

    // Data source.
    expect(loadingJob.dataSource.type).toBe(DataSourceType.File);

    // Data set.
    expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.JSON);
    const dataSchema = loadingJob.dataSet.dataSchema;
    expect(dataSchema.length).toBe(2);
    expect(dataSchema[0].name).toBe('name');
    expect(dataSchema[1].name).toBe('age');
    expect(loadingJob.dataSet.sampleData).toBeUndefined();
    expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.json');
    expect(loadingJob.dataSet.position).toEqual(dbJSONLoadingJobJson.dataSourceJson.position);

    // Loading statement.
    expect(loadingJob.loadingStatements.length).toBe(2);
    expect('vertexName' in loadingJob.loadingStatements[0]).toBeTruthy();
    expect(loadingJob.loadingStatements[0].usingClauses).toEqual(expectedJSONUsingClauses);
    expect('edgeName' in loadingJob.loadingStatements[1]).toBeTruthy();
    expect(loadingJob.loadingStatements[1].usingClauses).toEqual(expectedJSONUsingClauses);
  });

  it('should clone loading job successfully', () => {
    const loadingJobOriginal: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    const loadingJob = LoadingJobLogic.clone(loadingJobOriginal);

    // Job name.
    expect(loadingJob.jobName).toBe(mockGsqlLoadingJobJson.JobName);

    // Data source.
    expect(loadingJob.dataSource.type).toBe(DataSourceType.File);

    // Data set.
    expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.CSV);
    const dataSchema = loadingJob.dataSet.dataSchema;
    expect(dataSchema.length).toBe(4);
    expect(dataSchema[0].name).toBe('name');
    expect(dataSchema[1].name).toBe('age');
    expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');

    // Loading statement.
    expect(loadingJob.loadingStatements.length).toBe(3);
    expect('vertexName' in loadingJob.loadingStatements[0]).toBeTruthy();
    expect('edgeName' in loadingJob.loadingStatements[1]).toBeTruthy();
    expect('vertexName' in loadingJob.loadingStatements[2]).toBeTruthy();
  });

  it('should fail semantic check because one load to vertex and one load to edge ' +
    'semantic check failed', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    vertex.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    const semanticCheckResult = LoadingJobLogic.semanticCheck(loadingJobData, schema);
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message.length).toBe(3);
    expect(semanticCheckResult.message[0]).toBe('');
    expect(semanticCheckResult.message[1])
      .toBe('Data loading mapping number and edge attribute number don\'t match.');
    expect(semanticCheckResult.message[2])
      .toBe('Vertex type "movie" doesn\'t exist in graph schema.');
  });

  it('should pass semantic check', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    vertex.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'movie' }];
    const vertex2 = new Vertex();
    vertex2.name = 'movie';
    vertex2.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'STRING'
      }
    }));
    schema.vertexTypes.push(vertex);
    schema.vertexTypes.push(vertex2);
    schema.edgeTypes.push(edge);

    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    const semanticCheckResult = LoadingJobLogic.semanticCheck(loadingJobData, schema);
    expect(semanticCheckResult.success).toBeTruthy();
    expect(semanticCheckResult.message.length).toBe(3);
    expect(semanticCheckResult.message[0]).toBe('');
    expect(semanticCheckResult.message[1]).toBe('');
    expect(semanticCheckResult.message[2]).toBe('');
  });

  it('should dump to gsql and db exactly same as origin', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    vertex.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'movie' }];
    const vertex2 = new Vertex();
    vertex2.name = 'movie';
    vertex2.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'STRING'
      }
    }));
    schema.vertexTypes.push(vertex);
    schema.vertexTypes.push(vertex2);
    schema.edgeTypes.push(edge);

    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson2, dbLoadingJobJson2);

    let dumpGSQLJson: GSQLLoadingJobJson;
    let dumpDBJson: DBLoadingJobJson;
    [dumpGSQLJson, dumpDBJson] = LoadingJobLogic.dumpToGSQLAndDB(loadingJobData, schema);

    // Dump result should be same as input
    expect(dumpGSQLJson.Type).toBe('Offline');
    expect(dumpGSQLJson.GraphName).toBe('MyGraph');
    expect(dumpGSQLJson.JobName).toBe('load_job');
    expect(dumpGSQLJson.Filters.length).toBe(0);
    expect(Object.keys(dumpGSQLJson.Headers).length).toBe(0);
    expect(dumpGSQLJson.LoadingStatements.length).toBe(2);
    expect(dumpGSQLJson.LoadingStatements[0].Type).toBe('Vertex');
    expect(Object.keys(<GSQLLoadToVertexJson>dumpGSQLJson.LoadingStatements[0]
      .UsingClauses).length).toBe(0);
    expect((<GSQLLoadToVertexJson>dumpGSQLJson.LoadingStatements[0]).TargetName).toBe('person');
    expect((<GSQLLoadToVertexJson>dumpGSQLJson.LoadingStatements[0]).DataSource.Type).toBe('FileVar');
    expect((<GSQLLoadToVertexJson>dumpGSQLJson.LoadingStatements[0]).Mappings[1].Value).toBe(1);

    expect(dumpGSQLJson.LoadingStatements[1].Type).toBe('Edge');
    expect(Object.keys(<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]
      .UsingClauses).length).toBe(0);
    expect((<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]).TargetName).toBe('person_movie');
    expect((<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]).DataSource.Type).toBe('FileVar');
    expect((<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]).FromVertexType).toBe('person');
    expect((<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]).ToVertexType).toBe('movie');
    expect((<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]).Mappings[0].Value).toBe(0);
    expect((<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]).Mappings[1].Type).toBe('UDF');
    expect((<GSQLUDFJson>(<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1])
      .Mappings[1]).UdfName).toBe('gsql_concat');
    expect((<GSQLUDFJson>(<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]).Mappings[1])
      .Params[1].Type).toBe('UDF');
    expect((<GSQLUDFJson>(<GSQLUDFJson>(<GSQLLoadToEdgeJson>
      dumpGSQLJson.LoadingStatements[1]).Mappings[1]).Params[1]).Params[0].Type).toBe('SrcColIndex');
    expect((<GSQLUDFJson>(<GSQLUDFJson>(<GSQLLoadToEdgeJson>
      dumpGSQLJson.LoadingStatements[1]).Mappings[1]).Params[1]).Params[1].Type).toBe('Literal');
    expect((<GSQLUDFJson>(<GSQLUDFJson>(<GSQLLoadToEdgeJson>
      dumpGSQLJson.LoadingStatements[1]).Mappings[1]).Params[1]).Params[2].Type).toBe('Default');
    expect((<GSQLLoadToEdgeJson>dumpGSQLJson.LoadingStatements[1]).Mappings[2].Value).toBe(3);

    expect(dumpDBJson.loadingJobName).toBe('load_job');
    expect(dumpDBJson.header.length).toBe(4);
    expect(dumpDBJson.header[2]).toBe('col1');
    expect(dumpDBJson.sampleData.length).toBe(2);
    expect(dumpDBJson.sampleData[0][1]).toBe('25');
    expect(dumpDBJson.sampleData[1][0]).toBe('src2');
    expect(dumpDBJson.dataSourceJson.uri).toBe('/resources/data_set/gsql/persons.csv');
    expect(dumpDBJson.loadingStatementsStyle.length).toBe(2);
    expect(dumpDBJson.loadingStatementsStyle[1].middlePositions.length).toBe(2);
    expect(dumpDBJson.loadingStatementsStyle[1].middlePositions[0].x).toBe(0.4);
    expect(dumpDBJson.loadingStatementsStyle[1].middlePositions[1].x).toBe(0.6);
  });

  it('should load empty loading job successfully', () => {
    const loadingJob: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(undefined, dbLoadingJobJson);

    // Job name.
    expect(loadingJob.jobName).toBe(mockGsqlLoadingJobJson.JobName);

    // Data source.
    expect(loadingJob.dataSource.type).toBe(DataSourceType.File);

    // Data set.
    expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.CSV);
    const dataSchema = loadingJob.dataSet.dataSchema;
    expect(dataSchema.length).toBe(4);
    expect(dataSchema[0].name).toBe('name');
    expect(dataSchema[1].name).toBe('age');
    expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');

    // Loading statement
    expect(loadingJob.loadingStatements.length).toBe(0);
  });

  it('should dump to gsql as undefined, and dump to db correctly', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    vertex.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'INT'
      }
    }));
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'movie' }];
    const vertex2 = new Vertex();
    vertex2.name = 'movie';
    vertex2.attributes.push(new Attribute().loadFromGSQLJson({
      AttributeName: 'name',
      AttributeType: {
        Name: 'STRING'
      }
    }));
    schema.vertexTypes.push(vertex);
    schema.vertexTypes.push(vertex2);
    schema.edgeTypes.push(edge);

    dbLoadingJobJson = {
      loadingJobName: 'load_job',
      loadingStatementsStyle: [ ],
      dataSourceJson: {
        type: 'file',
        uri: '/resources/data_set/gsql/persons.csv',
        options: {
          fileFormat: FileFormat.None,
          eol: '\n',
          separator: ',',
          header: 'true'
        },
        position: {
          x: 0.5,
          y: 0.2
        }
      },
      header: ['name', 'age', 'col1', 'col2'],
      sampleData: [
        ['src', '25', '', ''],
        ['src2', '26', '', '']
      ]
    };

    mockGsqlLoadingJobJson = {
      Type: 'Online',
      Filters: [],
      GraphName: 'person_movie',
      Headers: {},
      JobName: 'load_job',
      LoadingStatements: [ ]
    };

    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    let dumpGSQLJson: GSQLLoadingJobJson;
    let dumpDBJson: DBLoadingJobJson;
    [dumpGSQLJson, dumpDBJson] = LoadingJobLogic.dumpToGSQLAndDB(loadingJobData, schema);

    // GSQL dump result should be undefined
    expect(dumpGSQLJson).toBeUndefined();

    // DB dump result should be correct
    expect(dumpDBJson.loadingJobName).toBe('load_job');
    expect(dumpDBJson.header.length).toBe(4);
    expect(dumpDBJson.header[2]).toBe('col1');
    expect(dumpDBJson.sampleData.length).toBe(2);
    expect(dumpDBJson.sampleData[0][1]).toBe('25');
    expect(dumpDBJson.sampleData[1][0]).toBe('src2');
    expect(dumpDBJson.sampleData[1][2]).toBe('');
    expect(dumpDBJson.dataSourceJson.uri).toBe('/resources/data_set/gsql/persons.csv');
    expect(dumpDBJson.loadingStatementsStyle.length).toBe(0);
  });

  describe('should create loading job correctly', () => {
    const mockFileDataSource: DataSource = {
      type: DataSourceType.File,
      isLocal: true,
    };

    const mockS3DataSource: DataSource = {
      type: DataSourceType.S3,
      name: 'myS3Connection',
      isLocal: true,
    };

    const mockGCSDataSource: DataSource = {
      type: DataSourceType.GoogleCloudStorage,
      name: 'myGCSConnection',
      isLocal: true,
    };

    const mockABSDataSource: DataSource = {
      type: DataSourceType.AzureBlobStorage,
      name: 'myABSConnection',
      isLocal: true,
    };

    describe('with CSV data format', () => {
      const mockCSVFile: DataSet = {
        uri: '/resources/data_set/gsql/persons.csv',
        parsingOptions: {
          fileFormat: FileFormat.None,
          separator: ',',
          eol: '\\n',
          header: 'true',
        },
        dataFormat: DataFormat.CSV,
        dataSchema: [
          {
            name: 'c1',
          },
          {
            name: 'c2',
          },
        ],
        position: {
          x: 0.5,
          y: 0.2
        }
      };

      it('with local file', () => {
        const loadingJob = LoadingJobLogic.createLoadingJob(
          'file_load_job',
          mockFileDataSource,
          mockCSVFile,
          [],
        );

        // Job name.
        expect(loadingJob.jobName).toBe('file_load_job');

        // Data source.
        expect(loadingJob.dataSource.name).toBe(undefined);
        expect(loadingJob.dataSource.type).toBe(DataSourceType.File);

        // Data set.
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');
        expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.CSV);

        const options = <TabularParsingOptions>loadingJob.dataSet.parsingOptions;
        expect(options.fileFormat).toBe(FileFormat.None);
        expect(options.separator).toBe(',');
        expect(options.eol).toBe('\\n');
        expect(options.header).toBeTruthy();
        expect(options.quote).toBeUndefined();

        const dataSchema = loadingJob.dataSet.dataSchema;
        expect(dataSchema.length).toBe(2);
        expect(dataSchema[0].name).toBe('c1');
        expect(dataSchema[1].name).toBe('c2');
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');

        // Loading statement.
        expect(loadingJob.loadingStatements.length).toBe(0);

        // Position.
        expect(loadingJob.dataSet.position.x).toBe(0.5);
        expect(loadingJob.dataSet.position.y).toBe(0.2);
      });

      it('with s3', () => {
        const loadingJob = LoadingJobLogic.createLoadingJob(
          's3_load_job',
          mockS3DataSource,
          mockCSVFile,
          [],
        );

        // Job name.
        expect(loadingJob.jobName).toBe('s3_load_job');

        // Data source.
        expect(loadingJob.dataSource.name).toBe('myS3Connection');
        expect(loadingJob.dataSource.type).toBe(DataSourceType.S3);

        // Data set.
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');
        expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.CSV);

        const options = <TabularParsingOptions>loadingJob.dataSet.parsingOptions;
        expect(options.fileFormat).toBe(FileFormat.None);
        expect(options.separator).toBe(',');
        expect(options.eol).toBe('\\n');
        expect(options.header).toBeTruthy();
        expect(options.quote).toBeUndefined();

        const dataSchema = loadingJob.dataSet.dataSchema;
        expect(dataSchema.length).toBe(2);
        expect(dataSchema[0].name).toBe('c1');
        expect(dataSchema[1].name).toBe('c2');
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');

        // Loading statement.
        expect(loadingJob.loadingStatements.length).toBe(0);

        // Position.
        expect(loadingJob.dataSet.position.x).toBe(0.5);
        expect(loadingJob.dataSet.position.y).toBe(0.2);
      });

      it('with gcs', () => {
        const loadingJob = LoadingJobLogic.createLoadingJob(
          'gcs_load_job',
          mockGCSDataSource,
          mockCSVFile,
          [],
        );

        // Job name.
        expect(loadingJob.jobName).toBe('gcs_load_job');

        // Data source.
        expect(loadingJob.dataSource.name).toBe('myGCSConnection');
        expect(loadingJob.dataSource.type).toBe(DataSourceType.GoogleCloudStorage);

        // Data set.
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');
        expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.CSV);

        const options = <TabularParsingOptions>loadingJob.dataSet.parsingOptions;
        expect(options.fileFormat).toBe(FileFormat.None);
        expect(options.separator).toBe(',');
        expect(options.eol).toBe('\\n');
        expect(options.header).toBeTruthy();
        expect(options.quote).toBeUndefined();

        const dataSchema = loadingJob.dataSet.dataSchema;
        expect(dataSchema.length).toBe(2);
        expect(dataSchema[0].name).toBe('c1');
        expect(dataSchema[1].name).toBe('c2');
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');

        // Loading statement.
        expect(loadingJob.loadingStatements.length).toBe(0);

        // Position.
        expect(loadingJob.dataSet.position.x).toBe(0.5);
        expect(loadingJob.dataSet.position.y).toBe(0.2);
      });

      it('with abs', () => {
        const loadingJob = LoadingJobLogic.createLoadingJob(
          'abs_load_job',
          mockABSDataSource,
          mockCSVFile,
          [],
        );

        // Job name.
        expect(loadingJob.jobName).toBe('abs_load_job');

        // Data source.
        expect(loadingJob.dataSource.name).toBe('myABSConnection');
        expect(loadingJob.dataSource.type).toBe(DataSourceType.AzureBlobStorage);

        // Data set.
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');
        expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.CSV);

        const options = <TabularParsingOptions>loadingJob.dataSet.parsingOptions;
        expect(options.fileFormat).toBe(FileFormat.None);
        expect(options.separator).toBe(',');
        expect(options.eol).toBe('\\n');
        expect(options.header).toBeTruthy();
        expect(options.quote).toBeUndefined();

        const dataSchema = loadingJob.dataSet.dataSchema;
        expect(dataSchema.length).toBe(2);
        expect(dataSchema[0].name).toBe('c1');
        expect(dataSchema[1].name).toBe('c2');
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/persons.csv');

        // Loading statement.
        expect(loadingJob.loadingStatements.length).toBe(0);

        // Position.
        expect(loadingJob.dataSet.position.x).toBe(0.5);
        expect(loadingJob.dataSet.position.y).toBe(0.2);
      });
    });

    describe('with JSON data format', () => {
      const mockJSONSchema: JSONDataFieldSchema[] = [
        {
          name: 'k1',
          type: JSONDataType.String,
          level: 0,
          path: ['k1']
        },
      ];
      const mockJSONFile: DataSet = {
        uri: '/resources/data_set/gsql/people.json',
        parsingOptions: {
          fileFormat: FileFormat.None,
          eol: '\\n',
        },
        dataFormat: DataFormat.JSON,
        dataSchema: mockJSONSchema,
        position: {
          x: 0.5,
          y: 0.2
        }
      };

      it('with local file', () => {
        const loadingJob = LoadingJobLogic.createLoadingJob(
          'file_load_job',
          mockFileDataSource,
          mockJSONFile,
          [],
        );

        // Job name.
        expect(loadingJob.jobName).toBe('file_load_job');

        // Data source.
        expect(loadingJob.dataSource.name).toBe(undefined);
        expect(loadingJob.dataSource.type).toBe(DataSourceType.File);

        // Data set.
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/people.json');
        expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.JSON);

        const dataSchema = loadingJob.dataSet.dataSchema;
        expect(dataSchema.length).toBe(1);
        expect(<JSONDataFieldSchema>dataSchema[0]).toEqual(
          {
            name: 'k1',
            type: JSONDataType.String,
            level: 0,
            path: ['k1']
          },
        );

        const options = <JSONParsingOptions>loadingJob.dataSet.parsingOptions;
        expect(options.fileFormat).toBe(FileFormat.None);
        expect(options.eol).toBe('\\n');

        // Loading statement.
        expect(loadingJob.loadingStatements.length).toBe(0);

        // Position.
        expect(loadingJob.dataSet.position.x).toBe(0.5);
        expect(loadingJob.dataSet.position.y).toBe(0.2);
      });

      it('with s3', () => {
        const loadingJob = LoadingJobLogic.createLoadingJob(
          's3_load_job',
          mockS3DataSource,
          mockJSONFile,
          [],
        );

        // Job name.
        expect(loadingJob.jobName).toBe('s3_load_job');

        // Data source.
        expect(loadingJob.dataSource.name).toBe('myS3Connection');
        expect(loadingJob.dataSource.type).toBe(DataSourceType.S3);

        // Data set.
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/people.json');
        expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.JSON);

        const dataSchema = loadingJob.dataSet.dataSchema;
        expect(dataSchema.length).toBe(1);
        expect(<JSONDataFieldSchema>dataSchema[0]).toEqual(
          {
            name: 'k1',
            type: JSONDataType.String,
            level: 0,
            path: ['k1']
          },
        );

        const options = <JSONParsingOptions>loadingJob.dataSet.parsingOptions;
        expect(options.fileFormat).toBe(FileFormat.None);
        expect(options.eol).toBe('\\n');

        // Loading statement.
        expect(loadingJob.loadingStatements.length).toBe(0);

        // Position.
        expect(loadingJob.dataSet.position.x).toBe(0.5);
        expect(loadingJob.dataSet.position.y).toBe(0.2);
      });

      it('with gcs', () => {
        const loadingJob = LoadingJobLogic.createLoadingJob(
          'gcs_load_job',
          mockGCSDataSource,
          mockJSONFile,
          [],
        );

        // Job name.
        expect(loadingJob.jobName.startsWith('gcs_load_job')).toBeTruthy();


        // Data source.
        expect(loadingJob.dataSource.name).toBe('myGCSConnection');
        expect(loadingJob.dataSource.type).toBe(DataSourceType.GoogleCloudStorage);

        // Data set.
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/people.json');
        expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.JSON);

        const dataSchema = loadingJob.dataSet.dataSchema;
        expect(dataSchema.length).toBe(1);
        expect(<JSONDataFieldSchema>dataSchema[0]).toEqual(
          {
            name: 'k1',
            type: JSONDataType.String,
            level: 0,
            path: ['k1']
          },
        );

        const options = <JSONParsingOptions>loadingJob.dataSet.parsingOptions;
        expect(options.fileFormat).toBe(FileFormat.None);
        expect(options.eol).toBe('\\n');

        // Loading statement.
        expect(loadingJob.loadingStatements.length).toBe(0);

        // Position.
        expect(loadingJob.dataSet.position.x).toBe(0.5);
        expect(loadingJob.dataSet.position.y).toBe(0.2);
      });

      it('with abs', () => {
        const loadingJob = LoadingJobLogic.createLoadingJob(
          'abs_load_job',
          mockABSDataSource,
          mockJSONFile,
          [],
        );

        // Job name.
        expect(loadingJob.jobName.startsWith('abs_load_job')).toBeTruthy();


        // Data source.
        expect(loadingJob.dataSource.name).toBe('myABSConnection');
        expect(loadingJob.dataSource.type).toBe(DataSourceType.AzureBlobStorage);

        // Data set.
        expect(loadingJob.dataSet.uri).toBe('/resources/data_set/gsql/people.json');
        expect(loadingJob.dataSet.dataFormat).toBe(DataFormat.JSON);

        const dataSchema = loadingJob.dataSet.dataSchema;
        expect(dataSchema.length).toBe(1);
        expect(<JSONDataFieldSchema>dataSchema[0]).toEqual(
          {
            name: 'k1',
            type: JSONDataType.String,
            level: 0,
            path: ['k1']
          },
        );

        const options = <JSONParsingOptions>loadingJob.dataSet.parsingOptions;
        expect(options.fileFormat).toBe(FileFormat.None);
        expect(options.eol).toBe('\\n');

        // Loading statement.
        expect(loadingJob.loadingStatements.length).toBe(0);

        // Position.
        expect(loadingJob.dataSet.position.x).toBe(0.5);
        expect(loadingJob.dataSet.position.y).toBe(0.2);
      });
    });
  });

  it('should create loading job name correctly', () => {
    expect(LoadingJobLogic.createLoadingJobName(DataSourceType.File, 'people.csv')
      .startsWith(LoadingJobPrefix.FILE)).toBeTruthy();
    expect(LoadingJobLogic.createLoadingJobName(DataSourceType.S3, 'people.csv')
      .startsWith(LoadingJobPrefix.S3)).toBeTruthy();
    expect(LoadingJobLogic.createLoadingJobName(DataSourceType.GoogleCloudStorage, 'people.csv')
      .startsWith(LoadingJobPrefix.GCS)).toBeTruthy();
    expect(LoadingJobLogic.createLoadingJobName(DataSourceType.AzureBlobStorage, 'people.csv')
      .startsWith(LoadingJobPrefix.ABS)).toBeTruthy();
  });

  it('should remove loading statement correctly', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    expect(loadingJobData.loadingStatements.length).toBe(3);
    expect('edgeName' in loadingJobData.loadingStatements[1]).toBeTruthy();
    expect((<LoadToEdgeData>loadingJobData.loadingStatements[1]).edgeName).toBe('person_movie');
    LoadingJobLogic.removeLoadingStatement(loadingJobData, 1);
    expect(loadingJobData.loadingStatements.length).toBe(2);
    expect('edgeName' in loadingJobData.loadingStatements[1]).toBeFalsy();
    expect((<LoadToVertexData>loadingJobData.loadingStatements[0]).vertexName).toBe('person');
    expect((<LoadToVertexData>loadingJobData.loadingStatements[1]).vertexName).toBe('movie');
  });

  it('should map data source to entity correctly', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    expect(loadingJobData.loadingStatements[0].mappings[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[0].mappings[1].index).toBe(1);
    LoadingMappingLogic.mapDataSourceToEntity(loadingJobData.loadingStatements[0], DataFormat.CSV, [], 3, 1, theSchema);
    expect(loadingJobData.loadingStatements[0].mappings[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[0].mappings[1].index).toBe(3);
  });

  it('should map data source to token function correctly', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_token_ignore_case_equal', 2);
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingJobData.loadingStatements[1], DataFormat.CSV, [], 1, 0, 0);
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingJobData.loadingStatements[1], DataFormat.CSV, [], 3, 0, 1);
    const tokenFunction = loadingJobData.loadingStatements[1].mappingWidgets[0];
    expect((tokenFunction as TokenFunction).funcName).toBe('gsql_token_ignore_case_equal');
    expect(tokenFunction.params.length).toBe(2);
    expect(tokenFunction.params[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(tokenFunction.params[0].index).toBe(1);
    expect(tokenFunction.params[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(tokenFunction.params[1].index).toBe(3);
  });

  it('should fail map token function to vertex primary id because token function output type mismatch', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_concat', 2);
    expect(() => LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData
    .loadingStatements[0], 0, 0, theSchema)).toThrow(new GraphStudioError(
      'Vertex "person"\'s primary id expects a(n) INT, but the token function\'s output type is STRING.'));
  });

  it('should fail map token function to vertex attribute because token function output type mismatch', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_concat', 2);
    expect(() => LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData
    .loadingStatements[0], 0, 1, theSchema)).toThrow(new GraphStudioError(
      'Vertex "person"\'s attribute "name" expects a(n) INT, but the token function\'s output type is STRING.'));
  });

  it('should fail map token function to edge source vertex because token function output type mismatch', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_concat', 2);
    expect(() => LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData
    .loadingStatements[1], 0, 0, theSchema)).toThrow(new GraphStudioError(
      `Edge "person_movie"'s source vertex "person"'s primary id expects a(n) INT, but the token ` +
      `function's output type is STRING.`));
  });

  it('should fail map token function to edge target vertex because token function output type mismatch', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_to_bool', 1);
    expect(() => LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData
    .loadingStatements[1], 0, 1, theSchema)).toThrow(new GraphStudioError(
      `Edge "person_movie"'s target vertex "movie"'s primary id expects a(n) STRING, but the token ` +
      `function's output type is BOOL.`));
  });

  it('should fail map token function to edge attribute because token function output type mismatch', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_to_bool', 1);
    expect(() => LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData
    .loadingStatements[1], 0, 2, theSchema)).toThrow(new GraphStudioError(
      `Edge "person_movie"'s attribute "name" expects a(n) INT, but the token ` +
      `function's output type is BOOL.`));
  });

  it('should succeed map token function to graph entity mappings', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_to_int', 1);
    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_to_int', 1);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_to_int', 1);
    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_to_int', 1);
    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_concat', 2);

    LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData.loadingStatements[0], 0, 0, theSchema);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData.loadingStatements[0], 1, 1, theSchema);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData.loadingStatements[1], 0, 0, theSchema);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData.loadingStatements[1], 2, 1, theSchema);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData.loadingStatements[1], 1, 2, theSchema);

    expect(loadingJobData.loadingStatements[0].mappings[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingJobData.loadingStatements[0].mappings[0].index).toBe(0);
    expect(loadingJobData.loadingStatements[0].mappings[1].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingJobData.loadingStatements[0].mappings[1].index).toBe(1);
    expect(loadingJobData.loadingStatements[1].mappings[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingJobData.loadingStatements[1].mappings[0].index).toBe(0);
    expect(loadingJobData.loadingStatements[1].mappings[1].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingJobData.loadingStatements[1].mappings[1].index).toBe(2);
    expect(loadingJobData.loadingStatements[1].mappings[2].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingJobData.loadingStatements[1].mappings[2].index).toBe(1);
  });

  it('should fail map token function to token function because circle references', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_concat', 1);
    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_concat', 2);
    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_concat', 1);
    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_concat', 3);
    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_concat', 1);
    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[0], 'gsql_concat', 2);

    const loadingStatement = loadingJobData.loadingStatements[0];

    // 1. self-circle
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingStatement, DataFormat.CSV, [], 0, 1, 1);
    expect(() => LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 1, 1, 0))
    .toThrow(new GraphStudioError('Token functions, Map widgets and UDT widgets cannot have circular references between each other.'));
    expect(loadingStatement.mappingWidgets[1].params[0].sourceType).toBe(SourceType.Default);
    expect(loadingStatement.mappingWidgets[1].params[1].sourceType).toBe(SourceType.DataSourceColumn);
    LoadingMappingLogic.removeLoadingMappings(loadingStatement, [{
      type: OneColumnMappingTargetType.MappingWidget,
      mappingWidgetIndex: 1,
      paramIndex: 1
    }]);

    // 2. 1 -> 2 & 2 -> 1
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 0, 1, 0);
    expect(() => LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 1, 0, 0))
    .toThrow(new GraphStudioError('Token functions, Map widgets and UDT widgets cannot have circular references between each other.'));
    LoadingMappingLogic.removeLoadingMappings(loadingStatement, [{
      type: OneColumnMappingTargetType.MappingWidget,
      mappingWidgetIndex: 1,
      paramIndex: 0
    }]);

    // 3. 1 -> 0, 3 -> 0, 4 -> 1, 1 -> 2, 0 -> 5, 5 -> 6, 5 -> 4
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 0, 1, 1);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 0, 3, 1);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 1, 4, 0);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 2, 1, 0);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 5, 0, 0);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 2, 5, 0);

    expect(loadingStatement.mappingWidgets[0].params[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[0].params[0].index).toBe(5);
    expect(loadingStatement.mappingWidgets[1].params[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[1].params[0].index).toBe(2);
    expect(loadingStatement.mappingWidgets[1].params[1].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[1].params[1].index).toBe(0);
    expect(loadingStatement.mappingWidgets[3].params[1].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[3].params[1].index).toBe(0);
    expect(loadingStatement.mappingWidgets[4].params[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[4].params[0].index).toBe(1);
    expect(loadingStatement.mappingWidgets[5].params[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[5].params[0].index).toBe(2);

    expect(() => LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 4, 5, 1))
    .toThrow(new GraphStudioError('Token functions, Map widgets and UDT widgets cannot have circular references between each other.'));
  });

  it('should remove loading mappings correctly', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_concat', 2);

    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingJobData.loadingStatements[1], DataFormat.CSV, [], 1, 0, 0);
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingJobData.loadingStatements[1], DataFormat.CSV, [], 2, 0, 1);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingJobData.loadingStatements[1], 0, 1, theSchema);

    expect(loadingJobData.loadingStatements[1].mappings[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[1].mappings[0].index).toBe(0);
    expect(loadingJobData.loadingStatements[1].mappings[1].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingJobData.loadingStatements[1].mappings[1].index).toBe(0);
    expect(loadingJobData.loadingStatements[1].mappings[2].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[1].mappings[2].index).toBe(3);
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[0].index).toBe(1);
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[1].index).toBe(2);

    LoadingMappingLogic.removeLoadingMappings(loadingJobData.loadingStatements[1], [
      <OneColumnMappingGraphEntityTarget>{
        type: OneColumnMappingTargetType.GraphEntity,
        mappingIndex: 1
      },
      <OneColumnMappingMappingWidgetTarget>{
        type: OneColumnMappingTargetType.MappingWidget,
        mappingWidgetIndex: 0,
        paramIndex: 1
      },
      <OneColumnMappingGraphEntityTarget>{
        type: OneColumnMappingTargetType.GraphEntity,
        mappingIndex: 2
      }
    ]);

    expect(loadingJobData.loadingStatements[1].mappings[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[1].mappings[0].index).toBe(0);
    expect(loadingJobData.loadingStatements[1].mappings[1].sourceType).toBe(SourceType.Default);
    expect(loadingJobData.loadingStatements[1].mappings[2].sourceType).toBe(SourceType.Default);
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[0].index).toBe(1);
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[1].sourceType).toBe(SourceType.Default);
  });

  it('should map literal to graph entity correctly', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    expect(loadingJobData.loadingStatements[0].mappings[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingJobData.loadingStatements[0].mappings[1].index).toBe(1);
    LoadingMappingLogic.mapStringLiteralToEntity(loadingJobData.loadingStatements[0], 1, 's1234');
    expect(loadingJobData.loadingStatements[0].mappings[1].sourceType).toBe(SourceType.Literal);
    expect(loadingJobData.loadingStatements[0].mappings[1].literal).toBe('s1234');
  });

  it('should map literal to token function correctly', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    LoadingMappingLogic.addTokenFunction(loadingJobData.loadingStatements[1], 'gsql_concat', 2);

    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[1].sourceType).toBe(SourceType.Default);

    LoadingMappingLogic.mapStringLiteralToMappingWidget(loadingJobData.loadingStatements[1], 0, 1, 'src');
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[1].sourceType).toBe(SourceType.Literal);
    expect(loadingJobData.loadingStatements[1].mappingWidgets[0].params[1].literal).toBe('src');
  });

  it('should remove token function correctly', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    const newSchema = theSchema.clone();
    newSchema.vertexTypes[0].primaryId = new Attribute().loadFromGSQLJson({
      AttributeName: 'description',
      AttributeType: {
        Name: 'STRING'
      }
    });
    newSchema.edgeTypes[0].attributes[0] = new Attribute().loadFromGSQLJson({
      AttributeName: 'description',
      AttributeType: {
        Name: 'STRING'
      }
    });

    const loadingStatement = loadingJobData.loadingStatements[1];
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_concat', 3);
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_concat', 2);
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_concat', 2);

    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 2, 0, 0);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 1, 0, 1);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 1, 0, 2);
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingStatement, DataFormat.CSV, [], 2, 1, 0);
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingStatement, DataFormat.CSV, [], 3, 1, 1);
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingStatement, DataFormat.CSV, [], 1, 2, 0);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 1, 2, 1);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingStatement, 0, 0, newSchema);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingStatement, 1, 1, newSchema);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingStatement, 2, 2, newSchema);

    expect(loadingStatement.mappings[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappings[0].index).toBe(0);
    expect(loadingStatement.mappings[1].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappings[1].index).toBe(1);
    expect(loadingStatement.mappings[2].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappings[2].index).toBe(2);
    expect(loadingStatement.mappingWidgets[0].params[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[0].params[0].index).toBe(2);
    expect(loadingStatement.mappingWidgets[0].params[1].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[0].params[1].index).toBe(1);
    expect(loadingStatement.mappingWidgets[1].params[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingStatement.mappingWidgets[1].params[0].index).toBe(2);
    expect(loadingStatement.mappingWidgets[1].params[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingStatement.mappingWidgets[1].params[1].index).toBe(3);
    expect(loadingStatement.mappingWidgets[2].params[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingStatement.mappingWidgets[2].params[0].index).toBe(1);
    expect(loadingStatement.mappingWidgets[2].params[1].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[2].params[1].index).toBe(1);
    expect(loadingStatement.style.middlePositions.length).toBe(3);
    const style = JSON.parse(JSON.stringify(loadingStatement.style.middlePositions));

    LoadingMappingLogic.removeMappingWidget(loadingStatement, 1);

    expect(loadingStatement.mappings[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappings[0].index).toBe(0);
    expect(loadingStatement.mappings[1].sourceType).toBe(SourceType.Default);
    expect(loadingStatement.mappings[2].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappings[2].index).toBe(1);
    expect(loadingStatement.mappingWidgets[0].params[0].sourceType).toBe(SourceType.MappingWidget);
    expect(loadingStatement.mappingWidgets[0].params[0].index).toBe(1);
    expect(loadingStatement.mappingWidgets[0].params[1].sourceType).toBe(SourceType.Default);
    expect(loadingStatement.mappingWidgets[1].params[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(loadingStatement.mappingWidgets[1].params[0].index).toBe(1);
    expect(loadingStatement.mappingWidgets[1].params[1].sourceType).toBe(SourceType.Default);
    expect(loadingStatement.mappingWidgets.length).toBe(2);
    expect(loadingStatement.style.middlePositions.length).toBe(2);
    expect(loadingStatement.style.middlePositions[0].x).toBe(style[0].x);
    expect(loadingStatement.style.middlePositions[0].y).toBe(style[0].y);
    expect(loadingStatement.style.middlePositions[1].x).toBe(style[2].x);
    expect(loadingStatement.style.middlePositions[1].y).toBe(style[2].y);
  });

  it('should fail token function mapping build because trying to map a token function with ' +
  'int type output to another token function', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);


    const loadingStatement = loadingJobData.loadingStatements[1];
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_to_int', 3);
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_concat', 2);
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_to_int', 2);

    expect(() => LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 2, 0, 0)
    ).toThrow(new GraphStudioError(
      `Cannot map token function "gsql_to_int" with return type INT to a token function's input which accept a STRING.`
    ));
  });

  it('should assign correct position to new token function', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    const loadingStatement = loadingJobData.loadingStatements[1];
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_to_int', 3);
    expect(loadingStatement.style.middlePositions[0].x).toBe(0.5);
    expect(loadingStatement.style.middlePositions[0].y).toBe(0.5);
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_to_int', 3);
    expect(loadingStatement.style.middlePositions[0].x).toBe(0.5);
    expect(loadingStatement.style.middlePositions[0].y).toBe(0.5);
    expect(loadingStatement.style.middlePositions[1].x).toBe(0.5);
    expect(loadingStatement.style.middlePositions[1].y).toBe(150.5);
    loadingStatement.style.middlePositions[0] = { x: 0, y: 20 };
    loadingStatement.style.middlePositions[1] = { x: 0, y: 110 };
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_to_int', 3);
    expect(loadingStatement.style.middlePositions[2].x).toBe(0.5);
    expect(loadingStatement.style.middlePositions[2].y).toBe(300.5);
  });

  it('should conduct auto mapping correctly', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    loadingJobData.dataSet.dataSchema = [
      {
        name: ' name ',
      },
      {
        name: ' ID ',
      },
    ];

    loadingJobData.loadingStatements.push(LoadToVertexLogic
    .createLoadToVertex(theSchema.getVertex('person'), {
      HEADER: 'FALSE',
      EOL: '\n',
      SEPARATOR: ','
    }));
    LoadingMappingLogic.autoMapping(loadingJobData, loadingJobData.loadingStatements[3], DataFormat.CSV, [], theSchema);
    const statement1 = loadingJobData.loadingStatements[3];
    expect(statement1.mappings[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(statement1.mappings[0].index).toBe(1);
    expect(statement1.mappings[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(statement1.mappings[1].index).toBe(0);

    loadingJobData.dataSet.dataSchema = [
      {
        name: '\tMovie-',
      },
      {
        name: ' name ',
      },
      {
        name: ' PERSON ',
      }
    ];

    loadingJobData.loadingStatements.push(LoadToEdgeLogic
    .createLoadToEdge(theSchema.getEdge('person_movie'), 'person', 'movie', {
      HEADER: 'FALSE',
      EOL: '\n',
      SEPARATOR: ','
    }));
    LoadingMappingLogic.autoMapping(loadingJobData, loadingJobData.loadingStatements[4], DataFormat.CSV, [], theSchema);
    const statement2 = loadingJobData.loadingStatements[4];
    expect(statement2.mappings[0].sourceType).toBe(SourceType.DataSourceColumn);
    expect(statement2.mappings[0].index).toBe(2);
    expect(statement2.mappings[1].sourceType).toBe(SourceType.DataSourceColumn);
    expect(statement2.mappings[1].index).toBe(0);
    expect(statement2.mappings[2].sourceType).toBe(SourceType.DataSourceColumn);
    expect(statement2.mappings[2].index).toBe(1);
  });

  it('should dump token functions\'s styles in DFS order, skip unused ones, duplicate reused ones', () => {
    const loadingJobData: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(mockGsqlLoadingJobJson, dbLoadingJobJson);

    const loadingStatement = loadingJobData.loadingStatements[1];
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_concat', 2);
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_to_int', 2);
    LoadingMappingLogic.addTokenFunction(loadingStatement, 'gsql_to_int', 2);

    loadingStatement.style.middlePositions[0] = { x: 10, y: 10 };
    loadingStatement.style.middlePositions[1] = { x: 20, y: 20 };
    loadingStatement.style.middlePositions[2] = { x: 0, y: 30 };

    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingStatement, DataFormat.CSV, [], 1, 0, 0);
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingStatement, DataFormat.CSV, [], 2, 0, 1);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 0, 1, 0);
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingStatement, DataFormat.CSV, [], 3, 1, 1);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingStatement, 1, 0, theSchema);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingStatement, 0, 1, theSchema);
    LoadingMappingLogic.mapMappingWidgetToEntity(loadingStatement, 1, 2, theSchema);
    // token funciton 2 is not used
    LoadingMappingLogic.mapDataSourceToMappingWidget(loadingStatement, DataFormat.CSV, [], 1, 2, 0);
    LoadingMappingLogic.mapMappingWidgetToMappingWidget(loadingStatement, 0, 2, 1);

    let dumpGSQLJson: GSQLLoadingJobJson;
    let dumpDBJson: DBLoadingJobJson;
    [dumpGSQLJson, dumpDBJson] = LoadingJobLogic.dumpToGSQLAndDB(loadingJobData, theSchema);
    expect(dumpDBJson.loadingStatementsStyle[2].middlePositions.length).toBe(5);
    expect(dumpDBJson.loadingStatementsStyle[2].middlePositions[0].x).toBe(20);
    expect(dumpDBJson.loadingStatementsStyle[2].middlePositions[1].y).toBe(10);
    expect(dumpDBJson.loadingStatementsStyle[2].middlePositions[2].x).toBe(10);
    expect(dumpDBJson.loadingStatementsStyle[2].middlePositions[3].y).toBe(20);
    expect(dumpDBJson.loadingStatementsStyle[2].middlePositions[4].x).toBe(10);

    const loadingJobData2: LoadingJobData = LoadingJobLogic
      .loadFromGSQLAndDB(dumpGSQLJson, dumpDBJson);
    expect(LoadingJobLogic.semanticCheck(loadingJobData2, theSchema).success).toBeTruthy();
  });

});
