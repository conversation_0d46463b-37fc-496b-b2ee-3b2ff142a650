import { <PERSON>Format, JSONData<PERSON>ieldSchema, JSONDataType } from './data-set.interface';
import { DBLoadingStatementStyle, DBPositionStyle } from './db-loading-job.interface';
import { GSQLLoadingAstJson } from './gsql-loading-job.interface';
import {
  LoadingMappingLogic,
  MapWidget, OneColumnMapping, SourceType, TokenFunction, TupleWidget,
} from './loading-mapping.model';

const mockJSONSchema: JSONDataFieldSchema[] = [
  {
    name: 'encoding',
    type: JSONDataType.String,
    level: 0,
    path: [
      'encoding'
    ]
  },
  {
    name: 'indent',
    type: JSONDataType.Object,
    level: 0,
    path: [
      'indent'
    ]
  },
  {
    name: 'length',
    type: JSONDataType.Number,
    level: 1,
    path: [
      'indent',
      'length'
    ]
  },
  {
    name: 'use_space',
    type: JSONDataType.Boolean,
    level: 1,
    path: [
      'indent',
      'use_space'
    ]
  },
  {
    name: 'wrapper',
    type: JSONDataType.Object,
    level: 1,
    path: [
      'indent',
      'wrapper'
    ]
  },
  {
    name: 'size',
    type: JSONDataType.Number,
    level: 2,
    path: [
      'indent',
      'wrapper',
      'size'
    ]
  },
  {
    name: 'plug-ins',
    type: JSONDataType.Array,
    level: 0,
    path: [
      'plug-ins'
    ]
  }
];

describe('LoadingMappingLogic', () => {

  describe('load from GSQL loading mapping', () => {
    let mappingContainer: OneColumnMapping[];
    let mappingWidgetContainer: (TokenFunction | MapWidget | TupleWidget)[];

    let oneColumnMappingAst: GSQLLoadingAstJson;

    beforeEach(() => {
      mappingContainer = [];
      mappingWidgetContainer = [];
    });

    it('with csv data format', () => {
      oneColumnMappingAst = {
        Type: 'SrcColIndex',
        Value: 0,
      };

      LoadingMappingLogic.loadMappingFromGSQLMappingJson(
        oneColumnMappingAst,
        mappingContainer,
        mappingWidgetContainer,
        [],
      );

      expect(mappingContainer.length).toBe(1);
      expect(mappingContainer[0]).toEqual(
        {
          sourceType: SourceType.DataSourceColumn,
          index: 0,
        },
      );
    });

    it('with json data format', () => {
      oneColumnMappingAst = {
        Type: 'SrcColName',
        Value: 'indent',
        SrcKeyNames: [
          'indent',
          'wrapper',
          'size',
        ],
      };

      LoadingMappingLogic.loadMappingFromGSQLMappingJson(
        oneColumnMappingAst,
        mappingContainer,
        mappingWidgetContainer,
        mockJSONSchema,
      );

      expect(mappingContainer.length).toBe(1);
      expect(mappingContainer[0]).toEqual(
        {
          sourceType: SourceType.DataSourceColumn,
          index: 5,
        }
      );
    });
  });


  describe('dump to GSQL loading mapping', () => {
    let gsqlMappingsContainer: GSQLLoadingAstJson[];
    let mappingWidgets: (TokenFunction | MapWidget | TupleWidget)[];
    let loadingJobStyle: DBLoadingStatementStyle;
    let middlePositionsContainer: DBPositionStyle[];

    let oneColumnMapping: OneColumnMapping;

    beforeEach(() => {
      gsqlMappingsContainer = [];
      mappingWidgets = [];
      loadingJobStyle = undefined;
      middlePositionsContainer = [];
    });

    it('with csv data format', () => {
      oneColumnMapping = {
        sourceType: SourceType.DataSourceColumn,
        index: 0,
      };

      LoadingMappingLogic.dumpToGSQLLoadingMapping(
        gsqlMappingsContainer,
        oneColumnMapping,
        mappingWidgets,
        loadingJobStyle,
        middlePositionsContainer,
        DataFormat.CSV,
        [],
      );

      expect(gsqlMappingsContainer.length).toBe(1);
      expect(gsqlMappingsContainer[0]).toEqual(
        {
          Type: 'SrcColIndex',
          Value: 0,
        },
      );
    });

    it('with json data format', () => {
      oneColumnMapping = {
        sourceType: SourceType.DataSourceColumn,
        index: 5,
      };

      LoadingMappingLogic.dumpToGSQLLoadingMapping(
        gsqlMappingsContainer,
        oneColumnMapping,
        mappingWidgets,
        loadingJobStyle,
        middlePositionsContainer,
        DataFormat.JSON,
        mockJSONSchema,
      );

      expect(gsqlMappingsContainer.length).toBe(1);
      expect(gsqlMappingsContainer[0]).toEqual(
        {
          Type: 'SrcColName',
          Value: 'indent',
          SrcKeyNames: [
            'indent',
            'wrapper',
            'size',
          ],
        }
      );
    });
  });
});


