import { Attribute, <PERSON>, Graph, Vertex } from '../topology';

import { DBLoadingStatementStyle } from './db-loading-job.interface';
import { GSQLLoadToVertexJson } from './gsql-loading-job.interface';
import { SourceType, TokenFunction } from './loading-mapping.model';
import { LoadToVertexLogic } from './load-to-vertex.model';

describe('GSQLLoadToVertexModel', () => {
  let dbLoadingStatementStyle: DBLoadingStatementStyle;
  let mockGsqlLoadToVertexJson: GSQLLoadToVertexJson;

  beforeEach(() => {
    dbLoadingStatementStyle = {
      sourcePosition: {
        x: 0.2,
        y: 0.5
      },
      targetPosition: {
        x: 0.8,
        y: 0.5
      },
      middlePositions: [
        {
          x: 0.5,
          y: 0.5
        },
        {
          x: 0.5,
          y: 0.6
        }
      ]
    };

    mockGsqlLoadToVertexJson = {
      Type: 'Vertex',
      UsingClauses: {},
      Mappings: [
        {
          Type: 'SrcColIndex',
          Value: 0
        },
        {
          Type: 'SrcColIndex',
          Value: 1
        },
        {
          Type: 'UDF',
          Params: [
            {
              Type: 'SrcColIndex',
              Value: 1
            },
            {
              Type: 'UDF',
              Params: [
                {
                  Type: 'SrcColIndex',
                  Value: 2
                },
                {
                  Type: 'Literal',
                  Value: 'a'
                },
                {
                  Type: 'Default'
                }
              ],
              UdfName: 'gsql_concat'
            }
          ],
          UdfName: 'gsql_concat'
        }
      ],
      TargetName: 'person',
      DataSource: {
        Type: 'Online'
      }
    };
  });

  it('should clone load to vertex successfully', () => {
    const loadToVertexOrigin = LoadToVertexLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVertexJson,
      dbLoadingStatementStyle,
      [],
    );
    const loadToVertex = LoadToVertexLogic.clone(loadToVertexOrigin);
    loadToVertexOrigin.vertexName = 'movie';
    expect(loadToVertexOrigin.vertexName).toBe('movie');
    expect(loadToVertex.vertexName).toBe('person');
    expect(loadToVertex.style.sourcePosition.x).toBe(0.2);
    expect(loadToVertex.style.targetPosition.y).toBe(0.5);
    expect(loadToVertex.mappings.length).toBe(3);
    expect(loadToVertex.mappings[0].sourceType).toBe(
      SourceType.DataSourceColumn
    );
    expect(loadToVertex.mappings[0].index).toBe(0);
    expect(loadToVertex.mappings[1].sourceType).toBe(
      SourceType.DataSourceColumn
    );
    expect(loadToVertex.mappings[1].index).toBe(1);
    expect(loadToVertex.mappings[2].sourceType).toBe(SourceType.MappingWidget);
    expect(loadToVertex.mappings[2].index).toBe(0);
    expect(loadToVertex.mappingWidgets.length).toBe(2);
    expect((loadToVertex.mappingWidgets[0] as TokenFunction).funcName).toBe('gsql_concat');
    expect(loadToVertex.mappingWidgets[0].params.length).toBe(2);
    expect(loadToVertex.mappingWidgets[0].params[0].sourceType).toBe(
      SourceType.DataSourceColumn
    );
    expect(loadToVertex.mappingWidgets[0].params[0].index).toBe(1);
    expect(loadToVertex.mappingWidgets[0].params[1].sourceType).toBe(
      SourceType.MappingWidget
    );
    expect(loadToVertex.mappingWidgets[0].params[1].index).toBe(1);
    expect((loadToVertex.mappingWidgets[1] as TokenFunction).funcName).toBe('gsql_concat');
    expect(loadToVertex.mappingWidgets[1].params.length).toBe(3);
    expect(loadToVertex.mappingWidgets[1].params[0].sourceType).toBe(
      SourceType.DataSourceColumn
    );
    expect(loadToVertex.mappingWidgets[1].params[0].index).toBe(2);
    expect(loadToVertex.mappingWidgets[1].params[1].sourceType).toBe(
      SourceType.Literal
    );
    expect(loadToVertex.mappingWidgets[1].params[1].literal).toBe('a');
    expect(loadToVertex.mappingWidgets[1].params[2].sourceType).toBe(
      SourceType.Default
    );
  });

  it('should fail semantic check because vertex is not in schema', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'v1';
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'v1', to: 'v1' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vertex
    const loadToVertex = LoadToVertexLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVertexJson,
      dbLoadingStatementStyle,
      [],
    );

    const semanticCheckResult = LoadToVertexLogic.semanticCheck(
      loadToVertex,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Vertex type "person" doesn\'t exist in graph schema.'
    );
  });

  it(
    'should fail semantic check because data mapping number and vertex attribute number ' +
      'not match',
    () => {
      // Prepare the schema
      const schema = new Graph();
      const vertex = new Vertex();
      vertex.name = 'person';
      vertex.attributes.push(
        new Attribute().loadFromGSQLJson({
          AttributeName: 'name',
          AttributeType: {
            Name: 'INT'
          }
        })
      );
      const edge = new Edge();
      edge.name = 'e1';
      edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
      schema.vertexTypes.push(vertex);
      schema.edgeTypes.push(edge);

      // Prepare the load to vertex
      const loadToVertex = LoadToVertexLogic.loadFromGSQLAndDB(
        mockGsqlLoadToVertexJson,
        dbLoadingStatementStyle,
        [],
      );

      const semanticCheckResult = LoadToVertexLogic.semanticCheck(
        loadToVertex,
        ['name', 'age'],
        schema
      );
      expect(semanticCheckResult.success).toBeFalsy();
      expect(semanticCheckResult.message).toBe(
        `Data loading mapping number and vertex attribute number don't match.`
      );
    }
  );

  it('should fail semantic check because primary id mapping cannot be empty', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    vertex.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    vertex.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    const edge = new Edge();
    edge.name = 'e1';
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
    schema.vertexTypes.push(vertex);
    schema.edgeTypes.push(edge);

    // Prepare the load to vertex
    const loadToVertex = LoadToVertexLogic.loadFromGSQLAndDB(
      mockGsqlLoadToVertexJson,
      dbLoadingStatementStyle,
      [],
    );
    loadToVertex.mappings[0].sourceType = SourceType.Default;

    const semanticCheckResult = LoadToVertexLogic.semanticCheck(
      loadToVertex,
      ['name', 'age'],
      schema
    );
    expect(semanticCheckResult.success).toBeFalsy();
    expect(semanticCheckResult.message).toBe(
      'Need to map data column to vertex primary id.'
    );
  });

  it(
    'should fail semantic check because data mapping number and vertex attribute number ' +
      'do not match',
    () => {
      // Prepare the schema
      const schema = new Graph();
      const vertex = new Vertex();
      vertex.name = 'person';
      vertex.attributes.push(
        new Attribute().loadFromGSQLJson({
          AttributeName: 'name',
          AttributeType: {
            Name: 'INT'
          }
        })
      );
      vertex.attributes.push(
        new Attribute().loadFromGSQLJson({
          AttributeName: 'name',
          AttributeType: {
            Name: 'INT'
          }
        })
      );
      const edge = new Edge();
      edge.name = 'e1';
      edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
      schema.vertexTypes.push(vertex);
      schema.edgeTypes.push(edge);

      // Prepare the load to vertex
      const dbLoadingStatementStyle2 = {
        sourcePosition: {
          x: 0.2,
          y: 0.5
        },
        targetPosition: {
          x: 0.8,
          y: 0.5
        },
        middlePositions: [
          {
            x: 0.5,
            y: 0.5
          }
        ]
      };
      const loadToVertex = LoadToVertexLogic.loadFromGSQLAndDB(
        mockGsqlLoadToVertexJson,
        dbLoadingStatementStyle2,
        [],
      );

      const semanticCheckResult = LoadToVertexLogic.semanticCheck(
        loadToVertex,
        ['name', 'age'],
        schema
      );
      expect(semanticCheckResult.success).toBeFalsy();
      expect(semanticCheckResult.message).toBe(
        'Token function number and token function ' +
          'style number do not match.'
      );
    }
  );

  it(
    'should fail semantic check because loading mapping refer to a data source column ' +
      `don't match`,
    () => {
      // Prepare the schema
      const schema = new Graph();
      const vertex = new Vertex();
      vertex.name = 'person';
      vertex.attributes.push(
        new Attribute().loadFromGSQLJson({
          AttributeName: 'name',
          AttributeType: {
            Name: 'INT'
          }
        })
      );
      vertex.attributes.push(
        new Attribute().loadFromGSQLJson({
          AttributeName: 'name',
          AttributeType: {
            Name: 'INT'
          }
        })
      );
      const edge = new Edge();
      edge.name = 'e1';
      edge.fromToVertexTypePairs = [{ from: 'person', to: 'person' }];
      schema.vertexTypes.push(vertex);
      schema.edgeTypes.push(edge);

      // Prepare the load to vertex
      const loadToVertex = LoadToVertexLogic.loadFromGSQLAndDB(
        mockGsqlLoadToVertexJson,
        dbLoadingStatementStyle,
        [],
      );

      let semanticCheckResult = LoadToVertexLogic.semanticCheck(
        loadToVertex,
        ['name'],
        schema
      );
      expect(semanticCheckResult.success).toBeFalsy();
      expect(semanticCheckResult.message).toBe(
        'Data mapped to a column outside data source columns.'
      );

      semanticCheckResult = LoadToVertexLogic.semanticCheck(
        loadToVertex,
        ['name', 'age'],
        schema
      );
      expect(semanticCheckResult.success).toBeFalsy();
      expect(semanticCheckResult.message).toBe(
        'Token function parameter mapped to a column outside data source columns.'
      );
    }
  );

  it('should create one load-to-vertex statement correctly', () => {
    // Prepare the schema
    const schema = new Graph();
    const vertex = new Vertex();
    vertex.name = 'person';
    vertex.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    const edge = new Edge();
    edge.name = 'person_movie';
    edge.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'INT'
        }
      })
    );
    edge.fromToVertexTypePairs = [{ from: 'person', to: 'movie' }];
    const vertex2 = new Vertex();
    vertex2.name = 'movie';
    vertex2.attributes.push(
      new Attribute().loadFromGSQLJson({
        AttributeName: 'name',
        AttributeType: {
          Name: 'STRING'
        }
      })
    );
    schema.vertexTypes.push(vertex);
    schema.vertexTypes.push(vertex2);
    schema.edgeTypes.push(edge);



    // Create one load-to-vertex statement
    const loadToVertex = LoadToVertexLogic.createLoadToVertex(
      schema.getVertex('movie'),
      {
        HEADER: 'FALSE',
        EOL: '\n',
        SEPARATOR: ','
      }
    );

    expect(loadToVertex.vertexName).toBe('movie');
    expect(loadToVertex.mappingWidgets.length).toBe(0);
    expect(loadToVertex.mappings.length).toBe(2);
    loadToVertex.mappings.forEach(mapping => {
      expect(mapping.sourceType).toBe(SourceType.Default);
    });
    expect(loadToVertex.style.sourcePosition.x).toBe(30);
    expect(loadToVertex.style.sourcePosition.y).toBe(300);
    expect(loadToVertex.style.targetPosition.x).toBe(650);
    expect(loadToVertex.style.targetPosition.y).toBe(300);
    expect(loadToVertex.style.middlePositions.length).toBe(0);
  });
});
