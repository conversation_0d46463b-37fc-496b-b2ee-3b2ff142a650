import { DataSetLogic } from './data-set-logic.model';
import { DataSource, DataSourceType } from './data-source.interface';

export class DataSourceLogic {
  static format = [
    ['none', 'text'],
    ['zip', 'zip'],
    ['gzip', 'gzip'],
    ['tar', 'tar'],
  ];

  static separators = [
    [',', ',', ','],
    ['\t', '\\t', '\\t'],
    ['.', '.', '.'],
    ['|', '|', '|'],
    ['/', '/', '/'],
    [':', ':', ':'],
    [';', ';', ';'],
    [' ', '\\u0020', 'whitespace'],
    [`'`, `'`, `'`],
    ['\\', '\\\\', '\\'],
    ['"', '\\"', '"'],
    ['^', '^', '^'],
    ['?', '?', '?'],
    ['~', '~', '~'],
    ['!', '!', '!'],
    ['@', '@', '@'],
    ['#', '#', '#'],
    ['$', '$', '$'],
    ['%', '%', '%'],
    ['&', '&', '&'],
    ['*', '*', '*'],
    ['-', '-', '-'],
    ['_', '_', '_'],
    ['+', '+', '+'],
    ['=', '=', '='],
    ['`', '`', '`'],
    ['<', '<', '<'],
    ['>', '>', '>'],
    ['\n', '\\n', '\\n'],
    [String.fromCharCode(1), '\\1', '\\1'],
    [String.fromCharCode(2), '\\2', '\\2'],
    [String.fromCharCode(3), '\\3', '\\3'],
    [String.fromCharCode(4), '\\4', '\\4'],
    [String.fromCharCode(5), '\\5', '\\5'],
    [String.fromCharCode(6), '\\6', '\\6'],
    [String.fromCharCode(7), '\\7', '\\7'],
  ];

  static eols = [
    ['\n', '\\n', '\\n'],
    ['\t', '\\t', '\\t'],
    ['.', '.', '.'],
    ['|', '|', '|'],
    ['/', '/', '/'],
    [':', ':', ':'],
    [';', ';', ';'],
    [' ', '\\u0020', 'whitespace'],
    [`'`, `'`, `'`],
    ['\\', '\\\\', '\\'],
    ['"', '\\"', '"'],
    ['^', '^', '^'],
    ['?', '?', '?'],
    ['~', '~', '~'],
    ['!', '!', '!'],
    ['@', '@', '@'],
    ['#', '#', '#'],
    ['$', '$', '$'],
    ['%', '%', '%'],
    ['&', '&', '&'],
    ['*', '*', '*'],
    ['-', '-', '-'],
    ['_', '_', '_'],
    ['+', '+', '+'],
    ['=', '=', '='],
    ['`', '`', '`'],
    ['<', '<', '<'],
    ['>', '>', '>'],
    [',', ',', ','],
    [String.fromCharCode(1), '\\1', '\\1'],
    [String.fromCharCode(2), '\\2', '\\2'],
    [String.fromCharCode(3), '\\3', '\\3'],
    [String.fromCharCode(4), '\\4', '\\4'],
    [String.fromCharCode(5), '\\5', '\\5'],
    [String.fromCharCode(6), '\\6', '\\6'],
    [String.fromCharCode(7), '\\7', '\\7'],
  ];

  static quotes = [
    ['none', '', 'None'],
    [`"`, 'double', `"`],
    [`'`, 'single', `'`],
  ];

  static ABSConnectionStringPattern = `^(DefaultEndpointsProtocol=(http|https);)?AccountName=[a-z0-9]{3,24};` +
    `AccountKey=[a-zA-Z0-9/+=]*(;EndpointSuffix=([\\w\\d]+)(\\.[\\w\\d]+)*)?$`;

  /**
   * Create a data source.
   *
   * @static
   * @param {string} name
   * @param {DataSourceType} type
   * @returns {DataSource}
   * @memberof DataSourceLogic
   */
  static createDataSource(name: string, type: DataSourceType): DataSource {
    if (name === undefined) {
      return {
        type: type,
        isLocal: true,
      };
    }

    return {
      name: name,
      type: type,
      isLocal: true,
    };
  }

  /**
   * Get data source type.
   *
   * @static
   * @param {string} type
   * @return {DataSourceType}
   * @memberof DataSourceLogic
   */
  static getDataSourceType(type: string): DataSourceType {
    switch (type) {
      case DataSourceType.File:
      case DataSourceType.S3:
      case DataSourceType.GoogleCloudStorage:
      case DataSourceType.Kafka:
      case DataSourceType.AzureBlobStorage:
        return type;
      default:
        return DataSourceType.None;
    }
  }

  /**
   * Get the type of uri for a given data source type.
   *
   * @param {string} dataSourceType
   * @returns {string}
   */
  static getURIName(dataSourceType: string): string {
    switch (dataSourceType) {
      case DataSourceType.S3:
        return 'S3 URI';
      case DataSourceType.GoogleCloudStorage:
        return 'gsutil URI';
      case DataSourceType.AzureBlobStorage:
        return 'Blob URL';
      default:
        // TODO: add more data source types.
        return '';
    }
  }

  /**
   * Get the uri prefix for a specific data source.
   *
   * @param {string} dataSourceType
   * @returns {string}
   */
  static getURIPrefixPlaceholder(dataSourceType: string): string {
    switch (dataSourceType) {
      case DataSourceType.S3:
        return 's3://';
      case DataSourceType.GoogleCloudStorage:
        return 'gs://';
      case DataSourceType.AzureBlobStorage:
        return 'https://';
      default:
        // TODO: add more data source types.
        return '';
    }
  }

  /**
   * Get link to guide users to get uri syntax.
   *
   * @param {string} dataSourceType
   * @returns {string}
   */
  static getGuideLink(dataSourceType: string): string {
    switch (dataSourceType) {
      case DataSourceType.S3:
        return 'https://docs.aws.amazon.com/AmazonS3/latest/userguide/access-bucket-intro.html#accessing-a-bucket-using-S3-format';
      case DataSourceType.GoogleCloudStorage:
        return 'https://cloud.google.com/storage/docs/gsutil#syntax';
      case DataSourceType.AzureBlobStorage:
        return 'https://learn.microsoft.com/en-us/rest/api/storageservices/copy-blob-from-url#request';
      default:
        return '';
    }
  }

  /**
   * Check if the current data source type is a kafka based data source.
   *
   * @param {string}
   * @returns {boolean}
   */
  static isRemoteDataSourceType(dataSourceType: string): boolean {
    switch (dataSourceType) {
      case DataSourceType.S3:
      case DataSourceType.GoogleCloudStorage:
      case DataSourceType.AzureBlobStorage:
        return true;
      default:
        return false;
    }
  }

  /**
   * Parse the Azure Blob Storage connection string to get account name and account key.
   *
   * @static
   * @param {string} absConnectionString
   * @returns {{accountName: string, accountKey: string}}
   * @memberof LoadingBuilderService
   */
   static getABSCredentials(inputStr: string): {accountName: string, accountKey: string} {
    const credentialInfo = { accountName: '', accountKey: ''};

    inputStr.split(';').forEach(keyValuePair => {
      let matchResult = keyValuePair.match(new RegExp('AccountName=(.*)'));
      if (matchResult) {
        credentialInfo['accountName'] = matchResult[1];
      }
      matchResult = keyValuePair.match(new RegExp('AccountKey=(.*)'));
      if (matchResult) {
        credentialInfo['accountKey'] = matchResult[1];
      }
    });
    return credentialInfo;
  }
}
