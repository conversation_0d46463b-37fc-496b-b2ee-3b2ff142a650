import { QuestionBase } from './question-base.model';
import { CheckboxQuestion } from './question-checkbox.model';
import { KeyValueQuestion } from './question-key-value.model';
import { InputQuestion } from './question-input.model';
import { InputVertexQuestion } from './question-input-vertex.model';
import { SelectQuestion } from './question-select.model';
import { UdtQuestion } from './question-udt.model';

/**
 * Multi input question extends input question and can accept multiple inputs.
 * - type: 'text' | 'number' | 'vertex'
 *
 * @export
 * @class ListQuestion
 * @extends {QuestionBase}
 */
export class ListQuestion<T> extends QuestionBase<T> {
  templateOption: any;
  controlType = 'list';
  incrementalPtr: number;
  questions: QuestionBase<any>[];

  constructor(options: any = {}) {
    super(options);

    this.templateOption = options.templateOption || {};
    this.questions = [];
    this.incrementalPtr = 0;
    // Retrieve the question list
    if (options.valueOptions) {
      options.valueOptions.forEach(valueOption => {
        const newQuestion = this.constructQuestion(valueOption);
        // Need assign new key to the question, we assign an integer
        // between 0 and questions length, which is not occupied yet.
        newQuestion.key = (this.incrementalPtr++).toString();
        this.questions.push(newQuestion);
      });
    }
  }

  addQuestion() {
    const newQuestion = this.constructQuestion(this.templateOption);
    // Need assign new key to the question, we assign an integer
    // between 0 and questions length, which is not occupied yet.
    newQuestion.key = (this.incrementalPtr++).toString();
    this.questions.push(newQuestion);
  }

  private constructQuestion(option: any): QuestionBase<any> {
    switch (option.controlType) {
      case 'input': {
        return new InputQuestion(option);
      }
      case 'checkbox': {
        return new CheckboxQuestion(option);
      }
      case 'select': {
        return new SelectQuestion(option);
      }
      case 'vertex': {
        return new InputVertexQuestion(option);
      }
      case 'keyValue': {
        return new KeyValueQuestion(option);
      }
      case 'udt': {
        return new UdtQuestion(option);
      }
    }
    return new QuestionBase(option);
  }
}
